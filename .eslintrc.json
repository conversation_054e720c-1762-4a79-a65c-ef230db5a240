{"extends": ["@electron-toolkit/eslint-config-ts", "@electron-toolkit/eslint-config-prettier"], "plugins": ["@typescript-eslint", "react"], "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": ["warn", {"vars": "all", "args": "after-used", "ignoreRestSiblings": true, "argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "no-unused-vars": "off", "react/no-children-prop": "off", "semi": ["error", "always"], "indent": ["error", 4], "quotes": ["error", "single"], "@typescript-eslint/no-empty-function": "warn", "@typescript-eslint/ban-ts-comment": "warn", "no-console": "warn"}, "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}}