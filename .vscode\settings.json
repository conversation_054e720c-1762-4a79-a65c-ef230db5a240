{
    "terminal.integrated.cursorBlinking": true,

    // TypeScript 配置
    "typescript.preferences.includePackageJsonAutoImports": "on",
    "typescript.preferences.importModuleSpecifier": "relative",
    "javascript.preferences.importModuleSpecifier": "relative",

    // 代码片段配置
    "editor.snippetSuggestions": "top",

    "terminal.integrated.defaultProfile.windows": "Command Prompt",
    "terminal.integrated.profiles.windows": {
        "Command Prompt": {
            "path": "C:\\Windows\\System32\\cmd.exe",
            "args": ["/k", "chcp 65001"]
        },
        "PowerShell": {
            "source": "PowerShell",
            "args": ["-NoExit", "-Command", "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8"]
        }
    },
    "terminal.integrated.env.windows": {
        "PYTHONIOENCODING": "utf-8"
    }
}
