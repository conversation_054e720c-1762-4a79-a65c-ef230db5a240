# KHB ELISA ST SERIAL INSTRUMEN CONTRON SYSTEM SOFTWARE`<br>`(科华酶免ST系列仪器控制系统软件)

---

## 一、项目信息

- 项目名称：ST_ELISA_Project
- 版本号：【v1.0.0010】
- 项目构建：[![Electron Version](https://img.shields.io/badge/electron-<version>-brightgreen.svg)](https://electronjs.org)
- 项目许可：[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
- 项目地址：[Git 项目链接](http://*************:8001/skh10/st_elisa_project.git '地址')

## 二、项目简介

- 基于 Electron + React 开发的桌面控制系统软件，用于KHB酶标仪的管理和控制，适配ST-360/960/980等型号酶标仪
- 核心功能
  1、 酶标仪数据采集
  2、 数据实时显示和存储
  3、 历史数据查询
  4、 数据导出功能
  5、 设备参数配置
- ![图片]()

## 三、环境要求

- 运行环境

    - Electron: `v33.3.1`

        ```markdown{.line-numbers}
        Electron是一个使用 JavaScript、HTML 和 CSS 构建桌面应用程序的框架。
        Electron 将 Chromium 和 Node.js 嵌入到了一个二进制文件中，
        因此它允许你仅需一个代码仓库，就可以撰写支持 Windows、macOS 和 Linux 的跨平台应用。

        要开发 Electron 应用，您需要安装 Node.js 运行环境和它的包管理器 npm。 我们推荐安装最新的长期支持 (LTS) 版本
        ```

    - Node.js : `22.13.0 LTS`
    - npm : `10.9.2`

        ```bash{.line-numbers}
        #查看运行环境和包管理器版本
        node -v
        npm -v
        ```

    - 操作系统：`(>= windows10)`

        ```markdown{.line-numbers}
        # Windows系统支持情况
        Windows 7：从 Electron 10 版本开始，Electron 就不再支持 Windows 7 和更早的操作系统。
        因此，如果您的应用需要支持 Windows 7，建议选择 Electron 9.x 或更早版本
        Windows 10：所有版本的 Electron 都支持 Windows 10，特别是 10.x 到 23.x 版本
        Windows 11：从 Electron 12 开始，Electron 完全支持 Windows 11
        ```

## 四、技术栈

- 前端框架：`React 18` + `TypeScript`
- UI组件库：`Chakra UI`
- 数据库：`SQLite3`
- 开发工具：`electron-vite` [在线手册](https://cn.electron-vite.org/guide/introduction '网址')
- 打包工具：`electron-builder`
- 串口通信：`serialport`

## 五、开发环境

- 5.1、克隆项目

    ```bash
    git clone http://*************:8001/skh10/st_elisa_project.git
    cd st-elisa-project

    # 验证安装
    node -v
    npm -v

    # 启动开发服务器
    npm run dev

    # scripts:
    - dev:react: 启动React开发服务器
    - dev:electron: 启动Electron应用
    - build: 构建应用
    - dist:win/mac/linux: 打包对应平台的应用
    ```

- 5.2 安装依赖

    ```bash
    # 安装 Node.js 和 npm
    # Windows/macOS：直接下载安装包运行安装（.msi 或 .pkg）
    # Linux：可以使用包管理器安装
    # 按照向导完成安装（默认会包含 npm），打开终端，检查版本

    # 验证安装
    node -v
    npm -v

    # 配置升级NPM
    npm install -g npm@latest

    # 设置国内镜像（加速下载）
    npm config set registry https://registry.npmmirror.com


    #全局安装 Electron
    npm install -g electron --save-dev

    #验证安装
    electron --version

    #使用vite创建项目
    npm create vite

    #设置交叉编译依赖功能
    npm i --save-dev electron-builder
    npm i --save-dev cross-env
    npm i --save-dev npm-run-all

    # 安装数据库工具集prisma
    npm install prisma --save-dev
    npx prisma init --datasource-provider sqlite

    # 安装数据库相关依赖
    npm install sqlite3 better-sqlite3

    # 安装UI组件库
    npm install @chakra-ui/react@3.8.1 @emotion/react @emotion/styled framer-motion
    npm install @chakra-ui/icons
    npm install react-router-dom

    # 安装串口通信组件库
    npm install serialport

    # 安装其他工具依赖
    npm install dayjs lodash
    ```

- 5.3 数据库开发工具 （prisma）

    ```bash
    #Prisma 是一个现代化的 数据库工具链（Database Toolkit），旨在简化开发者与数据库的交互流程。
    它提供了类型安全、直观的 API，并支持多种数据库系统（如 PostgreSQL、MySQL、SQLite、SQL Server 等）

    # Prisma Client
    # 自动生成的、类型安全的数据库客户端，用于在 Node.js 或 TypeScript 中直接操作数据库。
    # 类似 ORM（如 Sequelize、TypeORM），但更轻量且专注于类型安全。

    # 安装插件
    npm install prisma --save-dev

    # 数据库配置文件
    .env文件

    # Prisma Migrate 数据库迁移工具，通过代码定义数据模型（Schema），并自动生成迁移文件，替代手动编写 SQL 迁移脚本。

    # 首次初始化数据库
    npx prisma migrate dev --name init

    # 添加新表或修改结构
    npx prisma migrate dev --name add_filters_table

    # Prisma Studio 图形化界面（GUI）用于查看和编辑数据库中的数据。
    npx prisma studio

    # Prisma Schema
    #使用声明式语言（schema.prisma 文件）定义数据模型、关系和数据库连接。
    ```

- 5.4 代码格式化工具 (prettier)

    ```bash
    Prettier 是一个流行的 代码格式化工具，支持 JavaScript、TypeScript、CSS、HTML、JSON 等多种语言。
    它可以与 ESLint 配合使用，确保代码风格一致。

    # 全局安装（不推荐）：
    npm install -g prettier

    # 安装 Prettier （在项目根目录运行）
    npm install --save-dev prettier

    # 格式定义文件
    .prettierrc.yaml

    # 格式化文件
    npx prettier --write .

    # Prettier 可以和 ESLint 配合使用，避免规则冲突
    npm install --save-dev eslint-config-prettier eslint-plugin-prettier

    eslint-config-prettier：关闭 ESLint 中与 Prettier 冲突的规则
    eslint-plugin-prettier：以 ESLint 插件的方式运行 Prettier
    ```

- 5.5 代码质量管理工具 (ESLint)

    ```bash
    ESLint 是一个用于 JavaScript/TypeScript 代码的静态检查工具，可以帮助你规范代码风格、发现潜在错误。

    # 安装 ESLint （不推荐全局安装）
    npm install -g eslint

    # 项目内安装（推荐）
    # 进入你的项目目录，运行：
    npm init @eslint/config

    # 或手动安装
    npm install eslint --save-dev

    # 配置文件
    eslint.config.mjs

    # 检查错误
    npm run lint

    # 自动修复
    npm run lint:fix
    ```

## 六、项目结构

```bash
  ├── electron                   # Electron 主进程源码
  │   ├── main.ts                # 主进程入口
  │   └── preload.ts             # 预加载脚本
  ├── src                        # React 渲染进程源码
  │   ├── components             # 组件目录
  │   ├── pages                  # 页面目录
  │   ├── database               # 数据库相关
  │   │   └── index.ts           # 数据库配置
  ├── utils                      # 工具函数
  │   ├── App.tsx                # 应用入口组件
  │   └── main.tsx               # 渲染进程入口
  ├── electron-builder.json5     # electron-builder配置
  └── package.json               # 项目配置文件
```

## 七、 打包与发布

```bash
  # Windows版本
  npm run build:win

  # linux 版本
  npm run build:linux

  # MacOS版本
  npm run build:mac
```

## 八、 常见问题（FAQ）

```bash
❓ 如何解决 "Electron failed to install"?
确保使用正确的 Node.js 版本，并清理缓存：

npm cache clean --force
rm -rf node_modules
npm install


npm cache clean --force
rimraf node_modules package-lock.json


```

## 项目创建步骤

一、 使用electron-vite模板创建项目

```bash
# npm 7+
npm create @quick-start/electron@latest
npm install
npm run dev

# 选择以下选项：
# Project name: st-elisa-project
# Select a framework: React
# Add TypeScript: Yes
# Add Electron updater: Yes
# Enable Electron download mirror proxy: Yes
# Package name: st-elisa-project
```

## 项目目录

```bash
├── src
│   ├── main
│   ├── preload
│   ├── renderer
│   ├── index.html
├── package.json
├── vite.config.ts
```

## React + TypeScript + Vite

This template provides a minimal setup to get React working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default tseslint.config({
    languageOptions: {
        // other options...
        parserOptions: {
            project: ['./tsconfig.node.json', './tsconfig.app.json'],
            tsconfigRootDir: import.meta.dirname
        }
    }
});
```

- Replace `tseslint.configs.recommended` to `tseslint.configs.recommendedTypeChecked` or `tseslint.configs.strictTypeChecked`
- Optionally add `...tseslint.configs.stylisticTypeChecked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and update the config:

```js
// eslint.config.js
import react from 'eslint-plugin-react';

export default tseslint.config({
    // Set the react version
    settings: { react: { version: '18.3' } },
    plugins: {
        // Add the react plugin
        react
    },
    rules: {
        // other rules...
        // Enable its recommended rules
        ...react.configs.recommended.rules,
        ...react.configs['jsx-runtime'].rules
    }
});
```
