<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1753775831198" clover="3.2.0">
  <project timestamp="1753775831199" name="All files">
    <metrics statements="235" coveredstatements="127" conditionals="130" coveredconditionals="57" methods="46" coveredmethods="29" elements="411" coveredelements="213" complexity="0" loc="235" ncloc="235" packages="1" files="2" classes="2"/>
    <file name="elisaFittingEngine.ts" path="E:\2-ELISA\1-1-KHB_ST_Software\software\st_elisa_project\src\renderer\src\utils\elisaFittingEngine.ts">
      <metrics statements="192" coveredstatements="124" conditionals="98" coveredconditionals="57" methods="40" coveredmethods="29"/>
      <line num="1" count="1" type="stmt"/>
      <line num="2" count="1" type="stmt"/>
      <line num="3" count="1" type="stmt"/>
      <line num="4" count="1" type="stmt"/>
      <line num="6" count="1" type="stmt"/>
      <line num="8" count="1" type="stmt"/>
      <line num="13" count="21" type="stmt"/>
      <line num="16" count="21" type="cond" truecount="2" falsecount="0"/>
      <line num="17" count="1" type="stmt"/>
      <line num="20" count="20" type="cond" truecount="2" falsecount="0"/>
      <line num="21" count="1" type="stmt"/>
      <line num="27" count="19" type="cond" truecount="5" falsecount="3"/>
      <line num="29" count="4" type="stmt"/>
      <line num="30" count="4" type="stmt"/>
      <line num="32" count="2" type="stmt"/>
      <line num="33" count="2" type="stmt"/>
      <line num="35" count="0" type="stmt"/>
      <line num="36" count="0" type="stmt"/>
      <line num="38" count="4" type="stmt"/>
      <line num="39" count="4" type="stmt"/>
      <line num="41" count="3" type="stmt"/>
      <line num="42" count="3" type="stmt"/>
      <line num="44" count="6" type="stmt"/>
      <line num="45" count="6" type="stmt"/>
      <line num="47" count="0" type="stmt"/>
      <line num="48" count="0" type="stmt"/>
      <line num="50" count="0" type="stmt"/>
      <line num="54" count="19" type="stmt"/>
      <line num="56" count="19" type="stmt"/>
      <line num="63" count="4" type="stmt"/>
      <line num="65" count="4" type="stmt"/>
      <line num="68" count="4" type="stmt"/>
      <line num="69" count="4" type="stmt"/>
      <line num="72" count="25" type="stmt"/>
      <line num="73" count="4" type="stmt"/>
      <line num="76" count="4" type="stmt"/>
      <line num="79" count="4" type="stmt"/>
      <line num="81" count="4" type="stmt"/>
      <line num="94" count="0" type="stmt"/>
      <line num="97" count="0" type="stmt"/>
      <line num="111" count="2" type="stmt"/>
      <line num="112" count="2" type="cond" truecount="1" falsecount="1"/>
      <line num="113" count="2" type="stmt"/>
      <line num="116" count="29" type="stmt"/>
      <line num="119" count="2" type="stmt"/>
      <line num="120" count="1" type="stmt"/>
      <line num="121" count="1" type="stmt"/>
      <line num="122" count="1" type="stmt"/>
      <line num="124" count="1" type="stmt"/>
      <line num="125" count="7" type="stmt"/>
      <line num="126" count="7" type="stmt"/>
      <line num="128" count="7" type="cond" truecount="2" falsecount="0"/>
      <line num="129" count="1" type="stmt"/>
      <line num="133" count="6" type="stmt"/>
      <line num="134" count="6" type="stmt"/>
      <line num="137" count="6" type="cond" truecount="1" falsecount="1"/>
      <line num="138" count="0" type="stmt"/>
      <line num="142" count="6" type="stmt"/>
      <line num="146" count="0" type="stmt"/>
      <line num="147" count="0" type="stmt"/>
      <line num="148" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="152" count="2" type="stmt"/>
      <line num="155" count="2" type="stmt"/>
      <line num="156" count="2" type="stmt"/>
      <line num="158" count="6" type="cond" truecount="2" falsecount="0"/>
      <line num="159" count="4" type="cond" truecount="3" falsecount="1"/>
      <line num="160" count="2" type="cond" truecount="1" falsecount="1"/>
      <line num="165" count="2" type="stmt"/>
      <line num="167" count="2" type="stmt"/>
      <line num="180" count="0" type="stmt"/>
      <line num="183" count="0" type="stmt"/>
      <line num="193" count="0" type="stmt"/>
      <line num="195" count="0" type="stmt"/>
      <line num="196" count="0" type="stmt"/>
      <line num="197" count="0" type="stmt"/>
      <line num="201" count="0" type="stmt"/>
      <line num="204" count="0" type="stmt"/>
      <line num="205" count="0" type="stmt"/>
      <line num="206" count="0" type="stmt"/>
      <line num="209" count="0" type="stmt"/>
      <line num="211" count="0" type="stmt"/>
      <line num="215" count="0" type="stmt"/>
      <line num="216" count="0" type="stmt"/>
      <line num="227" count="0" type="stmt"/>
      <line num="230" count="0" type="stmt"/>
      <line num="244" count="4" type="stmt"/>
      <line num="245" count="4" type="stmt"/>
      <line num="246" count="4" type="stmt"/>
      <line num="248" count="4" type="stmt"/>
      <line num="255" count="20031" type="stmt"/>
      <line num="256" count="120051" type="stmt"/>
      <line num="257" count="120051" type="stmt"/>
      <line num="260" count="4" type="stmt"/>
      <line num="261" count="4" type="stmt"/>
      <line num="262" count="4" type="stmt"/>
      <line num="264" count="27" type="stmt"/>
      <line num="265" count="4" type="stmt"/>
      <line num="266" count="1" type="cond" truecount="3" falsecount="1"/>
      <line num="267" count="1" type="stmt"/>
      <line num="270" count="4" type="stmt"/>
      <line num="272" count="4" type="stmt"/>
      <line num="285" count="0" type="stmt"/>
      <line num="288" count="0" type="stmt"/>
      <line num="302" count="3" type="stmt"/>
      <line num="305" count="3" type="stmt"/>
      <line num="306" count="3" type="stmt"/>
      <line num="307" count="3" type="stmt"/>
      <line num="309" count="3" type="stmt"/>
      <line num="316" count="18023" type="stmt"/>
      <line num="317" count="108038" type="stmt"/>
      <line num="318" count="108038" type="stmt"/>
      <line num="321" count="3" type="stmt"/>
      <line num="322" count="3" type="stmt"/>
      <line num="323" count="3" type="stmt"/>
      <line num="325" count="20" type="stmt"/>
      <line num="326" count="3" type="stmt"/>
      <line num="327" count="2" type="cond" truecount="3" falsecount="1"/>
      <line num="328" count="0" type="stmt"/>
      <line num="331" count="3" type="stmt"/>
      <line num="333" count="3" type="stmt"/>
      <line num="346" count="0" type="stmt"/>
      <line num="349" count="0" type="stmt"/>
      <line num="363" count="6" type="stmt"/>
      <line num="365" count="6" type="stmt"/>
      <line num="366" count="6" type="stmt"/>
      <line num="367" count="6" type="stmt"/>
      <line num="369" count="6" type="stmt"/>
      <line num="376" count="42059" type="stmt"/>
      <line num="377" count="336101" type="stmt"/>
      <line num="378" count="336101" type="stmt"/>
      <line num="381" count="6" type="stmt"/>
      <line num="382" count="6" type="stmt"/>
      <line num="383" count="6" type="stmt"/>
      <line num="385" count="53" type="stmt"/>
      <line num="386" count="6" type="stmt"/>
      <line num="387" count="1" type="cond" truecount="3" falsecount="1"/>
      <line num="388" count="1" type="stmt"/>
      <line num="391" count="6" type="stmt"/>
      <line num="393" count="6" type="stmt"/>
      <line num="406" count="0" type="stmt"/>
      <line num="409" count="0" type="stmt"/>
      <line num="419" count="0" type="stmt"/>
      <line num="421" count="0" type="stmt"/>
      <line num="422" count="0" type="stmt"/>
      <line num="428" count="0" type="stmt"/>
      <line num="429" count="0" type="stmt"/>
      <line num="430" count="0" type="stmt"/>
      <line num="432" count="0" type="stmt"/>
      <line num="437" count="0" type="stmt"/>
      <line num="438" count="0" type="stmt"/>
      <line num="439" count="0" type="stmt"/>
      <line num="442" count="0" type="stmt"/>
      <line num="443" count="0" type="stmt"/>
      <line num="444" count="0" type="stmt"/>
      <line num="445" count="0" type="stmt"/>
      <line num="447" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="448" count="0" type="stmt"/>
      <line num="452" count="0" type="stmt"/>
      <line num="453" count="0" type="stmt"/>
      <line num="454" count="0" type="stmt"/>
      <line num="456" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="457" count="0" type="stmt"/>
      <line num="458" count="0" type="stmt"/>
      <line num="460" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="461" count="0" type="stmt"/>
      <line num="464" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="465" count="0" type="stmt"/>
      <line num="467" count="0" type="stmt"/>
      <line num="470" count="0" type="stmt"/>
      <line num="473" count="0" type="stmt"/>
      <line num="477" count="0" type="stmt"/>
      <line num="478" count="0" type="stmt"/>
      <line num="480" count="0" type="stmt"/>
      <line num="493" count="0" type="stmt"/>
      <line num="496" count="0" type="stmt"/>
      <line num="506" count="13" type="stmt"/>
      <line num="507" count="13" type="stmt"/>
      <line num="508" count="90" type="stmt"/>
      <line num="510" count="13" type="stmt"/>
      <line num="511" count="90" type="stmt"/>
      <line num="514" count="13" type="stmt"/>
      <line num="521" count="124" type="stmt"/>
      <line num="531" count="19" type="stmt"/>
      <line num="534" count="19" type="cond" truecount="2" falsecount="0"/>
      <line num="535" count="5" type="stmt"/>
      <line num="539" count="19" type="cond" truecount="2" falsecount="0"/>
      <line num="540" count="13" type="stmt"/>
      <line num="544" count="19" type="stmt"/>
      <line num="545" count="19" type="stmt"/>
      <line num="546" count="19" type="cond" truecount="2" falsecount="0"/>
      <line num="547" count="7" type="stmt"/>
      <line num="550" count="19" type="stmt"/>
    </file>
    <file name="logger.ts" path="E:\2-ELISA\1-1-KHB_ST_Software\software\st_elisa_project\src\renderer\src\utils\logger.ts">
      <metrics statements="43" coveredstatements="3" conditionals="32" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <line num="4" count="1" type="stmt"/>
      <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="6" count="0" type="stmt"/>
      <line num="15" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="16" count="0" type="stmt"/>
      <line num="18" count="0" type="stmt"/>
      <line num="19" count="0" type="stmt"/>
      <line num="22" count="0" type="stmt"/>
      <line num="29" count="0" type="stmt"/>
      <line num="32" count="1" type="stmt"/>
      <line num="35" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="36" count="0" type="stmt"/>
      <line num="37" count="0" type="stmt"/>
      <line num="40" count="0" type="stmt"/>
      <line num="41" count="0" type="stmt"/>
      <line num="47" count="0" type="stmt"/>
      <line num="52" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="53" count="0" type="stmt"/>
      <line num="54" count="0" type="stmt"/>
      <line num="57" count="0" type="stmt"/>
      <line num="58" count="0" type="stmt"/>
      <line num="64" count="0" type="stmt"/>
      <line num="69" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="70" count="0" type="stmt"/>
      <line num="71" count="0" type="stmt"/>
      <line num="74" count="0" type="stmt"/>
      <line num="75" count="0" type="stmt"/>
      <line num="81" count="0" type="stmt"/>
      <line num="86" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="87" count="0" type="stmt"/>
      <line num="88" count="0" type="stmt"/>
      <line num="91" count="0" type="stmt"/>
      <line num="92" count="0" type="stmt"/>
      <line num="99" count="0" type="stmt"/>
      <line num="104" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="106" count="0" type="stmt"/>
      <line num="108" count="0" type="stmt"/>
      <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="112" count="0" type="stmt"/>
      <line num="113" count="0" type="stmt"/>
      <line num="119" count="0" type="stmt"/>
      <line num="125" count="1" type="stmt"/>
    </file>
  </project>
</coverage>
