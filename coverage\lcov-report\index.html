<!doctype html>
<html lang="en">
    <head>
        <title>Code coverage report for All files</title>
        <meta charset="utf-8" />
        <link rel="stylesheet" href="prettify.css" />
        <link rel="stylesheet" href="base.css" />
        <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <style type="text/css">
            .coverage-summary .sorter {
                background-image: url(sort-arrow-sprite.png);
            }
        </style>
    </head>

    <body>
        <div class="wrapper">
            <div class="pad1">
                <h1>All files</h1>
                <div class="clearfix">
                    <div class="fl pad1y space-right2">
                        <span class="strong">55.95% </span>
                        <span class="quiet">Statements</span>
                        <span class="fraction">141/252</span>
                    </div>

                    <div class="fl pad1y space-right2">
                        <span class="strong">43.84% </span>
                        <span class="quiet">Branches</span>
                        <span class="fraction">57/130</span>
                    </div>

                    <div class="fl pad1y space-right2">
                        <span class="strong">63.04% </span>
                        <span class="quiet">Functions</span>
                        <span class="fraction">29/46</span>
                    </div>

                    <div class="fl pad1y space-right2">
                        <span class="strong">54.04% </span>
                        <span class="quiet">Lines</span>
                        <span class="fraction">127/235</span>
                    </div>
                </div>
                <p class="quiet">Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.</p>
                <template id="filterTemplate">
                    <div class="quiet">
                        Filter:
                        <input type="search" id="fileSearch" />
                    </div>
                </template>
            </div>
            <div class="status-line medium"></div>
            <div class="pad1">
                <table class="coverage-summary">
                    <thead>
                        <tr>
                            <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
                            <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
                            <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
                            <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
                            <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
                            <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
                            <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
                            <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
                            <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
                            <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="file medium" data-value="elisaFittingEngine.ts">
                                <a href="elisaFittingEngine.ts.html">elisaFittingEngine.ts</a>
                            </td>
                            <td data-value="66.02" class="pic medium">
                                <div class="chart">
                                    <div class="cover-fill" style="width: 66%"></div>
                                    <div class="cover-empty" style="width: 34%"></div>
                                </div>
                            </td>
                            <td data-value="66.02" class="pct medium">66.02%</td>
                            <td data-value="209" class="abs medium">138/209</td>
                            <td data-value="58.16" class="pct medium">58.16%</td>
                            <td data-value="98" class="abs medium">57/98</td>
                            <td data-value="72.5" class="pct medium">72.5%</td>
                            <td data-value="40" class="abs medium">29/40</td>
                            <td data-value="64.58" class="pct medium">64.58%</td>
                            <td data-value="192" class="abs medium">124/192</td>
                        </tr>

                        <tr>
                            <td class="file low" data-value="logger.ts"><a href="logger.ts.html">logger.ts</a></td>
                            <td data-value="6.97" class="pic low">
                                <div class="chart">
                                    <div class="cover-fill" style="width: 6%"></div>
                                    <div class="cover-empty" style="width: 94%"></div>
                                </div>
                            </td>
                            <td data-value="6.97" class="pct low">6.97%</td>
                            <td data-value="43" class="abs low">3/43</td>
                            <td data-value="0" class="pct low">0%</td>
                            <td data-value="32" class="abs low">0/32</td>
                            <td data-value="0" class="pct low">0%</td>
                            <td data-value="6" class="abs low">0/6</td>
                            <td data-value="6.97" class="pct low">6.97%</td>
                            <td data-value="43" class="abs low">3/43</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="push"></div>
            <!-- for sticky footer -->
        </div>
        <!-- /wrapper -->
        <div class="footer quiet pad2 space-top1 center small">
            Code coverage generated by
            <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
            at 2025-07-29T07:57:10.999Z
        </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
