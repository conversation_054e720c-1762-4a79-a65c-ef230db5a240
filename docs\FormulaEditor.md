# 公式编辑器使用说明

## 概述

这是一个基于 `mathjs` 和 `Chakra UI` 的公式编辑器组件，专门为 ELISA 检测项目设计，支持复杂的数学运算、函数和逻辑运算。

## 功能特性

### 1. 支持的运算类型

#### 数学运算
- 基本运算：`+`、`-`、`*`、`/`
- 括号：`(`、`)`
- 幂运算：`POW(x, y)`
- 平方根：`SQRT(x)`
- 绝对值：`ABS(x)`
- 四舍五入：`ROUND(x, decimals)`

#### 统计函数
- 平均值：`AVERAGE(x, y, z)`
- 最大值：`MAX(x, y, z)`
- 最小值：`MIN(x, y, z)`
- 求和：`SUM(x, y, z)`

#### 逻辑运算
- 比较：`<`、`>`、`<=`、`>=`、`=`、`!=`
- 逻辑与：`AND`
- 逻辑或：`OR`
- 逻辑非：`NOT`
- 三元运算符：`condition ? value1 : value2`

### 2. 预定义变量

#### 空白对照
- `[Blank]` - 空白对照值

#### 阴性对照
- `[NC]` - 阴性对照值
- `[NC-1]` - 阴性对照1
- `[NC-2]` - 阴性对照2
- `[NC-3]` - 阴性对照3

#### 阳性对照
- `[PC]` - 阳性对照值
- `[PC-1]` - 阳性对照1
- `[PC-2]` - 阳性对照2
- `[PC-3]` - 阳性对照3

#### 样本
- `[DUP-SAMP-1]` - 重复样本1
- `[DUP-SAMP-2]` - 重复样本2
- `[SAMP-1]` - 样本1
- `[SAMP-2]` - 样本2

#### 参考值
- `[NEG]` - 阴性参考值
- `[POS]` - 阳性参考值
- `[CUTOFF]` - Cutoff值

## 使用示例

### 1. 基本使用

```tsx
import { FormulaEditor } from '@renderer/components/FormulaEditor';

const MyComponent = () => {
    const [formula, setFormula] = useState('');
    
    return (
        <FormulaEditor
            value={formula}
            onChange={setFormula}
            label="公式"
            placeholder="请输入公式..."
        />
    );
};
```

### 2. 带测试数据的使用

```tsx
const testData = {
    'NC-1': 0.05,
    'NC-2': 0.06,
    'NC-3': 0.04,
    'PC-1': 0.8,
    'PC-2': 0.85
};

<FormulaEditor
    value={formula}
    onChange={setFormula}
    testData={testData}
    label="Cutoff公式"
    placeholder="例如: MAX(NC-1, NC-2, NC-3) / 2 - 0.05"
/>
```

### 3. 在项目管理中使用

```tsx
// 在 Project 接口中添加公式字段
export interface Project {
    // ... 其他字段
    cutOffFormula: string;
    resultFormula: string;
}

// 在表单中使用
<Box>
    <Text fontWeight="bold" fontSize="lg" mb={2} color="teal.600">
        公式设置
    </Text>
    
    <VStack align="stretch" spacing={4}>
        <FormulaEditor
            label="Cutoff公式"
            value={editProject.cutOffFormula || ''}
            onChange={(formula) => handleEditChange('cutOffFormula', formula)}
            placeholder="例如: MAX(NC-1, NC-2, NC-3) / 2 - 0.05"
            testData={testData}
        />
        
        <FormulaEditor
            label="结果判断公式"
            value={editProject.resultFormula || ''}
            onChange={(formula) => handleEditChange('resultFormula', formula)}
            placeholder="例如: [PC-1] > 0.8 AND [PC-2] > 0.8 AND [NC] <= 0.2"
            testData={testData}
        />
    </VStack>
</Box>
```

## 常用公式示例

### 1. Cutoff 计算
```javascript
// 基于阴性对照最大值计算
MAX(NC-1, NC-2, NC-3) / 2 - 0.05

// 条件计算
AVERAGE(PC-1, PC-2) < 0.5 ? AVERAGE(PC-1, PC-2) : 0.5

// 加权计算
([PC-1] * 0.1 + [PC-2] * 0.25) / 2
```

### 2. 质量控制
```javascript
// 检查阳性对照和阴性对照是否在合理范围
[PC-1] > 0.8 AND [PC-2] > 0.8 AND [NC] <= 0.2

// 空白校正检查
[Blank] <= AVERAGE(NC-1, NC-2, NC-3)
```

### 3. 重复性检查
```javascript
// 检查重复样本的一致性
([DUP-SAMP-1] = [NEG] AND [DUP-SAMP-2] = [NEG]) OR ([DUP-SAMP-1] = [POS] AND [DUP-SAMP-2] = [POS])
```

## 公式执行引擎

使用 `FormulaEngine` 类来执行公式：

```tsx
import { createFormulaEngine } from '@renderer/utils/formulaEngine';

const engine = createFormulaEngine(testData);
const result = engine.execute(formula);

if (result.success) {
    console.log('计算结果:', result.result);
} else {
    console.error('执行错误:', result.error);
}
```

## 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `value` | `string` | - | 公式内容 |
| `onChange` | `(formula: string) => void` | - | 公式变化回调 |
| `onValidate` | `(isValid: boolean, error?: string) => void` | - | 验证回调 |
| `placeholder` | `string` | `'请输入公式...'` | 占位符文本 |
| `label` | `string` | `'公式'` | 标签文本 |
| `isRequired` | `boolean` | `false` | 是否必填 |
| `testData` | `Record<string, number>` | `{}` | 测试数据 |
| `height` | `string` | `'120px'` | 编辑器高度 |
| `showExamples` | `boolean` | `true` | 是否显示示例 |

## 注意事项

1. **变量格式**：变量必须使用方括号包围，如 `[NC-1]`
2. **函数名**：函数名不区分大小写，但建议使用大写
3. **逻辑运算符**：使用 `AND`、`OR`、`NOT` 而不是 `&&`、`||`、`!`
4. **语法验证**：组件会实时验证公式语法，错误会显示在下方
5. **测试功能**：可以使用测试数据验证公式的执行结果

## 扩展功能

### 添加自定义变量
```tsx
// 在 FormulaEditor 组件中扩展 PREDEFINED_VARIABLES
const PREDEFINED_VARIABLES: FormulaVariable[] = [
    // ... 现有变量
    { name: '[CUSTOM]', type: 'custom', description: '自定义变量', example: '1.0', category: '自定义' }
];
```

### 添加自定义函数
```tsx
// 在公式引擎中添加自定义函数
const engine = new FormulaEngine({
    variables: testData,
    functions: {
        customFunction: (x: number) => x * 2
    }
});
``` 