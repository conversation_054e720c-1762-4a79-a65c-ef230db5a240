# PDFMake打印方案说明

## 概述

本项目为ELISA检测软件集成了基于PDFMake的打印功能，提供模板化、国际化的PDF报告生成能力。

## 技术栈

- **PDFMake**: 强大的JavaScript PDF生成库
- **TypeScript**: 类型安全的开发体验
- **React**: 前端UI框架
- **Chakra UI**: 组件库
- **i18next**: 国际化支持

## 方案优势

### 1. 模板化设计

- 支持复杂的PDF模板结构
- 可自定义样式和布局
- 支持表格、图表、图片等元素

### 2. 国际化支持

- 动态语言切换（中文/英文）
- 翻译文本集中管理
- 支持多语言报告生成

### 3. 数据驱动

- 基于TestRecord数据结构
- 自动生成96孔板数据表格
- 智能统计检测结果

### 4. 跨平台兼容

- 在Electron环境中运行良好
- 支持浏览器环境
- 响应式设计

## 文件结构

```
src/renderer/src/
├── utils/
│   ├── pdfDemo.ts              # 简化的PDF演示
│   ├── pdfGeneratorSimple.ts   # 简化的PDF生成器
│   ├── pdfTemplatesNew.ts      # PDF模板定义
│   └── pdfFonts.ts             # 字体配置
├── components/
│   └── PdfPreviewModal.tsx     # PDF预览模态框
└── pages/
    └── 3_resultdata.tsx        # 结果数据页面（已集成）
```

## 核心功能

### 1. PDF生成

```typescript
// 生成PDF Buffer
const buffer = await generateElisaReport(testRecord, 'zh');

// 生成预览URL
const url = await generatePdfPreviewUrl(testRecord, 'zh');

// 下载PDF
await downloadPdf(testRecord, 'zh', 'report.pdf');

// 打印PDF
await printPdf(testRecord, 'zh');
```

### 2. 报告内容

- **基本信息**: 微孔板编号、项目名称、检测日期等
- **检测参数**: 温度、湿度、试剂批号、审核者等
- **96孔板数据**: 完整的孔位数据表格
- **结果统计**: 阳性率、样本数量等统计信息
- **签名区域**: 操作员和审核者签名

### 3. 国际化支持

```typescript
// 中文报告
const chineseReport = await generateElisaReport(testRecord, 'zh');

// 英文报告
const englishReport = await generateElisaReport(testRecord, 'en');
```

## 使用方法

### 1. 在结果数据页面使用

```typescript
// 点击"PDF预览"按钮
const handlePdfPreview = () => {
    if (!currentRecord) {
        toast({
            title: '请先选择一条记录',
            status: 'warning',
            duration: 2000,
            isClosable: true,
            position: 'top'
        });
        return;
    }
    onPdfPreviewOpen();
};
```

### 2. PDF预览模态框

- 支持语言切换（中文/英文）
- 实时PDF预览
- 打印和保存功能
- 响应式设计

### 3. 自定义模板

```typescript
// 创建自定义模板
const customTemplate = {
    pageSize: 'A4',
    content: [
        // 自定义内容
    ],
    styles: {
        // 自定义样式
    }
};
```

## 配置说明

### 1. 字体配置

```typescript
// 支持中文字体
export const pdfFonts = {
    MiSans: {
        normal: 'MiSans-Regular.ttf',
        bold: 'MiSans-Bold.ttf',
        italics: 'MiSans-Regular.ttf',
        bolditalics: 'MiSans-Bold.ttf'
    }
};
```

### 2. 翻译配置

```typescript
const translations = {
    zh: {
        reportTitle: 'ELISA检测报告',
        plateNumber: '微孔板编号'
        // ... 更多翻译
    },
    en: {
        reportTitle: 'ELISA Test Report',
        plateNumber: 'Plate Number'
        // ... 更多翻译
    }
};
```

## 扩展功能

### 1. 添加新的报告类型

- 创建新的模板函数
- 添加相应的翻译文本
- 集成到UI组件中

### 2. 自定义样式

- 修改字体、颜色、布局
- 添加公司Logo
- 自定义页眉页脚

### 3. 数据导出

- 支持Excel导出
- 支持CSV格式
- 支持批量导出

## 注意事项

### 1. 类型安全

- 使用TypeScript确保类型安全
- 避免any类型的使用
- 保持接口一致性

### 2. 性能优化

- 使用单例模式避免重复初始化
- 异步处理PDF生成
- 合理的内存管理

### 3. 错误处理

- 完善的错误捕获机制
- 用户友好的错误提示
- 降级处理方案

## 未来规划

### 1. 功能增强

- 支持更多报告模板
- 添加图表和可视化
- 支持批量报告生成

### 2. 性能优化

- 缓存机制
- 后台生成
- 压缩优化

### 3. 用户体验

- 拖拽式模板设计
- 实时预览编辑
- 模板库管理

## 总结

PDFMake方案为ELISA检测软件提供了强大、灵活的打印功能，支持模板化设计和国际化需求。通过模块化的架构设计，易于维护和扩展，为后续功能开发奠定了良好的基础。
