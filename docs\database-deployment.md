# 数据库部署路径配置

## 📍 部署后数据库文件位置

### **开发环境**

- 路径：项目根目录下的 `dev.db`
- 位置：`./dev.db`

\*\*\* 项目测试使用的是动态设置数据库路径的方式

- 设置环境变量：
  -- `$env:DATABASE_URL="file:./prisma/dev.db";`
- 添加字段 创建并应用迁移：
  -- `npx prisma migrate dev --name add_new_field`
- 生成 Prisma 客户端
  -- `npx prisma generate`
  -- `npx prisma migrate dev --name`
-

### **生产环境（打包后）**

数据库文件将自动存储在用户数据目录中：

#### Windows

```
C:\Users\<USER>\AppData\Roaming\{应用名称}\database\elisa.db
```

#### macOS

```
~/Library/Application Support/{应用名称}/database/elisa.db
```

#### Linux

```
~/.config/{应用名称}/database/elisa.db
```

## 🚀 技术实现

### **动态路径解析**

系统在启动时会根据环境自动选择数据库路径：

```typescript
function getDatabasePath(): string {
    if (process.env.NODE_ENV === 'development') {
        return 'file:./dev.db'; // 开发环境
    } else {
        // 生产环境：用户数据目录
        const userDataPath = app.getPath('userData');
        const dbDir = path.join(userDataPath, 'database');
        const dbPath = path.join(dbDir, 'elisa.db');
        return `file:${dbPath}`;
    }
}
```

### **自动目录创建**

如果目录不存在，系统会自动创建：

```typescript
if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
}
```

## ✅ 优势

1. **权限安全**：避免了应用安装目录的写入权限问题
2. **数据持久性**：应用更新时用户数据不会丢失
3. **跨平台兼容**：自动适配不同操作系统的标准数据目录
4. **环境隔离**：开发和生产环境使用不同的数据库文件

## 🔧 应用生命周期管理

### **启动时**

```typescript
// 在 app.whenReady() 中调用
await initializeDatabase();
```

### **退出时**

```typescript
// 在 app.on('before-quit') 中调用
await closeDatabase();
```

## 📝 注意事项

1. **首次运行**：应用首次运行时会自动创建数据库文件和表结构
2. **数据迁移**：如果需要在生产环境中运行数据库迁移，请谨慎处理
3. **备份建议**：重要数据建议定期备份到其他位置
4. **路径查看**：可以在应用日志中查看实际的数据库文件路径

## 🎯 首次安装自动创建机制

### **数据库文件自动创建**

- SQLite 数据库文件会在第一次连接时自动创建
- 无需手动创建 `.db` 文件

### **表结构自动创建**

应用启动时会执行以下检查：

```typescript
const isInitialized = await checkDatabaseInitialized();
if (!isInitialized) {
    console.log('首次运行，正在创建数据库表结构...');
    await createDatabaseSchema();
}
```

### **自动创建的表**

首次运行时会自动创建以下数据表：

1. **DeviceInfo** - 设备信息表
2. **FilterInfo** - 滤光片信息表
3. **AppSettings** - 应用设置表
4. **SerialConfig** - 串口配置表
5. **projects** - 项目管理表

### **创建过程**

1. 应用启动
2. 检查数据库连接
3. 查询 sqlite_master 表检查是否存在 Project 表
4. 如果表不存在，执行 CREATE TABLE 语句
5. 完整的表结构创建后，应用正常运行

### **安全保障**

- 使用 `CREATE TABLE IF NOT EXISTS` 确保不会重复创建
- 所有 SQL 语句都经过 Prisma 的安全处理
- 失败时会抛出异常，防止数据损坏

## 🛠 故障排除

如果遇到数据库相关问题：

1. **检查权限**：确保应用对用户数据目录有读写权限
2. **检查磁盘空间**：确保目标驱动器有足够空间
3. **查看日志**：检查控制台输出的数据库连接信息
4. **手动清理**：如需重置，可删除整个 database 目录
