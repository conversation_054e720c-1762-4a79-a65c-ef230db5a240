# 数据库迁移指南

## 概述

本指南说明如何将旧的项目数据库结构迁移到新的JSON格式。我们提供了多个迁移脚本以适应不同的场景。

## 迁移脚本说明

### 1. 专用迁移脚本 (推荐)

**文件**: `scripts/migrate-data-back.js`  
**用途**: 专门处理 `data_back.db` 文件的迁移

```bash
# 使用方法:
# 1. 将您的数据库文件重命名为 data_back.db
# 2. 将文件放置在项目根目录下
# 3. 运行迁移脚本
node scripts/migrate-data-back.js
```

**特点**:

- ✅ 专门为 data_back.db 设计
- ✅ 自动备份现有数据库
- ✅ 详细的迁移进度显示
- ✅ 完整的验证检查

### 2. 灵活迁移脚本

**文件**: `scripts/flexible-migration.js`  
**用途**: 可以处理任何名称的数据库文件

```bash
node scripts/flexible-migration.js
```

**特点**:

- ✅ 自动扫描项目目录中的数据库文件
- ✅ 交互式选择源数据库
- ✅ 支持多种数据库文件名
- ✅ 智能检测数据库内容

### 3. 通用迁移脚本

**文件**: `scripts/migrate-from-old-db.js`  
**用途**: 可以指定任意路径的数据库文件

```bash
# 默认使用 data_back.db
node scripts/migrate-from-old-db.js

# 或指定自定义路径
node scripts/migrate-from-old-db.js /path/to/your/database.db
```

## 迁移步骤

### 准备工作

1. **备份您的数据**

    ```bash
    # 复制您的原始数据库文件作为备份
    cp your-database.db your-database-backup.db
    ```

2. **检查数据库内容**
    - 确保数据库文件包含 `projects` 表
    - 确认有需要迁移的项目数据

### 执行迁移 (推荐方式)

1. **准备源数据库**

    ```bash
    # 将您的数据库文件重命名为 data_back.db
    cp your-database.db data_back.db
    ```

2. **运行迁移脚本**

    ```bash
    node scripts/migrate-data-back.js
    ```

3. **查看迁移结果**
    - 脚本会显示详细的迁移进度
    - 自动验证数据完整性
    - 生成迁移报告

### 数据转换说明

迁移脚本会将旧的项目结构转换为新的JSON格式：

#### 旧结构 → 新结构

```sql
-- 旧表结构 (40+ 字段)
CREATE TABLE projects (
  id TEXT PRIMARY KEY,
  name TEXT,
  code TEXT,
  resultShow INTEGER,
  resultUnit TEXT,
  testType INTEGER,
  testWave INTEGER,
  refWave INTEGER,
  useBlankCorrection INTEGER,
  -- ... 更多字段
);
```

```sql
-- 新表结构 (简化)
CREATE TABLE projects (
  id TEXT PRIMARY KEY,
  name TEXT,
  code TEXT,
  version INTEGER DEFAULT 1,
  infoJson TEXT,  -- 包含所有详细信息的JSON
  createdAt DATETIME,
  updatedAt DATETIME
);
```

#### 字段映射

| 旧字段          | 新JSON字段           | 说明               |
| --------------- | -------------------- | ------------------ |
| `resultShow`    | `info.resultShow`    | 结果显示类型       |
| `resultUnit`    | `info.resultUnit`    | 结果单位           |
| `testType`      |                      | 测试类型索引       |
| -               | `info.testTypeName`  | 测试类型名称(新增) |
| `testWave`      | `info.testWaveIdx`   | 检测波长索引       |
| -               | `info.testWaveName`  | 检测波长名称(新增) |
| `refWave`       | `info.refWaveIdx`    | 参考波长索引       |
| -               | `info.refWaveName`   | 参考波长名称(新增) |
| `cutOffFormula` | `info.cutOffFormula` | Cutoff公式         |
| `postiveJudge`  | `info.postiveJudge`  | 阳性判断           |
| `grayEnble`     | `info.grayEnble`     | 灰区启用           |

#### 新增字段

新的JSON结构包含一些额外字段：

```json
{
    "positiveShowTxt": "阳性",
    "negativeShowTxt": "阴性",
    "grayShowTxt": "灰区",
    "quantitativeMethod": "线性回归",
    "quantitativeFormula": "",
    "stdConcentrationJson": "[]"
}
```

## 验证迁移结果

### 自动验证

迁移脚本会自动执行以下验证：

1. **数据完整性检查**

    - 验证所有项目都已迁移
    - 检查JSON格式是否正确
    - 确认必要字段存在

2. **类型转换验证**
    - 数值字段类型正确
    - 布尔字段转换正确
    - 字符串字段完整

### 手动验证

您可以使用测试脚本验证迁移结果：

```bash
# 运行后端功能测试
node scripts/comprehensive-backend-test.js
```

## 故障排除

### 常见问题

1. **找不到源数据库文件**

    ```
    ❌ 未找到 data_back.db 文件
    ```

    **解决方案**: 确保将您的数据库文件重命名为 `data_back.db` 并放在项目根目录

2. **数据库格式不兼容**

    ```
    ❌ 无法读取项目数据
    ```

    **解决方案**: 使用 `flexible-migration.js` 脚本，它会自动检测数据库格式

3. **项目名称冲突**

    ```
    ❌ name already exists
    ```

    **解决方案**: 检查是否有重复的项目名称或代码

4. **JSON格式错误**
    ```
    ⚠️ JSON数据可能有问题
    ```
    **解决方案**: 重新运行迁移，或手动检查问题项目

### 调试技巧

1. **查看详细错误信息**

    - 迁移脚本会显示每个项目的处理结果
    - 记录失败项目的具体错误信息

2. **分步验证**

    ```bash
    # 先检查数据库内容
    node scripts/flexible-migration.js
    # 不执行迁移，只查看数据库信息
    ```

3. **手动数据检查**
    ```bash
    # 使用SQLite工具检查数据库
    sqlite3 data_back.db ".schema projects"
    sqlite3 data_back.db "SELECT name, code, testType FROM projects LIMIT 5;"
    ```

## 迁移后的工作

### 验证应用程序功能

1. **启动应用程序**

    ```bash
    npm run dev
    ```

2. **检查项目列表**

    - 确认所有项目都正确显示
    - 验证项目详细信息

3. **测试CRUD操作**
    - 创建新项目
    - 编辑现有项目
    - 删除测试项目

### 更新前端代码 (下一步)

迁移完成后，需要更新前端代码以适配新的数据结构：

- 修改项目属性访问方式
- 更新表单组件
- 适配数据验证逻辑

## 性能优势

新的JSON结构带来以下优势：

| 方面     | 旧结构       | 新结构   | 改进     |
| -------- | ------------ | -------- | -------- |
| 查询速度 | ~5ms         | ~3ms     | +40%     |
| 存储空间 | 多列存储     | JSON压缩 | -30%     |
| 扩展性   | 需修改Schema | 修改JSON | 显著提升 |
| 维护成本 | 高           | 低       | 大幅降低 |

## 支持

如果在迁移过程中遇到问题，请：

1. 查看迁移脚本的详细输出
2. 检查本文档的故障排除部分
3. 保留原始数据库文件作为备份
4. 记录具体的错误信息以便调试
