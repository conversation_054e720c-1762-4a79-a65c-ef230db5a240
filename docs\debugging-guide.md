# UI组件调试信息使用指南

## 概述

在ELISA项目中，我们使用统一的日志系统来记录调试信息，而不是直接使用 `console.log`。这样可以：
- 统一管理日志格式
- 支持不同环境下的日志级别控制
- 便于问题追踪和调试
- 支持日志文件持久化

## 日志系统架构

```
渲染进程 (React组件) 
    ↓ (通过IPC)
预加载进程 (preload)
    ↓ (通过IPC)
主进程 (main)
    ↓
Winston日志系统
    ↓
控制台输出 + 日志文件
```

## 在UI组件中使用日志

### 1. 导入logger

```typescript
import logger from '@renderer/utils/logger';
```

### 2. 日志级别

- `logger.debug()` - 调试信息，仅在开发环境显示
- `logger.info()` - 一般信息
- `logger.warn()` - 警告信息
- `logger.error()` - 错误信息
- `logger.log()` - 通用日志（开发环境为debug，生产环境为info）

### 3. 日志格式

```typescript
logger.debug('消息内容', {
    component: '组件名称',
    data: {
        // 相关数据
        key1: value1,
        key2: value2
    }
});
```

### 4. 最佳实践

#### 组件生命周期调试

```typescript
// 组件加载
React.useEffect(() => {
    logger.debug('组件已加载', {
        component: 'ComponentName',
        data: { props: props }
    });
}, []);

// 状态变化
React.useEffect(() => {
    logger.debug('状态已更新', {
        component: 'ComponentName',
        data: { 
            stateName: 'value',
            previousValue: prevValue 
        }
    });
}, [stateValue]);
```

#### 事件处理调试

```typescript
const handleClick = async () => {
    logger.debug('按钮点击事件', {
        component: 'ComponentName',
        data: {
            buttonId: 'submit-btn',
            formData: formData
        }
    });
    
    try {
        // 业务逻辑
        const result = await apiCall();
        
        logger.info('操作成功', {
            component: 'ComponentName',
            data: { result }
        });
    } catch (error) {
        logger.error('操作失败', error, {
            component: 'ComponentName',
            data: { formData }
        });
    }
};
```

#### 数据流调试

```typescript
// 数据接收
const handleDataReceived = (data) => {
    logger.debug('接收到数据', {
        component: 'ComponentName',
        data: {
            dataType: 'userList',
            count: data.length,
            sample: data.slice(0, 3) // 只记录前3条作为示例
        }
    });
};

// 数据过滤
const handleFilter = (filters) => {
    logger.debug('应用过滤器', {
        component: 'ComponentName',
        data: {
            filters,
            originalCount: originalData.length
        }
    });
    
    const filtered = applyFilters(originalData, filters);
    
    logger.debug('过滤结果', {
        component: 'ComponentName',
        data: {
            filteredCount: filtered.length,
            reduction: originalData.length - filtered.length
        }
    });
};
```

## 调试信息分类

### 1. 组件生命周期
- 组件挂载/卸载
- 状态变化
- 属性更新

### 2. 用户交互
- 按钮点击
- 表单提交
- 数据选择

### 3. 数据操作
- API调用
- 数据过滤
- 数据转换

### 4. 错误处理
- 异常捕获
- 错误恢复
- 降级处理

## 环境控制

### 开发环境
- 所有日志级别都会显示
- 调试信息会输出到控制台
- 日志文件包含详细信息

### 生产环境
- 只显示 info、warn、error 级别
- debug 级别被忽略
- 日志文件精简

## 注意事项

1. **避免敏感信息**：不要在日志中记录密码、token等敏感信息
2. **控制日志量**：避免在循环中大量记录日志
3. **结构化数据**：使用对象格式记录复杂数据
4. **组件标识**：始终包含component字段便于追踪
5. **性能考虑**：生产环境会自动过滤debug日志

## 示例：完整组件调试

```typescript
import React, { useState, useEffect } from 'react';
import logger from '@renderer/utils/logger';

const MyComponent: React.FC<Props> = (props) => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(false);

    // 组件加载调试
    useEffect(() => {
        logger.debug('MyComponent 已挂载', {
            component: 'MyComponent',
            data: { props }
        });
    }, []);

    // 数据变化调试
    useEffect(() => {
        if (data) {
            logger.debug('数据已更新', {
                component: 'MyComponent',
                data: {
                    dataType: typeof data,
                    hasData: !!data,
                    dataKeys: Object.keys(data || {})
                }
            });
        }
    }, [data]);

    const handleLoadData = async () => {
        logger.debug('开始加载数据', {
            component: 'MyComponent',
            data: { loading: true }
        });

        setLoading(true);
        
        try {
            const result = await fetchData();
            setData(result);
            
            logger.info('数据加载成功', {
                component: 'MyComponent',
                data: { 
                    resultCount: result?.length || 0 
                }
            });
        } catch (error) {
            logger.error('数据加载失败', error, {
                component: 'MyComponent'
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div>
            {/* 组件内容 */}
        </div>
    );
};
```

## 日志查看

### 开发环境
- 控制台直接查看
- 开发者工具 Console 面板

### 生产环境
- 日志文件位置：`%APPDATA%/st_elisa_project/logs/`
- `combined.log` - 所有日志
- `error.log` - 仅错误日志

## 故障排除

如果日志不显示，检查：
1. 是否正确导入 logger
2. 环境变量 NODE_ENV 设置
3. IPC 通道是否正常工作
4. 主进程日志处理器是否注册 