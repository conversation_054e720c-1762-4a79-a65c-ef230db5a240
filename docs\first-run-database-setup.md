# 首次安装数据库自动创建说明

## 🎯 您的疑问解答

**Q: 第一次安装软件，如果数据库不存在会自动创建吗？**

**A: 是的！完全自动创建，用户无需任何手动操作。**

## 🚀 自动创建机制

### **第一步：数据库文件创建**

```
✅ SQLite 数据库文件会在首次连接时自动创建
✅ 无需手动创建 .db 文件
✅ 自动选择合适的存储位置
```

### **第二步：表结构自动创建**

```typescript
// 应用启动时自动检查
const isInitialized = await checkDatabaseInitialized();
if (!isInitialized) {
    console.log('首次运行，正在创建数据库表结构...');
    await createDatabaseSchema();
}
```

### **第三步：完整的表结构**

首次运行会自动创建以下数据表：

| 表名                | 说明         | 用途                             |
| ------------------- | ------------ | -------------------------------- |
| **projects**        | 项目管理表   | 存储用户创建的项目信息和检测参数 |
| **plate_templates** | 酶标板模板表 | 存储酶标板布局模板和配置         |
| **test_records**    | 检测记录表   | 存储检测结果和历史数据           |

## 📋 首次运行流程

```
1. 用户启动应用
   ↓
2. 系统检查数据库路径
   ↓
3. 检查数据库文件是否存在
   ↓
4. 如果不存在，自动创建数据库文件
   ↓
5. 检查表结构是否存在
   ↓
6. 如果不存在，自动执行CREATE TABLE语句
   ↓
7. 数据库初始化完成，应用正常运行
```

## 🔒 安全保障

### **防重复创建**

```sql
CREATE TABLE IF NOT EXISTS "projects" (
    -- 使用 IF NOT EXISTS 确保不会重复创建
);

CREATE TABLE IF NOT EXISTS "plate_templates" (
    -- 酶标板模板表
);

CREATE TABLE IF NOT EXISTS "test_records" (
    -- 检测记录表
);
```

### **事务安全**

- 所有创建操作都在事务中执行
- 失败时自动回滚，不会损坏数据

### **错误处理**

```typescript
try {
    await createDatabaseSchema();
    console.log('数据库表结构创建成功');
} catch (error) {
    console.error('创建数据库表结构失败:', error);
    throw error; // 阻止应用启动，避免后续错误
}
```

## 📁 实际文件位置

### **开发环境**

```
项目根目录/dev.db
```

### **生产环境（用户安装后）**

**Windows:**

```
C:\Users\<USER>\AppData\Roaming\{应用名称}\database\elisa.db
```

**macOS:**

```
~/Library/Application Support/{应用名称}/database/elisa.db
```

**Linux:**

```
~/.config/{应用名称}/database/elisa.db
```

## 🧪 验证测试

我们提供了测试脚本来验证数据库初始化：

```bash
# 运行数据库初始化测试
npx ts-node src/main/database/test-init.ts
```

测试会验证：

- ✅ 数据库连接成功
- ✅ 表结构创建成功
- ✅ 数据插入和查询功能
- ✅ 清理和关闭功能

## 📝 总结

**用户体验：**

- 🎯 **零配置**：安装后直接运行，无需任何设置
- 🚀 **自动化**：所有数据库操作完全自动化
- 🔒 **安全可靠**：完整的错误处理和回滚机制
- 📊 **状态透明**：控制台显示详细的创建过程

**技术保障：**

- 使用 SQLite 的 `IF NOT EXISTS` 语法
- Prisma 提供的类型安全和SQL注入防护
- 完整的应用生命周期管理
- 跨平台路径自动适配
- 三张核心表：项目管理、模板配置、检测记录

**简单来说：用户只需要安装和运行应用，所有数据库相关的创建和配置都是全自动的！** 🎉
