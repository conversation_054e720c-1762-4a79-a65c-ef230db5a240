appId: 'com.skhb.st.elisa'
productName: 'KHB ST ELISA'
copyright: "Copyright © 2025 KHB(XI'AN) Bio-engineering Co., Ltd."

directories:
    buildResources: build
    output: dist

files:
    - '**/*'
    - '!.vscode/**/*'
    - '!docs/**/*'
    - '!src/**/*'
    - '!scripts/**/*'
    - '!dist/**/*'
    - '!*-lock.json'
    - '!electron.vite.config.{js,ts,mjs,cjs}'
    - '!eslint.config.mjs'
    - '!{.eslintignore,.eslintrc.json,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
    - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
    - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
    - '!**/node_modules/.cache/**/*'
    - '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}'
    - '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}'
    - '!**/node_modules/*.d.ts'
    - '!**/node_modules/.bin'
    - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
    - '!.editorconfig'

extraResources:
    - from: 'node_modules/.prisma'
      to: 'node_modules/.prisma'
      filter: '**/*'
    - from: 'node_modules/@prisma'
      to: 'node_modules/@prisma'
      filter: '**/*'
    - from: 'resources/config'
      to: 'config'
      filter: '**/*'

asar: true
asarUnpack:
    - 'node_modules/@prisma/**'
    - 'node_modules/@prisma/engines/**'
    - 'node_modules/.prisma/**'
    - 'resources/**/*'
    - 'config/**/*'

# 添加发布者信息
publish:
    provider: generic
    url: https://www.skhb.com/

# Windows 特定配置
win:
    executableName: khb_st_elisa_x64
    target:
        - target: nsis
          arch: [x64]
    requestedExecutionLevel: highestAvailable
    compression: store

nsis:
    oneClick: false
    allowToChangeInstallationDirectory: true
    artifactName: ${name}-${version}_x64-setup.${ext}
    shortcutName: ${productName}
    uninstallDisplayName: ${productName}-${version}
    createDesktopShortcut: always
    createStartMenuShortcut: true
    menuCategory: false
    runAfterFinish: true

    # displayLanguageSelector: true
    # multiLanguageInstaller: true
    # language: 2052 # 2052 - 简体中文 (Simplified Chinese)

    # 其他常用语言代码：
    # 1033 - English (US)
    # 1028 - 繁体中文 (Traditional Chinese)
    # 1041 - 日语 (Japanese)
    # 1042 - 韩语 (Korean)

# 启用依赖重建
npmRebuild: true
buildDependenciesFromSource: false
