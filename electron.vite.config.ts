import { resolve } from 'path';
import { defineConfig, externalizeDepsPlugin } from 'electron-vite';
import react from '@vitejs/plugin-react';

const sharedAliases = {
    '@shared': resolve('src/shared')
};

const mainAliases = {
    ...sharedAliases,
    '@main': resolve('src/main')
};

const preloadAliases = {
    ...sharedAliases,
    '@preload': resolve('src/preload')
};

const rendererAliases = {
    ...sharedAliases,
    ...preloadAliases,
    '@renderer': resolve('src/renderer/src'),
    '@components': resolve('src/renderer/src/components'),
    '@pages': resolve('src/renderer/src/pages'),
    '@icons': resolve('src/renderer/src/icons'),
    '@locales': resolve('src/renderer/src/locales')
};

export default defineConfig({
    main: {
        plugins: [externalizeDepsPlugin()],
        resolve: {
            alias: {
                ...mainAliases
            }
        },
        build: {
            rollupOptions: {
                output: {
                    entryFileNames: 'main.js'
                },
                external: ['@prisma/client', 'electron-store', 'serialport']
            }
        }
    },
    preload: {
        plugins: [externalizeDepsPlugin()],
        resolve: {
            alias: {
                ...mainAliases,
                ...preloadAliases
            }
        }
    },
    renderer: {
        resolve: {
            alias: {
                ...rendererAliases
            },
            extensions: ['.tsx', '.ts', '.jsx', '.js', '.json'] // 默认值 , '.json'
        },
        plugins: [react()]
    }
});
