import js from '@eslint/js';
import globals from 'globals';
import tseslint from 'typescript-eslint';
import pluginReact from 'eslint-plugin-react';
import { defineConfig } from 'eslint/config';

export default defineConfig([
    { files: ['**/*.{js,mjs,cjs,ts,jsx,tsx}'], plugins: { js }, extends: ['js/recommended'] },
    { files: ['**/*.js'], languageOptions: { sourceType: 'commonjs' } },
    { files: ['**/*.{js,mjs,cjs,ts,jsx,tsx}'], languageOptions: { globals: globals.browser } },
    tseslint.configs.recommended,
    pluginReact.configs.flat.recommended
    // ,
    // {
    //     files: ['**/*.{ts,tsx}'],
    //     rules: {
    //         '@typescript-eslint/no-explicit-any': 'off',
    //         '@typescript-eslint/no-unsafe-assignment': 'off',
    //         '@typescript-eslint/no-unsafe-member-access': 'off',
    //         '@typescript-eslint/no-unsafe-call': 'off',
    //         '@typescript-eslint/no-unsafe-return': 'off'
    //     }
    // }
]);
