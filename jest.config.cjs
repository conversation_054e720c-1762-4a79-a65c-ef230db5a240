/* eslint-env node */
/* global module */

module.exports = {
    preset: 'ts-jest/presets/default-esm',
    testEnvironment: 'node',
    roots: ['<rootDir>/src'],
    testMatch: ['**/__tests__/**/*.test.ts', '**/__tests__/**/*.test.js'],
    transform: {
        '^.+\\.tsx?$': ['ts-jest', {
            tsconfig: 'tsconfig.web.json',
            useESM: true
        }],
        '^.+\\.jsx?$': ['ts-jest', {
            useESM: true
        }]
    },
    moduleDirectories: ['node_modules', 'src'],
    setupFilesAfterEnv: ['<rootDir>/jest.setup.cjs'],
    transformIgnorePatterns: [
        'node_modules/(?!(ml-levenberg-marquardt|ml-regression|spline-interpolator|mathjs)/)'
    ],
    extensionsToTreatAsEsm: ['.ts'],
    moduleNameMapper: {
        '^(\\.{1,2}/.*)\\.js$': '$1'
    }
};


