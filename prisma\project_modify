  info{
    // 结果显示设置
    resultShow   Int // 0=数值结果 1=文本结果 2=阴阳性结果
    resultUnit   String // 结果单位
    refRangeText String // 参考范围显示文本

    // 测试参数
    testType           String // x=定性 q=定量
    // testTypeName          String // 定性/定量
    
    testWaveIdx           Int // 检测波长滤光片编号 (0-8)
    testWaveName          String // 检测波长滤光片名称
    
    refWaveIdx            Int // 参考波长滤光片编号 (0-8)
    refWaveName           String // 参考波长滤光片名称

    useBlankCorrection Int // 空白校正 0=不使用 1=max 2=min 3=mean
    enteryMode         Int // 进板模式 0=连续 1=步进
    shakeTime          Int // 振板时间(秒)

    // 参考范围 (展开为独立字段)
    refRangeDown Float // 参考范围下限
    refRangeUp   Float // 参考范围上限

    // 阴性对照范围 (展开为独立字段)  
    ncRangeDown Float // 阴性对照范围下限
    ncRangeUp   Float // 阴性对照范围上限

    // 阳性对照范围 (展开为独立字段)
    pcRangeDown Float // 阳性对照范围下限
    pcRangeUp   Float // 阳性对照范围上限

    // 灰区范围 (展开为独立字段)
    grayRangeDown Float // 灰区范围下限
    grayRangeUp   Float // 灰区范围上限

    // 其他参数
    cutOffFormula     String // Cutoff计算公式
    postiveJudge      String // 阳性公式类型 >,>=, =<,<
    grayEnble         Boolean @default(false) // 是否启用灰区
    positiveShowTxt   String // 阳性显示 positive 阳性  +
    negativeShowTxt   String // 阴性显示 negative 阴性  -
    grayShowTxt       String // 灰区显示 gray     灰区 (+)

    // 定量参数
    quantitativeMethod String // 定量方法
    quantitativeFormula String // 定量公式
    quantitativexAxis Int     @default(0) // 定量x轴类型 0=Linear 1=Log 2=Ln
    quantitativeyAxis Int     @default(0) // 定量y轴类型 0=Linear 1=Log 2=Ln
    stdConcentrationJson   String // 标准品浓度 数组
  }
  
// 项目管理表 - 简化版，展开所有范围字段
model Project {
  id      String @id @default(cuid())
  name    String @unique // 项目名称，唯一约束
  code    String @unique // 项目代码，唯一约束
  version Int    @default(1) // 版本号，用于数据结构扩展和兼容性管理

  infoJson String // 项目信息

  // 系统字段
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("projects") // 表名映射
}