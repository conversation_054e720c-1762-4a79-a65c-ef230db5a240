// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 项目管理表 - 简化版，将详细信息存储为JSON
model Project {
  id      String @id @default(cuid())
  name    String @unique // 项目名称，唯一约束
  code    String @unique // 项目代码，唯一约束
  version Int    @default(1) // 版本号，用于数据结构扩展和兼容性管理

  infoJson String // 项目详细信息JSON字符串

  // 系统字段
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("projects") // 表名映射
}

// 酶标板模板表
model PlateTemplate {
  id        String @id @default(cuid())
  name      String // 模板名称
  plateData String // 序列化的plateData (JSON字符串)

  createdBy String? // 创建者
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("plate_templates") // 表名映射
}

// 检测记录表
model TestRecord {
  id         String   @id @default(cuid()) // 检测记录ID
  mtpNumber  String // 酶标板编号
  testDate   DateTime @default(now()) // 检测日期
  updateDate DateTime @updatedAt // 更新日期

  // 复杂对象以JSON字符串形式存储
  testProjectJson        String // 检测项目JSON字符串
  testAdditionalInfoJson String // 检测附加信息JSON字符串
  wellDataJson           String? // 孔位数据JSON字符串

  // 基本类型字段直接存储
  cutOffValue Float? // 截断值（数值类型）

  @@map("test_records") // 表名映射
}
