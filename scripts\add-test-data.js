import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function addTestData() {
    try {
        // 添加测试项目
        const testProjects = [
            {
                name: '乙肝表面抗原检测',
                code: 'HBSAG',
                version: 1,
                resultShow: 0,
                resultUnit: 'ng/ml',
                refRangeText: '< 0.5',
                testType: 0,
                testWave: 4,
                refWave: 5,
                useBlankCorrection: 1,
                enteryMode: 0,
                shakeTime: 30,
                refRangeDown: 0.0,
                refRangeUp: 0.5,
                ncRangeDown: 0.0,
                ncRangeUp: 0.3,
                pcRangeDown: 0.8,
                pcRangeUp: 2.0,
                grayRangeDown: 0.5,
                grayRangeUp: 0.8,
                cutOffFormula: 'NC + 0.1',
                postiveJudge: '>=',
                grayEnble: true,
                quantitativexAxis: 0,
                quantitativeyAxis: 0
            },
            {
                name: '丙肝抗体检测',
                code: 'HCVAB',
                version: 1,
                resultShow: 0,
                resultUnit: 'S/CO',
                refRangeText: '< 1.0',
                testType: 0,
                testWave: 4,
                refWave: 5,
                useBlankCorrection: 1,
                enteryMode: 0,
                shakeTime: 30,
                refRangeDown: 0.0,
                refRangeUp: 1.0,
                ncRangeDown: 0.0,
                ncRangeUp: 0.5,
                pcRangeDown: 1.5,
                pcRangeUp: 3.0,
                grayRangeDown: 1.0,
                grayRangeUp: 1.5,
                cutOffFormula: 'NC * 0.6',
                postiveJudge: '>=',
                grayEnble: true,
                quantitativexAxis: 0,
                quantitativeyAxis: 0
            },
            {
                name: '艾滋病抗体检测',
                code: 'HIVAB',
                version: 1,
                resultShow: 0,
                resultUnit: 'S/CO',
                refRangeText: '< 1.0',
                testType: 0,
                testWave: 4,
                refWave: 5,
                useBlankCorrection: 1,
                enteryMode: 0,
                shakeTime: 30,
                refRangeDown: 0.0,
                refRangeUp: 1.0,
                ncRangeDown: 0.0,
                ncRangeUp: 0.4,
                pcRangeDown: 1.8,
                pcRangeUp: 3.5,
                grayRangeDown: 1.0,
                grayRangeUp: 1.8,
                cutOffFormula: 'NC * 0.5',
                postiveJudge: '>=',
                grayEnble: true,
                quantitativexAxis: 0,
                quantitativeyAxis: 0
            }
        ];

        for (const projectData of testProjects) {
            const project = await prisma.project.create({
                data: projectData
            });
            console.log(`已添加项目: ${project.name} (${project.code})`);
        }

        console.log('测试数据添加完成！');
    } catch (error) {
        console.error('添加测试数据失败:', error);
    } finally {
        await prisma.$disconnect();
    }
}

addTestData(); 