const fs = require('fs');
const path = require('path');

exports.default = async function (context) {
    const { appOutDir, packager, electronPlatformName, arch } = context;

    console.log('=== After Pack Script Executing ===');
    console.log('Platform:', electronPlatformName);
    console.log('Architecture:', arch);
    console.log('App Output Directory:', appOutDir);

    try {
        // 检查主进程文件
        const mainFile = path.join(appOutDir, 'resources', 'app.asar', 'out', 'main', 'main.js');
        if (fs.existsSync(mainFile)) {
            console.log('✅ Main process file exists:', mainFile);
            const stats = fs.statSync(mainFile);
            console.log('Main file size:', stats.size, 'bytes');
        } else {
            console.log('❌ Main process file not found:', mainFile);
        }

        // 检查 package.json 中的 main 字段
        const packageJsonPath = path.join(appOutDir, 'resources', 'app.asar', 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            console.log('Package.json main field:', packageJson.main);
        }

        // 检查 Electron 运行时文件
        const electronExe = path.join(appOutDir, 'khb_st_elisa.exe');
        if (fs.existsSync(electronExe)) {
            console.log('✅ Electron executable exists:', electronExe);
            const stats = fs.statSync(electronExe);
            console.log('Executable size:', stats.size, 'bytes');
        } else {
            console.log('❌ Electron executable not found:', electronExe);
        }

        // 检查 Prisma 客户端
        const prismaDir = path.join(appOutDir, 'resources', 'app.asar.unpacked', 'node_modules', '@prisma', 'client');
        if (fs.existsSync(prismaDir)) {
            console.log('✅ Prisma client found in unpacked directory');
        } else {
            console.log('❌ Prisma client not found in unpacked directory');
        }

        // 检查 serialport 模块
        const serialportDir = path.join(appOutDir, 'resources', 'app.asar.unpacked', 'node_modules', 'serialport');
        if (fs.existsSync(serialportDir)) {
            console.log('✅ Serialport module found in unpacked directory');
        } else {
            console.log('❌ Serialport module not found in unpacked directory');
        }

        console.log('=== After Pack Script Completed ===');
    } catch (error) {
        console.error('Error in after-pack script:', error);
        throw error;
    }

    return true;
};
