const { exec } = require('child_process');
const fs = require('fs');

async function checkFileUsage() {
    console.log('🔍 检查文件占用情况...');
    
    const filesToCheck = [
        './dist/win-unpacked/resources/app.asar',
        './dist',
        './out'
    ];
    
    let hasVSCodeUsage = false;
    
    for (const filePath of filesToCheck) {
        if (fs.existsSync(filePath)) {
            console.log(`📁 检查: ${filePath}`);
            
            try {
                // 尝试重命名文件来检测是否被占用
                const tempPath = filePath + '.temp';
                fs.renameSync(filePath, tempPath);
                fs.renameSync(tempPath, filePath);
                console.log(`✅ ${filePath} - 未被占用`);
            } catch (error) {
                if (error.code === 'EBUSY') {
                    console.log(`⚠️  ${filePath} - 被占用`);
                    hasVSCodeUsage = true;
                }
            }
        }
    }
    
    if (hasVSCodeUsage) {
        console.log('🔍 检测到文件被占用，通常是 VS Code 导致的');
        const answer = await askUser('是否要重启 VS Code 以释放文件占用? (y/N): ');
        
        if (answer.toLowerCase() === 'y') {
            await restartVSCode();
        }
    } else {
        console.log('✅ 没有发现文件占用问题');
    }
}

async function restartVSCode() {
    try {
        console.log('🔄 重启 VS Code...');
        await execPromise('taskkill /F /IM Code.exe');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 使用 start 命令在新窗口中启动 VS Code
        await execPromise('start cmd /c "cd /d %cd% && code ."');
        console.log('✅ VS Code 重启命令已执行');
    } catch (error) {
        console.log('⚠️  VS Code 重启失败:', error.message);
    }
}

function askUser(question) {
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    return new Promise(resolve => {
        rl.question(question, answer => {
            rl.close();
            resolve(answer);
        });
    });
}

function execPromise(command) {
    return new Promise((resolve, reject) => {
        exec(command, { encoding: 'utf8' }, (error, stdout, stderr) => {
            if (error) {
                reject(error);
            } else {
                resolve({ stdout, stderr });
            }
        });
    });
}

checkFileUsage();
