const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkRecords() {
    try {
        const count = await prisma.testRecord.count();
        console.log(`数据库中总共有 ${count} 条TestRecord记录`);
        
        if (count > 0) {
            const records = await prisma.testRecord.findMany({
                take: 5,
                orderBy: { testDate: 'desc' }
            });
            
            console.log('\n最新的5条记录:');
            records.forEach((record, index) => {
                console.log(`${index + 1}. ${record.mtpNumber} - ${record.testDate.toLocaleDateString()}`);
            });
        }
    } catch (error) {
        console.error('检查记录时出错:', error);
    } finally {
        await prisma.$disconnect();
    }
}

checkRecords(); 