const { exec } = require('child_process');
const fs = require('fs');

async function forceClean() {
    console.log('🧹 开始强制清理构建目录...');
    
    try {
        // 1. 重启 VS Code
        await restartVSCode();
        
        // 2. 删除目录
        await deleteDirectory('./dist');
        await deleteDirectory('./out');
        
        console.log('🎉 清理完成！');
        
    } catch (error) {
        console.error('💥 清理失败:', error.message);
        process.exit(1);
    }
}

async function restartVSCode() {
    try {
        console.log('� 重启 VS Code...');
        await execPromise('taskkill /F /IM Code.exe');
        await new Promise(resolve => setTimeout(resolve, 2000));
        await execPromise('code .');
        console.log('✅ VS Code 已重启');
    } catch (error) {
        console.log('⚠️  VS Code 重启失败:', error.message);
    }
}

async function deleteDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
        console.log(`📁 目录 ${dirPath} 不存在，跳过`);
        return;
    }
    
    console.log(`🗑️  删除 ${dirPath}...`);
    await execPromise(`rimraf "${dirPath}"`);
    console.log(`✅ ${dirPath} 删除完成`);
}

function execPromise(command) {
    return new Promise((resolve, reject) => {
        exec(command, { encoding: 'utf8' }, (error, stdout, stderr) => {
            if (error) {
                reject(error);
            } else {
                resolve({ stdout, stderr });
            }
        });
    });
}

forceClean();

