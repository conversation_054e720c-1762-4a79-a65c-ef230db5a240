const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// WellData类型定义参考
// sampleType: SampleTypeVariable; // 样本类型
// sampleNumber: string; // 样本编号
// odMain?: number; // 主波长OD值
// odRef?: number; // 参考OD值
// odValue?: number; // 计算后的OD值
// odRatio?: number; // 计算后的OD比
// concentration?: number; // 浓度值
// result?: '' | 'positive' | 'negative' | '阳性(+)' | '阴性(-)' | '阳性' | '阴性' | '+' | '-'; // 结果

const sampleTypeList = ['', 'sample', 'blank', 'nc', 'pc', 'qc', 'std'];
const resultList = ['', 'positive', 'negative', '阳性(+)', '阴性(-)', '阳性', '阴性', '+', '-'];

function getSampleType(row, col) {
    if (row === 0 && col >= 0 && col <= 1) return sampleTypeList[row + 1];
    if (row === 1 && col >= 0 && col <= 6) return sampleTypeList[row + 1];
    if (row === 2 && col >= 0 && col <= 1) return sampleTypeList[row + 1];
    return 'sample';
}

function getSampleNumber(row, col, recordIndex) {
    if (row === 0 && col >= 0 && col <= 1) return `BLANK${col + 1}`;
    if (row === 1 && col >= 0 && col <= 6) return `STD${col + 1}`;
    if (row === 2 && col >= 0 && col <= 1) return `QC${col + 1}`;
    return `SAMPLE${recordIndex}_${row}_${col + 1}`;
}

function getRandomResult() {
    return resultList[Math.floor(Math.random() * resultList.length)];
}

function createWellData(recordIndex) {
    const wellData = {};
    for (let row = 0; row < 8; row++) {
        for (let col = 0; col < 12; col++) {
            const well = `${String.fromCharCode(65 + row)}${col + 1}`;
            wellData[well] = {
                sampleType: { name: getSampleType(row, col), type: getSampleType(row, col) },
                sampleNumber: getSampleNumber(row, col, recordIndex),
                odMain: parseFloat((Math.random() * 2).toFixed(3)),
                odRef: parseFloat((Math.random() * 2).toFixed(3)),
                odValue: parseFloat((Math.random() * 2).toFixed(3)),
                odRatio: parseFloat((Math.random() * 2).toFixed(3)),
                concentration: parseFloat((Math.random() * 10).toFixed(3)),
                result: getRandomResult()
            };
        }
    }
    return wellData;
}

async function generateTestRecords() {
    try {
        // 1. 清空历史数据
        await prisma.testRecord.deleteMany();
        console.log('已清空 test_records 表');

        // 2. 定义项目
        const testProjects = [
            {
                code: 'PROJ001',
                name: '新冠病毒抗体检测',
                resultShow: 2,
                resultUnit: 'S/CO',
                refRangeText: '<1.0 阴性, >=1.0 阳性',
                testType: 1,
                testWave: 0,
                refWave: 1,
                useBlankCorrection: 1,
                enteryMode: 0,
                shakeTime: 30,
                refRangeDown: 0.0,
                refRangeUp: 1.0,
                ncRangeDown: 0.0,
                ncRangeUp: 0.5,
                pcRangeDown: 1.5,
                pcRangeUp: 3.0,
                grayRangeDown: 0.8,
                grayRangeUp: 1.2,
                cutOffFormula: 'NC*2.1',
                postiveJudge: '>',
                grayEnble: true,
                quantitativexAxis: 0,
                quantitativeyAxis: 0
            },
            {
                code: 'PROJ002',
                name: '乙肝表面抗原检测',
                resultShow: 2,
                resultUnit: 'S/CO',
                refRangeText: '<1.0 阴性, >=1.0 阳性',
                testType: 1,
                testWave: 0,
                refWave: 1,
                useBlankCorrection: 1,
                enteryMode: 0,
                shakeTime: 30,
                refRangeDown: 0.0,
                refRangeUp: 1.0,
                ncRangeDown: 0.0,
                ncRangeUp: 0.3,
                pcRangeDown: 2.0,
                pcRangeUp: 4.0,
                grayRangeDown: 0.9,
                grayRangeUp: 1.1,
                cutOffFormula: 'PC*0.5',
                postiveJudge: '>',
                grayEnble: true,
                quantitativexAxis: 0,
                quantitativeyAxis: 0
            },
            {
                code: 'PROJ003',
                name: '艾滋病抗体检测',
                resultShow: 2,
                resultUnit: 'S/CO',
                refRangeText: '<1.0 阴性, >=1.0 阳性',
                testType: 1,
                testWave: 0,
                refWave: 1,
                useBlankCorrection: 1,
                enteryMode: 0,
                shakeTime: 30,
                refRangeDown: 0.0,
                refRangeUp: 1.0,
                ncRangeDown: 0.0,
                ncRangeUp: 0.4,
                pcRangeDown: 1.8,
                pcRangeUp: 3.5,
                grayRangeDown: 0.85,
                grayRangeUp: 1.15,
                cutOffFormula: 'NC*2.5',
                postiveJudge: '>',
                grayEnble: true,
                quantitativexAxis: 0,
                quantitativeyAxis: 0
            }
        ];
        const projects = testProjects;
        console.log('使用硬编码项目数据，跳过数据库操作');

        // 3. 生成测试记录数据
        const testRecords = [];
        const sameMtpNumbers = ['MTP20241201001', 'MTP20241202001', 'MTP20241203001'];
        const sameDates = [new Date('2024-12-01'), new Date('2024-12-02'), new Date('2024-12-03')];
        for (let i = 0; i < sameMtpNumbers.length; i++) {
            const mtpNumber = sameMtpNumbers[i];
            const testDate = sameDates[i];
            const project = projects[i % projects.length];
            const recordCount = 3 + (i % 3);
            for (let j = 0; j < recordCount; j++) {
                const wellData = createWellData(j + 1);
                const testRecord = {
                    mtpNumber: mtpNumber,
                    testDate: testDate,
                    testProjectJson: JSON.stringify(project),
                    cutOffValue: parseFloat((Math.random() * 1 + 0.5).toFixed(3)),
                    wellDataJson: JSON.stringify(wellData),
                    testAdditionalInfoJson: JSON.stringify({
                        operator: `操作员${i + 1}`,
                        reagentBatch: `BATCH${202400 + i + 1}`,
                        reagentSupplier: `供应商${i + 1}`,
                        instrumentModel: `仪器型号${i + 1}`,
                        instrumentSerial: `SN${202400 + i + 1}`,
                        testMethod: '定量法',
                        incubationTime: '30分钟',
                        temperature: '37°C'
                    })
                };
                testRecords.push(testRecord);
            }
        }
        const differentMtpNumbers = ['MTP20241201002', 'MTP20241201003', 'MTP20241202002', 'MTP20241202003', 'MTP20241203002', 'MTP20241203003'];
        for (let i = 0; i < differentMtpNumbers.length; i++) {
            const mtpNumber = differentMtpNumbers[i];
            const testDate = new Date('2024-12-01');
            testDate.setDate(testDate.getDate() + (i % 3));
            const project = projects[i % projects.length];
            const wellData = createWellData(i + 1);
            const testRecord = {
                mtpNumber: mtpNumber,
                testDate: testDate,
                testProjectJson: JSON.stringify(project),
                cutOffValue: parseFloat((Math.random() * 1 + 0.5).toFixed(3)),
                wellDataJson: JSON.stringify(wellData),
                testAdditionalInfoJson: JSON.stringify({
                    operator: `操作员${i + 4}`,
                    reagentBatch: `BATCH${202400 + i + 4}`,
                    reagentSupplier: `供应商${i + 4}`,
                    instrumentModel: `仪器型号${i + 4}`,
                    instrumentSerial: `SN${202400 + i + 4}`,
                    testMethod: '定量法',
                    incubationTime: '30分钟',
                    temperature: '37°C'
                })
            };
            testRecords.push(testRecord);
        }
        // 4. 批量插入
        console.log(`准备插入 ${testRecords.length} 条测试记录...`);
        for (const record of testRecords) {
            await prisma.testRecord.create({ data: record });
        }
        console.log('测试数据生成完成！');
        console.log(`总共生成了 ${testRecords.length} 条测试记录`);
        const totalRecords = await prisma.testRecord.count();
        console.log(`数据库中总共有 ${totalRecords} 条记录`);
        // 按微孔板编号分组统计
        const allRecords = await prisma.testRecord.findMany();
        const groupedRecords = allRecords.reduce((groups, record) => {
            if (!groups[record.mtpNumber]) {
                groups[record.mtpNumber] = 0;
            }
            groups[record.mtpNumber]++;
            return groups;
        }, {});
        console.log('\n按微孔板编号分组的记录数量:');
        Object.entries(groupedRecords).forEach(([mtpNumber, count]) => {
            console.log(`${mtpNumber}: ${count} 条记录`);
        });
    } catch (error) {
        console.error('生成测试数据时出错:', error);
    } finally {
        await prisma.$disconnect();
    }
}

if (require.main === module) {
    generateTestRecords();
}

module.exports = { generateTestRecords };
