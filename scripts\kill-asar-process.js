const { exec } = require('child_process');
const path = require('path');

async function killAsarProcess() {
    console.log('🔍 正在查找占用 app.asar 的进程...');

    const asarPath = path.resolve('./dist/win-unpacked/resources/app.asar');
    console.log('📁 目标文件:', asarPath);

    try {
        // 方法1: 查找 Electron 相关进程
        let electronProcesses = await findElectronProcesses();

        // 方法2: 查找文件句柄
        const fileHandleProcesses = await findFileHandles();

        // 合并结果
        const allProcesses = [...electronProcesses, ...fileHandleProcesses];
        const uniqueProcesses = allProcesses.filter((proc, index, self) => index === self.findIndex((p) => p.pid === proc.pid));

        if (uniqueProcesses.length > 0) {
            console.log('📋 找到可能占用文件的进程:');
            uniqueProcesses.forEach((proc) => {
                console.log(`  - ${proc.name} (PID: ${proc.pid})`);
            });

            const answer = await askUser('是否要结束这些进程? (y/N): ');
            if (answer.toLowerCase() === 'y') {
                await killSpecificProcesses(uniqueProcesses);
            }
        } else {
            console.log('⚠️  未找到占用进程，尝试其他方法...');
            await tryAlternativeCleanup();
        }
    } catch (error) {
        console.error('❌ 查找进程失败:', error.message);
    }
}

async function findElectronProcesses() {
    const processes = [];

    // 更全面的进程查找命令
    const commands = [
        // 查找所有可能占用文件的进程
        `wmic process where "Name='electron.exe'" get ProcessId,Name,CommandLine /format:csv`,
        `wmic process where "Name='khb_st_elisa.exe'" get ProcessId,Name,CommandLine /format:csv`,
        `wmic process where "Name='node.exe'" get ProcessId,Name,CommandLine /format:csv`,
        // 使用 handle.exe 查找占用特定文件的进程（如果可用）
        `handle.exe "${path.resolve('./dist/win-unpacked/resources/app.asar')}"`,
        // 使用 PowerShell 查找占用文件的进程
        `powershell -Command "Get-Process | Where-Object {$_.ProcessName -like '*electron*' -or $_.ProcessName -like '*khb*'} | Select-Object Id,ProcessName,Path"`
    ];

    for (const command of commands) {
        try {
            console.log(`🔍 执行命令: ${command.substring(0, 60)}...`);
            const { stdout } = await execPromise(command);
            console.log(`📋 命令输出: ${stdout.substring(0, 200)}...`);

            if (command.includes('handle.exe')) {
                // 处理 handle.exe 的输出
                const lines = stdout.split('\n');
                lines.forEach((line) => {
                    const match = line.match(/(\w+\.exe)\s+pid:\s*(\d+)/);
                    if (match) {
                        processes.push({ pid: match[2], name: match[1], cmdLine: line });
                    }
                });
            } else if (command.includes('powershell')) {
                // 处理 PowerShell 输出
                const lines = stdout.split('\n').filter((line) => line.trim());
                lines.forEach((line) => {
                    const parts = line.trim().split(/\s+/);
                    if (parts.length >= 2 && /^\d+$/.test(parts[0])) {
                        processes.push({ pid: parts[0], name: parts[1], cmdLine: line });
                    }
                });
            } else {
                // 处理 wmic 输出
                const lines = stdout.split('\n').filter((line) => line.trim() && !line.startsWith('Node,'));

                lines.forEach((line) => {
                    const parts = line.split(',');
                    if (parts.length >= 3) {
                        const pid = parts[2]?.trim();
                        const name = parts[1]?.trim();
                        const cmdLine = parts[0]?.trim();

                        if (pid && name && pid !== 'ProcessId' && /^\d+$/.test(pid)) {
                            processes.push({ pid, name, cmdLine });
                        }
                    }
                });
            }
        } catch (error) {
            console.log(`⚠️  命令执行失败: ${error.message}`);
        }
    }

    // 去重
    const uniqueProcesses = processes.filter((proc, index, self) => index === self.findIndex((p) => p.pid === proc.pid));

    return uniqueProcesses;
}

async function tryAlternativeCleanup() {
    console.log('🔧 尝试温和的清理方法...');

    // 只尝试结束明确的项目相关进程
    const safeProcesses = [
        'khb_st_elisa.exe',
        'electron.exe' // 但会进一步验证
    ];

    for (const processName of safeProcesses) {
        try {
            // 先检查进程是否存在且与项目相关
            const checkCommand = `wmic process where "Name='${processName}'" get ProcessId,CommandLine /format:csv`;
            const { stdout } = await execPromise(checkCommand);

            if (stdout.includes('st_elisa') || stdout.includes('khb')) {
                console.log(`🎯 发现项目相关进程: ${processName}`);
                const answer = await askUser(`是否结束 ${processName}? (y/N): `);
                if (answer.toLowerCase() === 'y') {
                    await execPromise(`taskkill /F /IM ${processName}`);
                    console.log(`✅ 已结束 ${processName}`);
                }
            }
        } catch (error) {
            // 进程可能不存在，忽略错误
        }
    }
}

async function killSpecificProcesses(processes) {
    console.log('🔪 正在结束指定进程...');

    for (const proc of processes) {
        try {
            await execPromise(`taskkill /F /PID ${proc.pid}`);
            console.log(`✅ 已结束进程: ${proc.name} (PID: ${proc.pid})`);
        } catch (error) {
            console.log(`❌ 结束进程 ${proc.pid} 失败:`, error.message);
        }
    }

    // 等待进程完全释放
    console.log('⏳ 等待进程完全释放...');
    await new Promise((resolve) => setTimeout(resolve, 2000));
}

function askUser(question) {
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            rl.close();
            resolve(answer);
        });
    });
}

function execPromise(command) {
    return new Promise((resolve, reject) => {
        exec(command, { encoding: 'utf8' }, (error, stdout, stderr) => {
            if (error) {
                reject(error);
            } else {
                resolve({ stdout, stderr });
            }
        });
    });
}

async function findFileHandles() {
    const asarPath = path.resolve('./dist/win-unpacked/resources/app.asar');
    console.log('🔍 使用 PowerShell 查找文件句柄...');

    try {
        // 使用 PowerShell 的 Get-Process 和文件句柄检测
        const psCommand = `
            $filePath = "${asarPath.replace(/\\/g, '\\\\')}"
            Get-Process | ForEach-Object {
                try {
                    $proc = $_
                    $handles = $proc.Modules | Where-Object { $_.FileName -like "*app.asar*" }
                    if ($handles) {
                        Write-Output "$($proc.Id),$($proc.ProcessName),$($proc.Path)"
                    }
                } catch {}
            }
        `;

        const { stdout } = await execPromise(`powershell -Command "${psCommand}"`);

        const processes = [];
        if (stdout.trim()) {
            const lines = stdout.split('\n').filter((line) => line.trim());
            lines.forEach((line) => {
                const parts = line.split(',');
                if (parts.length >= 2) {
                    processes.push({
                        pid: parts[0].trim(),
                        name: parts[1].trim(),
                        cmdLine: parts[2]?.trim() || ''
                    });
                }
            });
        }

        return processes;
    } catch (error) {
        console.log('⚠️  PowerShell 文件句柄检测失败:', error.message);
        return [];
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    killAsarProcess()
        .then(() => {
            console.log('🎉 进程清理完成');
        })
        .catch((error) => {
            console.error('💥 脚本执行失败:', error);
            process.exit(1);
        });
}

module.exports = { killAsarProcess };
