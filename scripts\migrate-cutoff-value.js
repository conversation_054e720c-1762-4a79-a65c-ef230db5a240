const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function migrateCutoffValue() {
    try {
        console.log('开始迁移 cutOffValue 数据...');

        // 1. 备份现有数据
        const backupPath = path.join(__dirname, 'backup-test-records.json');
        const existingRecords = await prisma.testRecord.findMany();
        
        fs.writeFileSync(backupPath, JSON.stringify(existingRecords, null, 2));
        console.log(`数据已备份到: ${backupPath}`);

        // 2. 迁移数据
        let migratedCount = 0;
        let errorCount = 0;

        for (const record of existingRecords) {
            try {
                let cutOffValue = null;

                // 解析原有的 cutOffValueJson
                if (record.cutOffValueJson) {
                    const parsed = JSON.parse(record.cutOffValueJson);
                    if (typeof parsed === 'object' && parsed !== null && 'value' in parsed) {
                        cutOffValue = parseFloat(parsed.value);
                    } else if (typeof parsed === 'number') {
                        cutOffValue = parsed;
                    } else {
                        console.warn(`跳过无效的 cutOffValue: ${record.id}`);
                        continue;
                    }
                }

                // 更新记录
                await prisma.testRecord.update({
                    where: { id: record.id },
                    data: {
                        cutOffValue: cutOffValue
                    }
                });

                migratedCount++;
                console.log(`已迁移记录: ${record.id}, cutOffValue: ${cutOffValue}`);
            } catch (error) {
                errorCount++;
                console.error(`迁移记录失败: ${record.id}`, error);
            }
        }

        console.log(`迁移完成！成功: ${migratedCount}, 失败: ${errorCount}`);

    } catch (error) {
        console.error('迁移过程中出错:', error);
    } finally {
        await prisma.$disconnect();
    }
}

// 运行迁移
if (require.main === module) {
    migrateCutoffValue();
}

module.exports = { migrateCutoffValue }; 