/**
 * 简单数据迁移脚本
 * 从 data_back.db 迁移项目数据到现有数据库
 */

const { PrismaClient } = require('@prisma/client');
const path = require('path');
const fs = require('fs');

// 数据库路径
const OLD_DB = 'C:\\Users\\<USER>\\AppData\\Roaming\\khb_st_elisa\\database\\data_back.db';
const NEW_DB = 'C:\\Users\\<USER>\\AppData\\Roaming\\khb_st_elisa\\database\\data.db';

console.log('🔄 简单数据迁移');
console.log('===============');
console.log(`源: ${OLD_DB}`);
console.log(`目标: ${NEW_DB}`);

// 转换旧项目数据为新JSON格式
function convertToNewFormat(oldProject) {
  return {
    resultShow: oldProject.resultShow ?? 0,
    resultUnit: oldProject.resultUnit || '',
    refRangeText: oldProject.refRangeText || '',
    testType: oldProject.testType ?? 'x',
    // testTypeName: (oldProject.testType === 1) ? '定量' : '定性',
    testWaveIdx: oldProject.testWave ?? 0,
    testWaveName: oldProject.testWave ? `${oldProject.testWave}nm` : '',
    refWaveIdx: oldProject.refWave ?? 0,
    refWaveName: oldProject.refWave === 0 ? '无参考' : `${oldProject.refWave}nm`,
    useBlankCorrection: oldProject.useBlankCorrection ?? 0,
    enteryMode: oldProject.enteryMode ?? 0,
    shakeTime: oldProject.shakeTime ?? 0,
    refRangeDown: oldProject.refRangeDown ?? 0,
    refRangeUp: oldProject.refRangeUp ?? 0,
    ncRangeDown: oldProject.ncRangeDown ?? 0,
    ncRangeUp: oldProject.ncRangeUp ?? 0,
    pcRangeDown: oldProject.pcRangeDown ?? 0,
    pcRangeUp: oldProject.pcRangeUp ?? 0,
    grayRangeDown: oldProject.grayRangeDown ?? 0,
    grayRangeUp: oldProject.grayRangeUp ?? 0,
    cutOffFormula: oldProject.cutOffFormula || '',
    postiveJudge: oldProject.postiveJudge || '>',
    grayEnble: Boolean(oldProject.grayEnble),
    positiveShowTxt: '阳性',
    negativeShowTxt: '阴性',
    grayShowTxt: '灰区',
    quantitativeMethod: (oldProject.testType === 1) ? '线性回归' : '',
    quantitativeFormula: '',
    quantitativexAxis: oldProject.quantitativexAxis ?? 0,
    quantitativeyAxis: oldProject.quantitativeyAxis ?? 0,
    stdConcentration: []
  };
}

async function migrate() {
  if (!fs.existsSync(OLD_DB)) {
    console.log(`❌ 未找到: ${OLD_DB}`);
    return;
  }
  
  const oldPrisma = new PrismaClient({
    datasources: { db: { url: `file:${OLD_DB}` } }
  });
  
  process.env.DATABASE_URL = `file:${NEW_DB}`;
  const newPrisma = new PrismaClient();
  
  try {
    await oldPrisma.$connect();
    await newPrisma.$connect();
    
    // 1. 迁移项目数据
    console.log('\n📖 迁移项目数据...');
    const oldProjects = await oldPrisma.$queryRaw`SELECT * FROM projects`;
    console.log(`读取到 ${oldProjects.length} 个项目`);
    
    let projectSuccess = 0;
    for (const old of oldProjects) {
      try {
        const info = convertToNewFormat(old);
        await newPrisma.project.create({
          data: {
            id: old.id,
            name: old.name,
            code: old.code,
            version: old.version || 1,
            infoJson: JSON.stringify(info),
            createdAt: old.createdAt ? new Date(old.createdAt) : new Date(),
            updatedAt: old.updatedAt ? new Date(old.updatedAt) : new Date()
          }
        });
        projectSuccess++;
        console.log(`✅ 项目: ${old.name}`);
      } catch (error) {
        console.log(`❌ 项目: ${old.name} - ${error.message}`);
      }
    }
    
    // 2. 迁移模板数据
    console.log('\n📖 迁移模板数据...');
    try {
      const oldTemplates = await oldPrisma.$queryRaw`SELECT * FROM plate_templates`;
      console.log(`读取到 ${oldTemplates.length} 个模板`);
      
      let templateSuccess = 0;
      for (const template of oldTemplates) {
        try {
          await newPrisma.$executeRaw`
            INSERT OR REPLACE INTO plate_templates (id, name, plateData, createdBy, createdAt, updatedAt)
            VALUES (${template.id}, ${template.name}, ${template.plateData}, 
                    ${template.createdBy}, ${template.createdAt}, ${template.updatedAt})
          `;
          templateSuccess++;
          console.log(`✅ 模板: ${template.name}`);
        } catch (error) {
          console.log(`❌ 模板: ${template.name} - ${error.message}`);
        }
      }
      console.log(`模板迁移: ${templateSuccess}/${oldTemplates.length}`);
    } catch (e) {
      console.log('没有模板数据需要迁移');
    }
    
    // 3. 迁移检测记录数据
    console.log('\n📖 迁移检测记录数据...');
    try {
      const oldRecords = await oldPrisma.$queryRaw`SELECT * FROM test_records`;
      console.log(`读取到 ${oldRecords.length} 个检测记录`);
      
      let recordSuccess = 0;
      for (const record of oldRecords) {
        try {
          await newPrisma.$executeRaw`
            INSERT OR REPLACE INTO test_records (id, mtpNumber, testDate, updateDate, testProjectJson, testAdditionalInfoJson, wellDataJson, cutOffValue)
            VALUES (${record.id}, ${record.mtpNumber}, ${record.testDate}, ${record.updateDate}, 
                    ${record.testProjectJson}, ${record.testAdditionalInfoJson}, ${record.wellDataJson}, ${record.cutOffValue})
          `;
          recordSuccess++;
          console.log(`✅ 记录: ${record.mtpNumber}`);
        } catch (error) {
          console.log(`❌ 记录: ${record.mtpNumber} - ${error.message}`);
        }
      }
      console.log(`检测记录迁移: ${recordSuccess}/${oldRecords.length}`);
    } catch (e) {
      console.log('没有检测记录数据需要迁移');
    }
    
    console.log(`\n🎉 迁移完成!`);
    console.log(`✅ 项目: ${projectSuccess}/${oldProjects.length}`);
    
  } finally {
    await oldPrisma.$disconnect();
    await newPrisma.$disconnect();
  }
}

migrate();