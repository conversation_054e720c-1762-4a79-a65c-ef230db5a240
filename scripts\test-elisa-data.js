// 测试酶标板数据完整性检查的脚本

console.log('开始测试酶标板数据完整性检查...\n');

// 模拟酶标板数据格式（包含列标题）
const testData1 = `1     2     3     4     5     6     7     8     9     10    11    12
A 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
B 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
C 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
D 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
E 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
F 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
G 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
H 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000`;

const testData2 = `1     2     3     4     5     6     7     8     9     10    11    12
A 0.123 0.456 0.789 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
B 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
C 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
D 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
E 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
F 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
G 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
H 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000`;

// 两遍数据测试（包含中间列标题）
const testData3 = `1     2     3     4     5     6     7     8     9     10    11    12
A 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
B 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
C 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
D 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
E 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
F 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
G 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
H 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
1     2     3     4     5     6     7     8     9     10    11    12
A 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
B 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
C 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
D 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
E 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
F 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
G 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
H 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000`;

// 不完整数据测试
const incompleteData = `1     2     3     4     5     6     7     8     9     10    11    12
A 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
B 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000
C 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000 0.000`;

// 模拟数据完整性检查函数
function isDataComplete(dataString) {
    if (!dataString || dataString.length === 0) return false;

    console.log('检查数据完整性');
    console.log('数据预览:', dataString.substring(0, 200) + (dataString.length > 200 ? '...' : ''));

    // 只保留A-H开头的行，跳过列标题行
    const lines = dataString
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => /^[A-H]/.test(line));

    console.log('过滤后的A-H行数:', lines.length);

    // 检查是否有足够的A-H行（8行或16行）
    if (lines.length === 8 || lines.length === 16) {
        // 检查每8行是否A-H顺序且每行13列
        const expectedRows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];

        const validBlock = (block) => {
            return (
                block.length === 8 &&
                block.every((line, i) => {
                    const parts = line.split(/\s+/);
                    return parts[0] === expectedRows[i] && parts.length >= 13;
                })
            );
        };

        // 检查第一组8行
        if (validBlock(lines.slice(0, 8))) {
            if (lines.length === 8) {
                console.log('✓ 数据完整性检查：发现完整的酶标板数据（一遍）');
                return true;
            } else if (lines.length === 16 && validBlock(lines.slice(8, 16))) {
                console.log('✓ 数据完整性检查：发现完整的酶标板数据（两遍）');
                return true;
            }
        }
    }

    console.log('✗ 数据不完整：继续等待 (A-H行数:', lines.length, ')');
    return false;
}

// 模拟OD值解析函数
function parseODValues(dataString) {
    const odValues = {};

    try {
        // 只处理A-H开头的行，跳过列标题行
        const lines = dataString
            .split('\n')
            .map((line) => line.trim())
            .filter((line) => /^[A-H]/.test(line));

        console.log('开始解析OD值，A-H行数:', lines.length);

        // 处理所有A-H开头的行
        for (const line of lines) {
            const parts = line.split(/\s+/).filter((part) => part.length > 0);

            // 检查行首是否为A-H
            const rowLetter = parts[0];
            if (!rowLetter || !['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'].includes(rowLetter)) {
                continue;
            }

            // 解析12个数值
            for (let j = 1; j <= 12 && j < parts.length; j++) {
                const wellId = `${rowLetter}${j}`;
                const odValue = parseFloat(parts[j]);

                if (!isNaN(odValue)) {
                    // 如果是两遍数据，后面的会覆盖前面的
                    odValues[wellId] = odValue;
                }
            }
        }

        console.log('OD值解析完成，解析孔位数:', Object.keys(odValues).length);
    } catch (error) {
        console.error('解析OD值失败:', error);
    }

    return odValues;
}

// 测试用例
console.log('=== 测试用例1：一遍数据（含列标题） ===');
console.log(isDataComplete(testData1) ? '✓ 通过' : '✗ 失败');
const result1 = parseODValues(testData1);
console.log('解析结果:', Object.keys(result1).length, '个孔位');
console.log();

console.log('=== 测试用例2：带数值的一遍数据（含列标题） ===');
console.log(isDataComplete(testData2) ? '✓ 通过' : '✗ 失败');
const result2 = parseODValues(testData2);
console.log('解析结果:', Object.keys(result2).length, '个孔位');
console.log('A1值:', result2['A1'], 'A2值:', result2['A2'], 'A3值:', result2['A3']);
console.log();

console.log('=== 测试用例3：两遍数据（含中间列标题） ===');
console.log(isDataComplete(testData3) ? '✓ 通过' : '✗ 失败');
const result3 = parseODValues(testData3);
console.log('解析结果:', Object.keys(result3).length, '个孔位');
console.log();

console.log('=== 测试用例4：不完整数据（含列标题） ===');
console.log(isDataComplete(incompleteData) ? '✓ 通过' : '✗ 失败');
const result4 = parseODValues(incompleteData);
console.log('解析结果:', Object.keys(result4).length, '个孔位');
console.log();

console.log('=== 测试用例5：空数据 ===');
console.log(isDataComplete('') ? '✓ 通过' : '✗ 失败');
console.log();

console.log('测试完成！');
