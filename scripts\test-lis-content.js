// 模拟TestRecord数据结构
const mockTestRecord = {
    id: 'test-001',
    mtpNumber: 'MTP20240329001',
    testDate: new Date('2024-03-29'),
    updateDate: new Date('2024-03-29'),
    cutOffValue: 0.1,
    testProject: {
        id: 'proj-001',
        name: '乙肝表面抗原',
        code: 'HBsAg',
        testType: 'x', // 定性
        resultShow: 2,
        resultUnit: 'OD',
        refRangeText: '0~5',
        testWave: 0,
        refWave: 0,
        useBlankCorrection: 'none',
        enteryMode: 0,
        shakeTime: 0,
        refRangeDown: 0,
        refRangeUp: 5,
        ncRangeDown: 0,
        ncRangeUp: 5,
        pcRangeDown: 0,
        pcRangeUp: 5,
        grayRangeDown: 0,
        grayRangeUp: 5,
        cutOffFormula: 'Cutoff计算公式',
        postiveJudge: '>=',
        grayEnble: false,
        quantitativexAxis: 0,
        quantitativeyAxis: 0,
        quantitativeMethod: '线性回归',
        quantitativeFormula: 'y = mx + b',
        stdConcentration: [
            ['STD1', 1],
            ['STD2', 2],
            ['STD3', 3]
        ]
    },
    testAdditionalInfo: {
        reagentBatch: 'BATCH001',
        reagentExpiry: '2025-12-31',
        reagentSupplier: '供应商A',
        testTemperature: 25,
        testRelativeHumidity: 60,
        testReviewer: '审核员A',
        mainTitle: '主标题',
        subTitle: '副标题',
        note: '备注',
        testMethod: '检测方法',
        testOperator: '操作员A'
    },
    wellData: {
        A1: {
            sampleType: { name: '', type: '' },
            sampleNumber: 'A1066',
            odMain: 0.0671,
            odRef: 0.0671,
            odValue: 0.0671,
            odRatio: 0.3197,
            concentration: 0.3197,
            result: '阴性'
        },
        A2: {
            sampleType: { name: 'sample', type: 'sample' },
            sampleNumber: 'A1067',
            odMain: 0.1234,
            odRef: 0.1234,
            odValue: 0.1234,
            odRatio: 0.5678,
            concentration: 0.5678,
            result: '阳性'
        },
        B1: {
            sampleType: { name: 'sample', type: 'sample' },
            sampleNumber: 'A1068',
            odMain: 0.089,
            odRef: 0.089,
            odValue: 0.089,
            odRatio: 0.445,
            concentration: 0.445,
            result: '阴性'
        }
    }
};

// 模拟LisExporter的核心方法
class MockLisExporter {
    generateLISContent(record) {
        const lines = [];

        // 获取项目信息
        const projectCode = record.testProject.code;
        const projectType = record.testProject.testType;

        // 格式化检测日期
        const testDate = this.formatDate(record.testDate);

        // 遍历所有孔位数据
        const wellIds = this.getWellIds();

        for (const wellId of wellIds) {
            const wellData = record.wellData?.[wellId];

            if (wellData && wellData.sampleNumber) {
                // 获取各项数据
                const sampleNumber = wellData.sampleNumber;
                const odValue = wellData.odValue?.toFixed(4) || '0.0000';
                const odRatio = wellData.odRatio?.toFixed(4) || '0.0000';
                const result = this.mapResult(wellData.result);

                // 生成LIS格式行：孔号=[检验日期,标本号,项目代码,项目类型,结果,OD值,OD/CUTOFF]
                const line = `${wellId}=[${testDate},${sampleNumber},${projectCode},${projectType},${result},${odValue},${odRatio}]`;
                lines.push(line);
            } else if (wellData) {
                // 如果有孔位数据但没有样本号，使用默认值
                const sampleNumber = 'N/A';
                const odValue = wellData.odValue?.toFixed(4) || '0.0000';
                const odRatio = wellData.odRatio?.toFixed(4) || '0.0000';
                const result = this.mapResult(wellData.result);

                const line = `${wellId}=[${testDate},${sampleNumber},${projectCode},${projectType},${result},${odValue},${odRatio}]`;
                lines.push(line);
            }
        }

        return lines.join('\n');
    }

    getWellIds() {
        const wellIds = [];
        const rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
        const cols = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];

        for (const row of rows) {
            for (const col of cols) {
                wellIds.push(`${row}${col}`);
            }
        }

        return wellIds;
    }

    formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}${month}${day}`;
    }

    mapResult(result) {
        if (!result) return 'N';

        const resultMap = {
            阳性: 'P',
            '阳性(+)': 'P',
            '+': 'P',
            positive: 'P',
            阴性: 'N',
            '阴性(-)': 'N',
            '-': 'N',
            negative: 'N'
        };

        return resultMap[result] || 'N';
    }
}

// 测试
function testLisContent() {
    console.log('开始测试LIS内容生成...');

    const exporter = new MockLisExporter();
    const content = exporter.generateLISContent(mockTestRecord);

    console.log('生成的LIS内容:');
    console.log('='.repeat(50));
    console.log(content);
    console.log('='.repeat(50));

    // 验证关键行
    const lines = content.split('\n');
    const a1Line = lines.find((line) => line.startsWith('A1='));
    const a2Line = lines.find((line) => line.startsWith('A2='));
    const b1Line = lines.find((line) => line.startsWith('B1='));

    console.log('\n验证关键行:');
    console.log('A1行:', a1Line);
    console.log('A2行:', a2Line);
    console.log('B1行:', b1Line);

    // 验证格式
    if (a1Line && a1Line.includes('20240329,A1066,HBsAg,X,N,0.0671,0.3197')) {
        console.log('✅ A1行格式正确');
    } else {
        console.log('❌ A1行格式错误');
    }

    if (a2Line && a2Line.includes('20240329,A1067,HBsAg,X,P,0.1234,0.5678')) {
        console.log('✅ A2行格式正确');
    } else {
        console.log('❌ A2行格式错误');
    }

    console.log('\n测试完成!');
}

testLisContent();
