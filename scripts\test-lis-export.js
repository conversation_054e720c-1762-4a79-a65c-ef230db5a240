const { LisExporter } = require('../src/main/utils/lisExport');
const { createMockTestRecord } = require('../src/renderer/src/utils/testDataGenerator');

// 创建测试数据
const testRecord = createMockTestRecord('test-001', '2024-03-29', 'HBsAg', '乙肝表面抗原', {
    includeWellData: true,
    cutOffValue: 0.1
});

// 创建PrintRequest
const printRequest = {
    projectCode: 'HBsAg',
    projectName: '乙肝表面抗原',
    record: testRecord,
    printDate: '2024-03-29',
    options: {
        language: 'zh',
        printType: 'qualitative',
        includePlateData: true,
        includeStatistics: true,
        dataType: 'od'
    }
};

// 测试LisExporter
async function testLisExport() {
    try {
        console.log('开始测试LIS导出功能...');

        const lisExporter = new LisExporter();
        const result = await lisExporter.exportToLIS(printRequest);

        console.log('导出结果:', result);

        if (result.success) {
            console.log('✅ LIS导出成功!');
            console.log('文件路径:', result.data?.filePath);
        } else {
            console.log('❌ LIS导出失败:', result.message);
        }
    } catch (error) {
        console.error('测试过程中发生错误:', error);
    }
}

// 运行测试
testLisExport();
