// 测试logger修复的脚本
// 这个脚本模拟了可能导致"An object could not be cloned"错误的情况

console.log('开始测试logger修复...');

// 模拟一个包含循环引用的错误对象
function createCircularError() {
    const error = new Error('测试错误');
    const circularObj = { error };
    error.circularRef = circularObj; // 创建循环引用
    return error;
}

// 模拟序列化函数
function serializeError(error) {
    if (error instanceof Error) {
        return {
            name: error.name,
            message: error.message,
            stack: error.stack
            // 注意：不包含可能导致循环引用的属性
        };
    }

    // 处理其他类型的错误
    if (typeof error === 'object' && error !== null) {
        try {
            // 尝试序列化对象
            JSON.stringify(error);
            return error;
        } catch {
            // 如果无法序列化，返回字符串表示
            return {
                type: typeof error,
                stringValue: String(error)
            };
        }
    }

    return error;
}

// 测试1: 普通Error对象
console.log('\n测试1: 普通Error对象');
try {
    const normalError = new Error('普通错误');
    const serialized = serializeError(normalError);
    console.log('✓ 普通Error序列化成功:', serialized);
} catch (error) {
    console.log('✗ 普通Error序列化失败:', error.message);
}

// 测试2: 包含循环引用的Error对象
console.log('\n测试2: 包含循环引用的Error对象');
try {
    const circularError = createCircularError();
    const serialized = serializeError(circularError);
    console.log('✓ 循环引用Error序列化成功:', serialized);
} catch (error) {
    console.log('✗ 循环引用Error序列化失败:', error.message);
}

// 测试3: 复杂对象
console.log('\n测试3: 复杂对象');
try {
    const complexObj = {
        func: function () {
            return 'test';
        }, // 函数不可序列化
        date: new Date(),
        error: new Error('嵌套错误')
    };
    const serialized = serializeError(complexObj);
    console.log('✓ 复杂对象序列化成功:', serialized);
} catch (error) {
    console.log('✗ 复杂对象序列化失败:', error.message);
}

// 测试4: 直接JSON.stringify测试
console.log('\n测试4: 直接JSON.stringify测试');
try {
    const circularError = createCircularError();
    JSON.stringify(circularError);
    console.log('✗ 直接JSON.stringify应该失败但成功了');
} catch (error) {
    console.log('✓ 直接JSON.stringify正确失败:', error.message);
}

console.log('\n测试完成！');
console.log('如果所有测试都通过，说明修复是有效的。');
