import { app, ipcMain } from 'electron';
import { IPCChannels } from '@shared/ipcChannels';
import { dialog } from 'electron';
import logger from './utils/logger';

// 确保日志目录存在
// const LIS_DIR = path.join(app.getPath('userData'), 'lis');

export async function setupAppHandlers(): Promise<void> {
    logger.info('setupAppHandlers', { component: './src/main/app.ts' });

    // 处理版本号请求
    ipcMain.handle(IPCChannels.APP.GetAppVersion, async () => {
        return app.getVersion();
    });

    ipcMain.handle(IPCChannels.APP.GetUserPath, async () => {
        return app.getPath('userData');
    });

    ipcMain.handle(IPCChannels.APP.GetAppPath, async () => {
        return app.getPath('appData');
    });

    ipcMain.handle(IPCChannels.APP.OpenFileDialog, async (_, options) => {
        return dialog.showOpenDialog(options);
    });

    ipcMain.handle(IPCChannels.APP.OpenFolderDialog, async (_, options) => {
        return dialog.showOpenDialog(options);
    });

    ipcMain.handle(IPCChannels.APP.SaveFileDialog, async (_, options) => {
        return dialog.showSaveDialog(options);
    });

    ipcMain.handle(IPCChannels.APP.SaveFolderDialog, async (_, options) => {
        return dialog.showSaveDialog(options);
    });

    // TODO: 实现应用设置相关的处理器
    // ipcMain.handle(IPCChannels.APP.GetAppSettings, async () => {
    //     // 实现获取设置的逻辑
    // });

    // ipcMain.handle(IPCChannels.APP.UpdateAppSettings, async (_, settings) => {
    //     // 实现更新设置的逻辑
    // });
}
