import { ipcMain } from 'electron';
import { IPCChannels } from '@shared/ipcChannels';
import { ConfigLoader } from './utils/configLoader';
import logger from './utils/logger';

export async function setupConfigInfoHandlers(): Promise<void> {
    logger.info('setupConfigInfoHandlers', { component: './src/main/configInfo.ts' });
    const configLoader = await ConfigLoader.getInstance();
    // logger.debug('配置加载器实例', { component: 'configInfo.ts', data: configLoader });

    ipcMain.handle(IPCChannels.configInfo.GetDeviceConfigInfo, async () => {
        try {
            // logger.debug('获取设备配置信息', { component: 'configInfo.ts' });
            return configLoader.getConfig();
        } catch (error) {
            logger.error('获取设备配置失败', error, { component: './src/main/configInfo.ts' });
            throw error;
        }
    });
}
