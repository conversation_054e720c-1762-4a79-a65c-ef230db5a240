import { PrismaClient } from '@prisma/client';
import { ApiResponse } from '@shared/types';
import { app } from 'electron';
import path from 'path';
import fs from 'fs';
import logger from '../utils/logger';

function getDatabasePath(): string {
    // 统一使用用户数据目录
    const userDataPath = app.getPath('userData');
    const dbDir = path.join(userDataPath, 'database');
    const dbPath = path.join(dbDir, 'data.db');

    logger.info('Database path:', {
        data: dbPath,
        component: './src/main/database/index.ts'
    });

    // 确保目录存在
    if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
    }

    return `file:${dbPath}`;
}

// 创建 Prisma 客户端实例
const databaseUrl = getDatabasePath();

export const prisma = new PrismaClient({
    // __internal: {
    //     engine: {
    //         binaryPath: path.join(__dirname, 'node_modules/.prisma/client/query-engine')
    //     }
    // },
    datasources: {
        db: {
            url: databaseUrl
        }
    }
});

// 数据库初始化函数
export async function initializeDatabase(): Promise<void> {
    try {
        // 确保数据库连接正常
        await prisma.$connect();

        logger.info('check database initialized...', { component: './src/main/database/index.ts' });

        // 检查数据库是否已经初始化（检查是否存在表）
        const isInitialized = await checkDatabaseInitialized();

        logger.info('database initialized status:', {
            data: isInitialized,
            component: './src/main/database/index.ts'
        });

        if (!isInitialized) {
            logger.info('first run, creating database schema...', {
                component: './src/main/database/index.ts'
            });
            await createDatabaseSchema();
        } else {
            logger.info('database already exists, skipping schema creation', {
                component: './src/main/database/index.ts'
            });
        }
        // 移除 upgradeDatabaseSchema 调用，使用 Prisma migrations 处理结构升级
        logger.info('database initialized successfully', {
            component: './src/main/database/index.ts'
        });
    } catch (error) {
        logger.error('database init error:', error, { component: './src/main/database/index.ts' });
        throw error;
    }
}

// 检查数据库是否已初始化
async function checkDatabaseInitialized(): Promise<boolean> {
    try {
        // 尝试查询表结构，而不是查询数据
        const result = await prisma.$queryRaw<Array<{ name: string }>>`
            SELECT name FROM sqlite_master WHERE type='table' AND name='projects'
        `;

        const hasTable = result.length > 0;
        logger.info('database initialized status, isExist:', {
            data: hasTable,
            component: './src/main/database/index.ts'
        });
        return hasTable;
    } catch (error) {
        logger.error('check database initialized error:', error, {
            component: './src/main/database/index.ts'
        });
        return false;
    }
}

// 创建数据库表结构
async function createDatabaseSchema(): Promise<void> {
    try {
        // 创建 projects 表 - 新简化结构
        await prisma.$executeRaw`
            CREATE TABLE IF NOT EXISTS "projects" (
                "id" TEXT NOT NULL PRIMARY KEY,
                "name" TEXT NOT NULL UNIQUE,
                "code" TEXT NOT NULL UNIQUE,
                "version" INTEGER NOT NULL DEFAULT 1,
                "infoJson" TEXT NOT NULL,
                "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            );
        `;

        // 创建 plate_templates 表
        await prisma.$executeRaw`
            CREATE TABLE IF NOT EXISTS "plate_templates" (
                "id" TEXT NOT NULL PRIMARY KEY,
                "name" TEXT NOT NULL,
                "plateData" TEXT NOT NULL,
                "createdBy" TEXT,
                "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            );
        `;

        // 创建 test_records 表
        await prisma.$executeRaw`
            CREATE TABLE IF NOT EXISTS "test_records" (
                "id" TEXT NOT NULL PRIMARY KEY,
                "mtpNumber" TEXT NOT NULL,
                "testDate" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "updateDate" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "testProjectJson" TEXT NOT NULL,
                "testAdditionalInfoJson" TEXT NOT NULL,
                "wellDataJson" TEXT,
                "cutOffValue" REAL
            );
        `;

        logger.info('database schema created scuccessfully', {
            component: './src/main/database/index.ts'
        });
    } catch (error) {
        logger.error('create database schema error:', error, {
            component: './src/main/database/index.ts'
        });
        throw error;
    }
}

// 数据库清理函数
export async function closeDatabase(): Promise<void> {
    try {
        await prisma.$disconnect();
        logger.info('database connection closed', { component: './src/main/database/index.ts' });
    } catch (error) {
        logger.error('close database error:', error, { component: './src/main/database/index.ts' });
    }
}

// 统一错误处理函数
export function handlePrismaError<T = void>(error: unknown, operation: string): ApiResponse<T> {
    logger.error('handlePrismaError:', error, { component: './src/main/database/index.ts' });

    // 检查是否是Prisma错误
    if (error && typeof error === 'object' && 'code' in error) {
        const prismaError = error as { code: string; meta?: { target?: string[] } };

        if (prismaError.code === 'P2002') {
            const field = prismaError.meta?.target?.[0];
            if (field === 'name') {
                return { success: false, error: 'name already exists' };
            }
            if (field === 'code') {
                return { success: false, error: 'code already exists' };
            }
            return { success: false, error: 'data already exists, please check the unique field' };
        }

        if (prismaError.code === 'P2025') {
            return { success: false, error: 'data not found' };
        }
    }

    return { success: false, error: `${operation} failed` };
}

// 导出数据库初始化函数
export { setupDatabaseHandlers } from './setup';

// 导出数据库操作模块
export * from './testProject';
export * from './mtpTemplateManager';
export * from './testRecord';
// export * from './device';      // 未来扩展
// export * from './filter';     // 未来扩展
// export * from './settings';   // 未来扩展
