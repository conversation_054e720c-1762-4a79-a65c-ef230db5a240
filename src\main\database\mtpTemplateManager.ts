import { prisma } from './index';
import { PlateData } from '@shared/types/plateData';

// 直接使用 Prisma 生成的类型 - 无需任何修改！
import type { PlateTemplate as PrismaPlateTemplate } from '@prisma/client';
import logger from '../../preload/utils/logger';

export type PlateTemplate = PrismaPlateTemplate;

// export interface CreateTemplateData {
//     name: string;

//     plateData: string;

//     createdBy?: string;
// }

// export interface UpdateTemplateData {
//     name?: string;
//     description?: string;
//     plateData?: PlateData;
//     category?: string;
//     isPublic?: boolean;
// }

export class MtpTemplateManager {
    // 创建模板
    static async addTemplate(data: { name: string; plateData: PlateData; createdBy?: string }) {
        try {
            const template = await prisma.plateTemplate.create({
                data: {
                    name: data.name,
                    plateData: JSON.stringify(data.plateData),
                    createdBy: data.createdBy
                }
            });
            return template;
        } catch (error) {
            logger.error('create template error:', error, {
                component: './src/main/database/mtpTemplateManager.ts'
            });
            throw error;
        }
    }

    // 获取所有模板
    static async getAllTemplates(): Promise<PlateTemplate[]> {
        try {
            const templates = await prisma.plateTemplate.findMany({
                orderBy: {
                    createdAt: 'desc'
                }
            });
            return templates;
        } catch (error) {
            logger.error('get all templates error:', error, {
                component: './src/main/database/mtpTemplateManager.ts'
            });

            throw error;
        }
    }

    // 根据ID获取模板
    static async getTemplateById(id: string): Promise<PlateTemplate | null> {
        try {
            const template = await prisma.plateTemplate.findUnique({
                where: { id }
            });
            return template;
        } catch (error) {
            logger.error('get template by id error:', error, {
                component: './src/main/database/mtpTemplateManager.ts'
            });

            throw error;
        }
    }

    // 更新模板
    static async updateTemplate(id: string, data: { name?: string; plateData?: PlateData }): Promise<PlateTemplate> {
        try {
            const updateData: { name?: string; plateData?: string } = {};
            if (data.name !== undefined) updateData.name = data.name;
            if (data.plateData !== undefined) updateData.plateData = JSON.stringify(data.plateData);

            const template = await prisma.plateTemplate.update({
                where: { id },
                data: updateData
            });
            return template;
        } catch (error) {
            logger.error('update template error:', error, {
                component: './src/main/database/mtpTemplateManager.ts'
            });

            throw error;
        }
    }

    // 删除模板
    static async deleteTemplate(id: string): Promise<void> {
        try {
            await prisma.plateTemplate.delete({
                where: { id }
            });
        } catch (error) {
            logger.error('delete template error:', error, {
                component: './src/main/database/mtpTemplateManager.ts'
            });

            throw error;
        }
    }

    // 解析模板数据
    static parsePlateData(template: PlateTemplate): PlateData {
        try {
            return JSON.parse(template.plateData) as PlateData;
            // JSON.stringify();
        } catch (error) {
            logger.error('parse plate data error:', error, {
                component: './src/main/database/mtpTemplateManager.ts'
            });

            throw new Error('template data format error');
        }
    }
}
