import { setupTestProjectHandlers } from './testProject';
import { setupTestRecordHandlers } from './testRecord';
import logger from '../utils/logger';
// import { setupDeviceHandlers } from './device';     // 未来扩展
// import { setupFilterHandlers } from './filter';     // 未来扩展
// import { setupSettingsHandlers } from './settings'; // 未来扩展

/**
 * 设置所有数据库相关的IPC处理器
 * 这是数据库模块的统一入口点
 */
export function setupDatabaseHandlers(): void {
    logger.info('setupDatabaseHandlers', { component: './src/main/database/setup.ts' });

    // 项目管理处理器
    setupTestProjectHandlers();
    setupTestRecordHandlers();

    // 未来可以添加其他模块的处理器
    // setupDeviceHandlers();
    // setupFilterHandlers();
    // setupSettingsHandlers();
}
