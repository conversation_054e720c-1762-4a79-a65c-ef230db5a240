/**
 * 项目数据库操作 - 支持JSON结构
 * 使用新的扁平化项目结构
 */

import { ipcMain } from 'electron';
import { IPCChannels } from '@shared/ipcChannels';
import { prisma, handlePrismaError } from './index';
import type { Project, ProjectCreate, ProjectUpdate, ApiResponse } from '@shared/types';
import { ProjectUtils } from '@shared/types/project';

// ==================== CRUD 操作 ====================

// 获取项目列表
export async function getProjectList(): Promise<ApiResponse<Project[]>> {
    try {
        const dbProjects = await prisma.project.findMany({
            orderBy: { createdAt: 'desc' }
        });
        // 转换数据库记录为扁平化的项目对象
        const projects = dbProjects.map((dbProject) => ProjectUtils.fromDbRecord(dbProject));
        return { success: true, data: projects };
    } catch (error: unknown) {
        return handlePrismaError<Project[]>(error, '获取项目列表');
    }
}

// 添加项目
export async function addProject(projectData: ProjectCreate): Promise<ApiResponse<Project>> {
    try {
        // 创建完整的项目对象
        const project: Project = {
            id: '', // 数据库会自动生成
            ...projectData,
            version: 1,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        // 序列化为数据库记录
        const dbRecord = ProjectUtils.toDbRecord(project);

        const dbResult = await prisma.project.create({
            data: {
                name: dbRecord.name,
                code: dbRecord.code,
                infoJson: dbRecord.infoJson,
                version: dbRecord.version
            }
        });

        // 反序列化为项目对象
        const resultProject = ProjectUtils.fromDbRecord(dbResult);
        return { success: true, data: resultProject };
    } catch (error: unknown) {
        return handlePrismaError<Project>(error, '添加项目');
    }
}

// 删除项目
export async function deleteProject(id: string): Promise<ApiResponse> {
    try {
        await prisma.project.delete({ where: { id } });
        return { success: true };
    } catch (error: unknown) {
        return handlePrismaError(error, '删除项目');
    }
}

// 更新项目
export async function updateProject(projectUpdate: ProjectUpdate): Promise<ApiResponse<Project>> {
    try {
        // 先获取现有项目
        const existingProject = await prisma.project.findUnique({
            where: { id: projectUpdate.id }
        });

        if (!existingProject) {
            return { success: false, error: '项目不存在' };
        }

        // 反序列化现有项目
        const currentProject = ProjectUtils.fromDbRecord(existingProject);

        // 合并更新数据
        const updatedProject: Project = {
            ...currentProject,
            ...projectUpdate,
            updatedAt: new Date()
        };

        // 序列化为数据库记录
        const dbRecord = ProjectUtils.toDbRecord(updatedProject);

        const updateData: {
            name?: string;
            code?: string;
            infoJson?: string;
            updatedAt: Date;
        } = {
            updatedAt: dbRecord.updatedAt
        };

        if (projectUpdate.name !== undefined) {
            updateData.name = dbRecord.name;
        }
        if (projectUpdate.code !== undefined) {
            updateData.code = dbRecord.code;
        }
        // 总是更新infoJson，因为可能更新了其他字段
        updateData.infoJson = dbRecord.infoJson;

        const dbResult = await prisma.project.update({
            where: { id: projectUpdate.id },
            data: updateData
        });

        // 反序列化为项目对象
        const resultProject = ProjectUtils.fromDbRecord(dbResult);
        return { success: true, data: resultProject };
    } catch (error: unknown) {
        return handlePrismaError<Project>(error, '更新项目');
    }
}

// IPC 处理器设置
export function setupTestProjectHandlers(): void {
    ipcMain.handle(IPCChannels.configInfo.GetProjectList, () => getProjectList());
    ipcMain.handle(IPCChannels.configInfo.AddProject, (_, project) => addProject(project));
    ipcMain.handle(IPCChannels.configInfo.DeleteProject, (_, id) => deleteProject(id));
    ipcMain.handle(IPCChannels.configInfo.UpdateProject, (_, project: Project) => updateProject(project));
}
