import { ipcMain } from 'electron';
import { IPCChannels } from '@shared/ipcChannels';
import { prisma, handlePrismaError } from './index';
import type { ApiResponse, TestRecord } from '@shared/types';

// 创建检测记录
export async function createTestRecord(testRecord: TestRecord): Promise<ApiResponse<TestRecord>> {
    try {
        const createdRecord = await prisma.testRecord.create({
            data: {
                mtpNumber: testRecord.mtpNumber,
                testDate: testRecord.testDate,
                updateDate: testRecord.updateDate,
                testProjectJson: JSON.stringify(testRecord.testProject),
                testAdditionalInfoJson: JSON.stringify(testRecord.testAdditionalInfo),
                cutOffValue: testRecord.cutOffValue ?? null,
                wellDataJson: testRecord.wellData ? JSON.stringify(testRecord.wellData) : null
            }
        });

        return { success: true, data: parseTestRecordFromDb(createdRecord) };
    } catch (error) {
        return handlePrismaError<TestRecord>(error, '创建检测记录');
    }
}

// 获取所有检测记录
export async function getAllTestRecords(): Promise<ApiResponse<TestRecord[]>> {
    try {
        const records = await prisma.testRecord.findMany({
            orderBy: { testDate: 'desc' }
        });

        const parsedRecords = records.map(parseTestRecordFromDb);
        return { success: true, data: parsedRecords };
    } catch (error) {
        return handlePrismaError<TestRecord[]>(error, '获取检测记录列表');
    }
}

// 根据ID获取检测记录
export async function getTestRecordById(id: string): Promise<ApiResponse<TestRecord>> {
    try {
        const record = await prisma.testRecord.findUnique({
            where: { id }
        });

        if (!record) {
            return { success: false, error: '检测记录不存在' };
        }

        return { success: true, data: parseTestRecordFromDb(record) };
    } catch (error) {
        return handlePrismaError<TestRecord>(error, '获取检测记录');
    }
}

// 根据酶标板编号获取检测记录（支持模糊查询）
export async function getTestRecordByMtpNumber(
    mtpNumber: string
): Promise<ApiResponse<TestRecord[]>> {
    try {
        // 移除通配符并转换为小写进行不区分大小写的查询
        const searchTerm = mtpNumber.replace(/%/g, '').toLowerCase();

        const records = await prisma.testRecord.findMany({
            where: {
                mtpNumber: {
                    contains: searchTerm
                }
            },
            orderBy: { testDate: 'desc' }
        });

        // 在应用层进行不区分大小写的过滤
        const filteredRecords = records.filter((record) =>
            record.mtpNumber.toLowerCase().includes(searchTerm)
        );

        const parsedRecords = filteredRecords.map(parseTestRecordFromDb);
        return { success: true, data: parsedRecords };
    } catch (error) {
        return handlePrismaError<TestRecord[]>(error, '根据酶标板编号获取检测记录');
    }
}

// 根据检测日期获取检测记录
export async function getTestRecordByTestDate(
    startDate: string,
    endDate: string
): Promise<ApiResponse<TestRecord[]>> {
    try {
        // 将字符串日期转换为 DateTime 对象
        // 确保开始日期从 00:00:00 开始，结束日期到 23:59:59 结束
        const startDateTime = new Date(startDate + 'T00:00:00.000Z');
        const endDateTime = new Date(endDate + 'T23:59:59.999Z');

        const records = await prisma.testRecord.findMany({
            where: {
                testDate: {
                    gte: startDateTime,
                    lte: endDateTime
                }
            },
            orderBy: { testDate: 'desc' }
        });

        const parsedRecords = records.map(parseTestRecordFromDb);
        return { success: true, data: parsedRecords };
    } catch (error) {
        return handlePrismaError<TestRecord[]>(error, '根据检测日期获取检测记录');
    }
}

// 更新检测记录
export async function updateTestRecord(
    id: string,
    testRecord: Partial<TestRecord>
): Promise<ApiResponse<TestRecord>> {
    try {
        const updateData: Record<string, unknown> = {};

        if (testRecord.mtpNumber !== undefined) updateData.mtpNumber = testRecord.mtpNumber;
        if (testRecord.testDate !== undefined) updateData.testDate = testRecord.testDate;
        if (testRecord.updateDate !== undefined) updateData.updateDate = testRecord.updateDate;
        if (testRecord.testProject !== undefined)
            updateData.testProjectJson = JSON.stringify(testRecord.testProject);
        if (testRecord.testAdditionalInfo !== undefined)
            updateData.testAdditionalInfoJson = JSON.stringify(testRecord.testAdditionalInfo);
        if (testRecord.cutOffValue !== undefined) updateData.cutOffValue = testRecord.cutOffValue;
        if (testRecord.wellData !== undefined)
            updateData.wellDataJson = testRecord.wellData
                ? JSON.stringify(testRecord.wellData)
                : null;

        const updatedRecord = await prisma.testRecord.update({
            where: { id },
            data: updateData
        });

        return { success: true, data: parseTestRecordFromDb(updatedRecord) };
    } catch (error) {
        return handlePrismaError<TestRecord>(error, '更新检测记录');
    }
}

// 删除检测记录
export async function deleteTestRecord(id: string): Promise<ApiResponse<void>> {
    try {
        await prisma.testRecord.delete({
            where: { id }
        });

        return { success: true };
    } catch (error) {
        return handlePrismaError<void>(error, '删除检测记录');
    }
}

// 批量删除检测记录
export async function deleteTestRecords(ids: string[]): Promise<ApiResponse<void>> {
    try {
        await prisma.testRecord.deleteMany({
            where: { id: { in: ids } }
        });

        return { success: true };
    } catch (error) {
        return handlePrismaError<void>(error, '批量删除检测记录');
    }
}

// 从数据库记录解析为TestRecord对象
function parseTestRecordFromDb(dbRecord: {
    id: string;
    mtpNumber: string;
    testDate: Date;
    updateDate: Date;
    testProjectJson: string;
    testAdditionalInfoJson: string;
    cutOffValue: number | null;
    wellDataJson: string | null;
}): TestRecord {
    return {
        id: dbRecord.id,
        mtpNumber: dbRecord.mtpNumber,
        testDate: dbRecord.testDate,
        updateDate: dbRecord.updateDate,
        testProject: JSON.parse(dbRecord.testProjectJson),
        testAdditionalInfo: JSON.parse(dbRecord.testAdditionalInfoJson),
        cutOffValue: dbRecord.cutOffValue ?? 0.1,
        wellData: dbRecord.wellDataJson ? JSON.parse(dbRecord.wellDataJson) : undefined
    };
}

// IPC 处理器设置
export function setupTestRecordHandlers(): void {
    ipcMain.handle(IPCChannels.configInfo.GetTestRecordList, () => getAllTestRecords());
    ipcMain.handle(IPCChannels.configInfo.AddTestRecord, (_, testRecord: TestRecord) =>
        createTestRecord(testRecord)
    );
    ipcMain.handle(IPCChannels.configInfo.DeleteTestRecord, (_, id: string) =>
        deleteTestRecord(id)
    );
    ipcMain.handle(IPCChannels.configInfo.UpdateTestRecord, (_, testRecord: TestRecord) =>
        updateTestRecord(testRecord.id, testRecord)
    );
    ipcMain.handle(IPCChannels.configInfo.GetTestRecordById, (_, id: string) =>
        getTestRecordById(id)
    );
    ipcMain.handle(IPCChannels.configInfo.GetTestRecordByMtpNumber, (_, mtpNumber: string) =>
        getTestRecordByMtpNumber(mtpNumber)
    );
    ipcMain.handle(
        IPCChannels.configInfo.GetTestRecordByTestDate,
        (_, startDate: string, endDate: string) => getTestRecordByTestDate(startDate, endDate)
    );
}
