import { ipcMain } from 'electron';
import { IPCChannels } from '@shared/ipcChannels';
import logger from './utils/logger';

export function setupLogHandlers(): void {
    logger.info('setupLogHandlers', { component: './src/main/log.ts' });
    ipcMain.handle(
        IPCChannels.LOG.Send,
        async (
            _,
            args: {
                level: string;
                message: string;
                component?: string;
                data?: unknown;
                error?: Error | unknown;
            }
        ) => {
            const { level, message, component, data, error } = args;

            // 构建日志元数据
            const meta = {
                component,
                data
            };

            // 使用对应的日志级别记录
            switch (level) {
                case 'error':
                    logger.error(message, error, meta);
                    break;
                case 'warn':
                    logger.warn(message, meta);
                    break;
                case 'log':
                    // log 级别在开发环境使用 debug，生产环境使用 info
                    if (process.env.NODE_ENV === 'development') {
                        logger.debug(message, meta);
                    } else {
                        logger.info(message, meta);
                    }
                    break;
                case 'info':
                    logger.info(message, meta);
                    break;
                case 'debug':
                    logger.debug(message, meta);
                    break;
                default:
                    logger.info(message, meta);
            }

            return { success: true };
        }
    );
}
