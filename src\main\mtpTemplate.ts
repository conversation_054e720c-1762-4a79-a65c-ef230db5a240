import { ipcMain } from 'electron';
import { IPCChannels } from '@shared/ipcChannels';
import { MtpTemplateManager } from './database/mtpTemplateManager';
import { PlateData } from '@shared/types/plateData';
import logger from './utils/logger';

export function setupMtpTemplateHandlers(): void {
    logger.info('setupMtpTemplateHandlers', { component: './src/main/mtpTemplate.ts' });

    // 创建模板
    ipcMain.handle(
        IPCChannels.configInfo.AddMtpTemplate,
        async (
            _,
            data: {
                name: string;
                plateData: PlateData;
                createdBy?: string;
            }
        ) => {
            try {
                const template = await MtpTemplateManager.addTemplate(data);
                return { success: true, data: template };
            } catch (error) {
                logger.error('创建模板失败', error, {
                    component: './src/main/templateHandlers.ts'
                });
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '创建模板失败'
                };
            }
        }
    );

    // 获取所有模板
    ipcMain.handle(IPCChannels.configInfo.GetAllMtpTemplates, async () => {
        try {
            const templates = await MtpTemplateManager.getAllTemplates();
            return { success: true, data: templates };
        } catch (error) {
            logger.error('获取模板列表失败', error, {
                component: './src/main/templateHandlers.ts'
            });
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取模板列表失败'
            };
        }
    });

    // 根据ID获取模板
    ipcMain.handle(IPCChannels.configInfo.GetMtpTemplateById, async (_, id: string) => {
        try {
            const template = await MtpTemplateManager.getTemplateById(id);
            if (!template) {
                return { success: false, error: '模板不存在' };
            }
            return { success: true, data: template };
        } catch (error) {
            logger.error('获取模板失败', error, { component: './src/main/templateHandlers.ts' });
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取模板失败'
            };
        }
    });

    // 更新模板
    ipcMain.handle(
        IPCChannels.configInfo.UpdateMtpTemplate,
        async (
            _,
            id: string,
            data: {
                name?: string;
                plateData?: PlateData;
            }
        ) => {
            try {
                const template = await MtpTemplateManager.updateTemplate(id, data);
                return { success: true, data: template };
            } catch (error) {
                logger.error('更新模板失败', error, {
                    component: './src/main/templateHandlers.ts'
                });
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '更新模板失败'
                };
            }
        }
    );

    // 删除模板
    ipcMain.handle(IPCChannels.configInfo.DeleteMtpTemplate, async (_, id: string) => {
        try {
            await MtpTemplateManager.deleteTemplate(id);
            return { success: true };
        } catch (error) {
            logger.error('删除模板失败', error, { component: './src/main/templateHandlers.ts' });
            return {
                success: false,
                error: error instanceof Error ? error.message : '删除模板失败'
            };
        }
    });
}
