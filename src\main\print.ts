import { ipc<PERSON><PERSON>, BrowserWindow, dialog, Menu } from 'electron';
import logger from './utils/logger';
import fs from 'fs';
import { ReportTemplateEngine } from './utils/reportTemplateEngine';
import { ExcelExporter } from './utils/excelExport';
import { CsvExporter } from './utils/csvExport';
import { LisExporter } from './utils/lisExport';
import type { PrintRequest, PrintResult } from '@shared/types';
import { IPCChannels } from '@shared/ipcChannels';
import { app } from 'electron';

export function setupPrintHandlers(): void {
    logger.info('setupPrintHandlers', { component: './src/main/print.ts' });

    // 获取打印机列表
    ipcMain.handle(IPCChannels.PRINT.GetPrinters, async () => {
        try {
            const mainWindow = BrowserWindow.getAllWindows()[0];
            if (!mainWindow) {
                throw new Error('resultData.messages.mainWindowNotExist');
            }

            const printers = await mainWindow.webContents.getPrintersAsync();
            return printers.map((printer) => printer.name);
        } catch (error) {
            logger.error('获取打印机列表失败', error, { component: './src/main/print.ts' });
            return [];
        }
    });

    // 打印预览
    ipcMain.handle(IPCChannels.PRINT.PrintPreview, async (_, data: PrintRequest) => {
        try {
            // const i18n = I18nManager.getInstance();
            const templateEngine = new ReportTemplateEngine();
            const htmlContent = templateEngine.render(data);

            // 创建预览窗口
            const previewWindow = new BrowserWindow({
                width: 1200,
                height: 1000,
                minWidth: 800,
                minHeight: 600,
                webPreferences: {
                    nodeIntegration: false,
                    contextIsolation: true
                },
                title: 'Print Preview' + ` - ${data.testRecord.mtpNumber}`,
                show: false,
                alwaysOnTop: true // 窗口置顶显示
            });

            // 设置菜单栏
            const menu = Menu.buildFromTemplate([
                {
                    label: '🖨️',
                    submenu: [
                        {
                            label: '🖨️',
                            accelerator: 'CmdOrCtrl+P',
                            click: () => {
                                previewWindow.webContents.print(
                                    {
                                        silent: false,
                                        printBackground: true,
                                        color: true,
                                        margins: { marginType: 'printableArea' as const },
                                        landscape: false,
                                        pagesPerSheet: 1,
                                        collate: false,
                                        copies: 1
                                    },
                                    (success, errorType) => {
                                        if (success) {
                                            logger.info('打印成功', {
                                                component: './src/main/print.ts'
                                            });
                                        } else {
                                            logger.error('打印失败', errorType, {
                                                component: './src/main/print.ts'
                                            });
                                        }
                                        previewWindow.close();
                                    }
                                );
                            }
                        },
                        {
                            label: '💾',
                            accelerator: 'CmdOrCtrl+S',
                            click: async () => {
                                try {
                                    const pdfBuffer = await previewWindow.webContents.printToPDF({
                                        printBackground: true,
                                        margins: { marginType: 'printableArea' },
                                        landscape: false,
                                        pageSize: 'A4'
                                    });

                                    const result = await dialog.showSaveDialog(previewWindow, {
                                        title: '💾 PDF',
                                        defaultPath:
                                            app.getPath('documents') +
                                            `\\ELISA_${data.testRecord.mtpNumber}.pdf`,
                                        filters: [{ name: 'PDF文件', extensions: ['pdf'] }]
                                    });

                                    if (!result.canceled && result.filePath) {
                                        fs.writeFileSync(result.filePath, pdfBuffer);
                                        logger.info('PDF保存成功', {
                                            component: './src/main/print.ts',
                                            filePath: result.filePath
                                        });
                                    }
                                } catch (error) {
                                    logger.error('保存PDF失败', error, {
                                        component: './src/main/print.ts'
                                    });
                                }
                            }
                        },
                        { type: 'separator' },
                        {
                            label: '❌',
                            accelerator: 'CmdOrCtrl+W',
                            click: () => previewWindow.close()
                        }
                    ]
                }
                //  ,
                // {
                //     label: 'View',
                //     submenu: [
                //         { label: '🔍 ', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
                //         { label: '🔍➕ ', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
                //         { label: '🔍➖ ', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
                //         { type: 'separator' },
                //         { label: '⛶ ', accelerator: 'F11', role: 'togglefullscreen' }
                //     ]
                // }
            ]);

            previewWindow.setMenu(menu);

            // 加载HTML内容
            await previewWindow.loadURL(
                `data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`
            );

            // 窗口准备好后显示
            previewWindow.once('ready-to-show', () => {
                previewWindow.show();
            });

            // 监听预览窗口关闭事件
            previewWindow.on('closed', () => {
                logger.info('打印预览窗口已关闭', { component: './src/main/print.ts' });
            });

            const result: PrintResult = {
                success: true,
                message: '打印预览已打开'
            };

            return result;
        } catch (error) {
            logger.error('打印预览失败', error, { component: './src/main/print.ts' });
            const result: PrintResult = {
                success: false,
                message: error instanceof Error ? error.message : '未知错误'
            };
            return result;
        }
    });

    // 生成PDF
    ipcMain.handle(
        IPCChannels.PRINT.PrintToPdf,
        async (_, data: PrintRequest, options?: Electron.PrintToPDFOptions) => {
            try {
                const mainWindow = BrowserWindow.getAllWindows()[0];
                if (!mainWindow) {
                    throw new Error('主窗口不存在');
                }

                const templateEngine = new ReportTemplateEngine();
                const htmlContent = templateEngine.render(data);

                // 创建临时窗口来生成PDF
                const tempWindow = new BrowserWindow({
                    show: false,
                    webPreferences: {
                        nodeIntegration: false,
                        contextIsolation: true
                    }
                });

                // 加载HTML内容
                await tempWindow.loadURL(
                    `data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`
                );

                // 等待页面加载完成
                await new Promise((resolve) => setTimeout(resolve, 1000));

                // 生成PDF
                const pdfBuffer = await tempWindow.webContents.printToPDF(
                    options || {
                        printBackground: true,
                        margins: { marginType: 'printableArea' },
                        landscape: false,
                        pageSize: 'A4'
                    }
                );

                // 关闭临时窗口
                tempWindow.close();

                // 保存PDF文件
                const result = await dialog.showSaveDialog(mainWindow, {
                    title: '保存PDF文件',
                    defaultPath:
                        app.getPath('documents') + `\\ELISA报告_${data.testRecord.mtpNumber}.pdf`,
                    filters: [{ name: 'PDF文件', extensions: ['pdf'] }]
                });

                if (!result.canceled && result.filePath) {
                    fs.writeFileSync(result.filePath, pdfBuffer);

                    const printResult: PrintResult = {
                        success: true,
                        message: 'PDF生成成功',
                        data: {
                            filePath: result.filePath
                        }
                    };
                    return printResult;
                } else {
                    const printResult: PrintResult = {
                        success: false,
                        message: '用户取消了保存'
                    };
                    return printResult;
                }
            } catch (error) {
                logger.error('生成PDF失败', error, { component: './src/main/print.ts' });
                const result: PrintResult = {
                    success: false,
                    message: error instanceof Error ? error.message : '未知错误'
                };
                return result;
            }
        }
    );

    // 导出到Excel
    ipcMain.handle(IPCChannels.PRINT.ExportToExcel, async (_, data: PrintRequest) => {
        try {
            const excelExporter = new ExcelExporter();
            const result = await excelExporter.exportToExcel(data);
            return result;
        } catch (error) {
            logger.error('导出Excel失败', error, { component: './src/main/print.ts' });
            const result: PrintResult = {
                success: false,
                message: error instanceof Error ? error.message : '未知错误'
            };
            return result;
        }
    });

    // 导出到CSV
    ipcMain.handle(IPCChannels.PRINT.ExportToCsv, async (_, data: PrintRequest) => {
        try {
            const csvExporter = new CsvExporter();
            const result = await csvExporter.exportToCsv(data);
            return result;
        } catch (error) {
            logger.error('导出CSV失败', error, { component: './src/main/print.ts' });
            const result: PrintResult = {
                success: false,
                message: error instanceof Error ? error.message : '未知错误'
            };
            return result;
        }
    });

    // 导出到LIS
    ipcMain.handle(IPCChannels.PRINT.ExportToLIS, async (_, data: PrintRequest) => {
        try {
            const lisExporter = new LisExporter();
            const result = await lisExporter.exportToLIS(data);
            return result;
        } catch (error) {
            logger.error('导出LIS失败', error, { component: './src/main/print.ts' });
            const result: PrintResult = {
                success: false,
                message: error instanceof Error ? error.message : '未知错误'
            };
            return result;
        }
    });

    // 直接打印
    ipcMain.handle(
        IPCChannels.PRINT.Print,
        async (_, data: PrintRequest, options?: Electron.WebContentsPrintOptions) => {
            try {
                const templateEngine = new ReportTemplateEngine();
                const htmlContent = templateEngine.render(data);

                // 创建临时窗口来打印
                const tempWindow = new BrowserWindow({
                    show: false,
                    webPreferences: {
                        nodeIntegration: false,
                        contextIsolation: true
                    }
                });

                // 加载HTML内容
                await tempWindow.loadURL(
                    `data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`
                );

                // 等待页面加载完成
                await new Promise((resolve) => setTimeout(resolve, 1000));

                // 获取所有打印机并输出日志
                const printers = await tempWindow.webContents.getPrintersAsync();
                logger.info('可用打印机: ' + JSON.stringify(printers));
                const defaultPrinter = printers.find((p) => p.isDefault);
                logger.info('默认打印机: ' + JSON.stringify(defaultPrinter));

                if (!defaultPrinter) {
                    tempWindow.close();
                    return {
                        success: false,
                        message: '未找到默认打印机.'
                    };
                }

                const printOptions = {
                    ...options,
                    silent: true,
                    printBackground: true,
                    color: true,
                    deviceName: defaultPrinter.name,
                    margins: { marginType: 'printableArea' as const },
                    landscape: false,
                    pagesPerSheet: 1,
                    collate: false,
                    copies: 1
                };

                tempWindow.webContents.print(printOptions, (success, errorType) => {
                    if (success) {
                        logger.info('打印成功', { component: './src/main/print.ts' });
                    } else {
                        logger.error('打印失败', errorType, { component: './src/main/print.ts' });
                    }
                    tempWindow.close();
                });

                return { success: true, message: '打印成功' };
            } catch (error) {
                logger.error('打印失败', error, { component: './src/main/print.ts' });
                return {
                    success: false,
                    message: error instanceof Error ? error.message : '未知错误'
                };
            }
        }
    );
}
