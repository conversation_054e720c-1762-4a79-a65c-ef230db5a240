import { ipc<PERSON>ain } from 'electron';
import { IPCChannels } from '@shared/ipcChannels';
import { PortsList, SerialPortOptions } from '@shared/types';
import serialManager from './utils/serialManager';
import logger from './utils/logger';

export async function setupSerialHandlers(): Promise<void> {
    logger.info('setupSerialHandlers', { component: './src/main/serial.ts' });

    ipcMain.handle(IPCChannels.SERIAL.GetSerialPorts, async (): Promise<PortsList[]> => {
        try {
            return await serialManager.listPorts();
        } catch (error) {
            logger.error('获取串口列表失败', error, { component: './src/main/serial.ts' });
            throw error;
        }
    });

    ipcMain.handle(
        IPCChannels.SERIAL.OpenSerialPort,
        async (_, options: SerialPortOptions): Promise<boolean> => {
            try {
                return await serialManager.openPort(options);
            } catch (error) {
                logger.error('打开串口失败', error, { component: './src/main/serial.ts' });
                throw error;
            }
        }
    );

    ipcMain.handle(IPCChannels.SERIAL.CloseSerialPort, async (): Promise<boolean> => {
        try {
            return await serialManager.closePort();
        } catch (error) {
            logger.error('关闭串口失败', error, { component: './src/main/serial.ts' });
            throw error;
        }
    });

    ipcMain.handle(IPCChannels.SERIAL.SendData, async (_, data: string): Promise<boolean> => {
        try {
            return await serialManager.sendData(data);
        } catch (error) {
            logger.error('发送数据失败', error, { component: './src/main/serial.ts' });
            throw error;
        }
    });

    ipcMain.handle(
        IPCChannels.SERIAL.ReceiveData,
        async (_, timeoutMs: number): Promise<string> => {
            try {
                return await serialManager.receiveRawData(timeoutMs);
            } catch (error) {
                logger.error('接收数据失败', error, { component: './src/main/serial.ts' });
                throw error;
            }
        }
    );
}
