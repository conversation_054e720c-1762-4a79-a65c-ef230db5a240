import { ipcMain, app } from 'electron';
import path from 'path';
import ElectronStore from 'electron-store';
import { IPCChannels } from '@shared/ipcChannels';
import logger from './utils/logger';

const SETTINGS_DIR = path.join(app.getPath('userData'), 'config');

// 创建 Store 实例
export const store = new ElectronStore({
    cwd: SETTINGS_DIR,
    name: 'system_settings'
});

console.log('Store path:', store.path);

// 注册 IPC 处理器
export function setupStoreHandlers(): void {
    logger.info('setupStoreHandlers', { component: './src/main/store.ts' });

    // 存储相关处理器
    ipcMain.handle(IPCChannels.STORE.Get, async (_, { key }) => {
        try {
            const value = store.get(key);
            return { success: true, data: value };
        } catch (error: unknown) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    });

    ipcMain.handle(IPCChannels.STORE.Set, async (_, { key, value }) => {
        try {
            store.set(key, value);
            return { success: true };
        } catch (error: unknown) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    });

    ipcMain.handle(IPCChannels.STORE.Delete, async (_, { key }) => {
        try {
            store.delete(key);
            return { success: true };
        } catch (error: unknown) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    });
}
