import { is } from '@electron-toolkit/utils';
import path from 'path';
import fs from 'fs';
import type { DeviceConfig, DeviceConfigRaw } from '@shared/types';
import { convertToDeviceConfigs } from '@shared/commondefines';
import logger from './logger';

export class ConfigLoader {
    private static instance: ConfigLoader;
    private deviceConfigRaw: DeviceConfigRaw[] = [];
    private deviceConfig: DeviceConfig[] = [];

    private constructor() {}

    // 单例模式，确保只加载一次
    public static async getInstance(): Promise<ConfigLoader> {
        if (!ConfigLoader.instance) {
            ConfigLoader.instance = new ConfigLoader();
        }
        await ConfigLoader.instance.loadConfig();
        return ConfigLoader.instance;
    }

    private getConfigPath(): string {
        const configName = 'setting.json';

        if (is.dev) {
            // 开发环境：从项目根目录读取
            return path.join(__dirname, '../../resources/config', configName);
        } else {
            // 生产环境：从应用资源目录读取
            return path.join(process.resourcesPath, 'config', configName);
        }
    }

    // 加载配置（只在启动时调用一次）
    public async loadConfig(): Promise<DeviceConfig[] | null> {
        // 只有当deviceConfig存在且有内容时才直接返回
        if (this.deviceConfig && this.deviceConfig.length > 0) return this.deviceConfig;

        try {
            const configPath = this.getConfigPath();
            logger.info('设备配置文件路径:', {
                data: { path: configPath },
                component: './src/main/utils/configLoader.ts'
            });

            const jsonString = await fs.promises.readFile(configPath, 'utf-8');
            logger.info('配置文件内容:', {
                data: jsonString,
                component: './src/main/utils/configLoader.ts'
            });

            this.deviceConfigRaw = JSON.parse(jsonString) as DeviceConfigRaw[];
            // logger.info('原始设备配置', {
            //     data: JSON.stringify(this.deviceConfigRaw, null, 2),
            //     component: './src/main/utils/configLoader.ts'
            // });

            this.deviceConfig = convertToDeviceConfigs(this.deviceConfigRaw);
            // logger.info('处理后的设备配置', {
            //     data: JSON.stringify(this.deviceConfig, null, 2),
            //     component: './src/main/utils/configLoader.ts'
            // });

            return this.deviceConfig;
        } catch (error) {
            logger.error('配置加载失败', error, { component: './src/main/utils/configLoader.ts' });
            throw error;
        }
    }

    // 获取配置（随时调用）
    public getConfig(): DeviceConfig[] | null {
        if (!this.deviceConfig) {
            throw new Error('config not loaded');
        }
        return this.deviceConfig;
    }
}
