import { dialog } from 'electron';
import fs from 'fs';
import { PrintRequest, PrintResult, TestRecord } from '@shared/types';
import logger from './logger';
import { app } from 'electron';

export class CsvExporter {
    /**
     * 导出ELISA数据到CSV
     */
    public async exportToCsv(data: PrintRequest): Promise<PrintResult> {
        try {
            const { testRecord, options } = data;
            const lang = options.language || 'zh';
            const csvContent = this.generateCsvContent(testRecord, lang);

            // 保存文件
            const result = await this.saveFile(testRecord, csvContent);
            return result;
        } catch (error) {
            logger.error('CSV导出失败', error, { component: './src/main/utils/csvExport.ts' });
            return {
                success: false,
                message: error instanceof Error ? error.message : 'CSV导出失败'
            };
        }
    }

    /**
     * 生成CSV内容
     */
    private generateCsvContent(record: TestRecord, lang: 'zh' | 'en'): string {
        // 表头
        const headers = [
            lang === 'zh' ? '孔位' : 'Well',
            lang === 'zh' ? '样本类型' : 'Sample Type',
            lang === 'zh' ? '样本编号' : 'Sample Number',
            lang === 'zh' ? '主波长OD' : 'Main OD',
            lang === 'zh' ? '参考OD' : 'Ref OD',
            lang === 'zh' ? '计算OD' : 'Calc OD',
            lang === 'zh' ? 'OD比值' : 'OD Ratio',
            lang === 'zh' ? '浓度' : 'Concentration',
            lang === 'zh' ? '结果' : 'Result'
        ];
        const rows: string[][] = [headers];

        if (record.wellData) {
            const rowNames = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
            const colNums = Array.from({ length: 12 }, (_, i) => i + 1);
            rowNames.forEach((row) => {
                colNums.forEach((col) => {
                    const wellId = `${row}${col}`;
                    const wellData = record.wellData![wellId];
                    if (wellData) {
                        rows.push([
                            wellId,
                            wellData.sampleType.name || '',
                            wellData.sampleNumber || '',
                            wellData.odMain?.toFixed(4) || '',
                            wellData.odRef?.toFixed(4) || '',
                            wellData.odValue?.toFixed(4) || '',
                            wellData.odRatio?.toFixed(4) || '',
                            wellData.concentration?.toFixed(2) || '',
                            wellData.result === 1 ? '阳性' : wellData.result === 0 ? '阴性' : wellData.result === 2 ? '灰区' : ''
                        ]);
                    }
                });
            });
        }
        // 转为CSV字符串
        return rows.map((row) => row.map((cell) => `"${cell.replace(/"/g, '""')}"`).join(',')).join('\r\n');
    }

    /**
     * 保存文件
     */
    private async saveFile(record: TestRecord, csvContent: string): Promise<PrintResult> {
        try {
            const { BrowserWindow } = await import('electron');
            const mainWindow = BrowserWindow.getAllWindows()[0];
            if (!mainWindow) {
                throw new Error('主窗口不存在');
            }
            // 生成文件名
            const fileName = app.getPath('documents') + `\\ELISA_Data_${record.testProject.name}(${record.testProject.code})_${record.mtpNumber}.csv`;
            const result = await dialog.showSaveDialog(mainWindow, {
                title: '保存CSV文件',
                defaultPath: fileName,
                filters: [
                    { name: 'CSV文件', extensions: ['csv'] },
                    { name: '所有文件', extensions: ['*'] }
                ]
            });
            if (!result.canceled && result.filePath) {
                // 写入UTF-8 BOM，防止Excel乱码
                const BOM = '\uFEFF';
                fs.writeFileSync(result.filePath, BOM + csvContent, { encoding: 'utf8' });
                logger.info('CSV文件保存成功', {
                    filePath: result.filePath,
                    component: './src/main/utils/csvExport.ts'
                });
                return {
                    success: true,
                    message: 'CSV文件保存成功',
                    data: {
                        filePath: result.filePath
                    }
                };
            } else {
                return {
                    success: false,
                    message: '用户取消了保存'
                };
            }
        } catch (error) {
            logger.error('保存CSV文件失败', error, { component: './src/main/utils/csvExport.ts' });
            throw error;
        }
    }
}
