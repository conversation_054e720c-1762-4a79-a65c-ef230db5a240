import ExcelJS from 'exceljs';
import { dialog } from 'electron';
import fs from 'fs';
import { PrintRequest, PrintResult, TestRecord } from '@shared/types';
import logger from './logger';
import { app } from 'electron';

export class ExcelExporter {
    private workbook: ExcelJS.Workbook;

    constructor() {
        this.workbook = new ExcelJS.Workbook();
        this.workbook.creator = 'KHB ST ELISA System';
        this.workbook.lastModifiedBy = 'KHB ST ELISA System';
        this.workbook.created = new Date();
        this.workbook.modified = new Date();
    }

    /**
     * 导出ELISA数据到Excel
     */
    public async exportToExcel(data: PrintRequest): Promise<PrintResult> {
        try {
            const { testRecord, options } = data;
            const lang = options.language || 'zh';

            // 创建工作表
            await this.createBasicInfoSheet(testRecord, lang);
            await this.createPlateDataSheet(testRecord, lang);
            await this.createStatisticsSheet(testRecord, lang);
            await this.createRawDataSheet(testRecord, lang);

            // 保存文件
            const result = await this.saveFile(testRecord);
            return result;
        } catch (error) {
            logger.error('Excel导出失败', error, { component: './src/main/utils/excelExport.ts' });
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Excel导出失败'
            };
        }
    }

    /**
     * 创建基本信息工作表
     */
    private async createBasicInfoSheet(record: TestRecord, lang: 'zh' | 'en'): Promise<void> {
        const worksheet = this.workbook.addWorksheet(lang === 'zh' ? '基本信息' : 'Basic Info');

        // 设置列宽
        worksheet.columns = [
            { key: 'label', width: 20 },
            { key: 'value', width: 30 },
            { key: 'label2', width: 20 },
            { key: 'value2', width: 30 }
        ];

        // 添加标题
        const titleRow = worksheet.addRow(['']);
        titleRow.getCell(1).value = lang === 'zh' ? 'ELISA检测报告' : 'ELISA Test Report';
        titleRow.getCell(1).font = { bold: true, size: 16 };
        titleRow.getCell(1).alignment = { horizontal: 'center' };
        worksheet.mergeCells('A1:D1');

        // 添加基本信息
        const basicInfo = [
            [
                { label: lang === 'zh' ? '检测时间' : 'Test Time', value: record.testDate },
                { label: lang === 'zh' ? '微孔板编号' : 'Plate Number', value: record.mtpNumber }
            ],
            [
                { label: lang === 'zh' ? '检测项目' : 'Project', value: record.testProject.name },
                {
                    label: lang === 'zh' ? '项目代码' : 'Project Code',
                    value: record.testProject.code
                }
            ],
            [
                {
                    label: lang === 'zh' ? '检测波长' : 'Test Wavelength',
                    value: `${record.testProject.testWaveName}`
                },
                {
                    label: lang === 'zh' ? '参考波长' : 'Reference Wavelength',
                    value: `${record.testProject.refWaveName}`
                }
            ],
            [
                {
                    label: lang === 'zh' ? '试剂批号' : 'Reagent Batch',
                    value: record.testAdditionalInfo.reagentBatch || 'N/A'
                },
                {
                    label: lang === 'zh' ? '试剂有效期' : 'Reagent Expiry',
                    value: record.testAdditionalInfo.reagentExpiry || 'N/A'
                }
            ],
            [
                {
                    label: lang === 'zh' ? '检测温度' : 'Test Temperature',
                    value: `${record.testAdditionalInfo.testTemperature || 'N/A'}°C`
                },
                {
                    label: lang === 'zh' ? '相对湿度' : 'Relative Humidity',
                    value: `${record.testAdditionalInfo.testRelativeHumidity || 'N/A'}%`
                }
            ],
            [
                {
                    label: lang === 'zh' ? 'Cutoff值' : 'Cutoff Value',
                    value: record.cutOffValue?.toString() || 'N/A'
                },
                {
                    label: lang === 'zh' ? '检测审核者' : 'Test Reviewer',
                    value: record.testAdditionalInfo.testReviewer || 'N/A'
                }
            ]
        ];

        basicInfo.forEach((row) => {
            const excelRow = worksheet.addRow([row[0].label, row[0].value, row[1].label, row[1].value]);

            // 设置标签列样式
            excelRow.getCell(1).font = { bold: true };
            excelRow.getCell(3).font = { bold: true };
        });

        // 添加边框
        this.addBorders(worksheet, 2, basicInfo.length + 1, 4);
    }

    /**
     * 创建96孔板数据工作表
     */
    private async createPlateDataSheet(record: TestRecord, lang: 'zh' | 'en'): Promise<void> {
        const worksheet = this.workbook.addWorksheet(lang === 'zh' ? '96孔板数据' : '96-Well Plate Data');

        // 设置列宽
        const columns = [
            { key: 'row', width: 8 },
            ...Array.from({ length: 12 }, (_, i) => ({ key: `col${i + 1}`, width: 15 }))
        ];
        worksheet.columns = columns;

        // 添加列标题
        const headerRow = worksheet.addRow(['', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']);
        headerRow.eachCell((cell) => {
            cell.font = { bold: true };
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE3F0FC' }
            };
            cell.alignment = { horizontal: 'center' };
        });

        // 添加行标题和数据
        const rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
        rows.forEach((row) => {
            const rowData = [row];

            for (let col = 1; col <= 12; col++) {
                const wellId = `${row}${col}`;
                const wellData = record.wellData?.[wellId];

                if (wellData) {
                    const cellContent = [
                        wellData.sampleNumber || '',
                        wellData.odValue !== undefined ? wellData.odValue.toFixed(4) : '',
                        wellData.concentration !== undefined ? wellData.concentration.toFixed(2) : '',
                        wellData.result || ''
                    ].join('\n');

                    rowData.push(cellContent);
                } else {
                    rowData.push('');
                }
            }

            const excelRow = worksheet.addRow(rowData);

            // 设置行标题样式
            excelRow.getCell(1).font = { bold: true };
            excelRow.getCell(1).fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFF4F8FB' }
            };
            excelRow.getCell(1).alignment = { horizontal: 'center', vertical: 'middle' };

            // 设置数据单元格样式
            for (let col = 2; col <= 13; col++) {
                const cell = excelRow.getCell(col);
                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            }
        });

        // 添加说明
        const noteRow = worksheet.addRow(['']);
        noteRow.getCell(1).value =
            lang === 'zh'
                ? '说明：每格包含样本编号、OD值、浓度、结果，用换行分隔'
                : 'Note: Each cell contains sample number, OD value, concentration, and result, separated by line breaks';
        noteRow.getCell(1).font = { italic: true, size: 10 };
        worksheet.mergeCells(`A${worksheet.rowCount}:M${worksheet.rowCount}`);
    }

    /**
     * 创建统计信息工作表
     */
    private async createStatisticsSheet(record: TestRecord, lang: 'zh' | 'en'): Promise<void> {
        const worksheet = this.workbook.addWorksheet(lang === 'zh' ? '统计信息' : 'Statistics');

        // 设置列宽
        worksheet.columns = [
            { key: 'category', width: 25 },
            { key: 'value', width: 15 },
            { key: 'percentage', width: 15 }
        ];

        // 计算统计数据
        const stats = this.calculateStatistics(record);

        // 添加标题
        const titleRow = worksheet.addRow([lang === 'zh' ? '检测结果统计' : 'Test Result Statistics']);
        titleRow.getCell(1).font = { bold: true, size: 14 };
        worksheet.mergeCells('A1:C1');

        // 添加统计数据
        const statisticsData = [
            {
                category: lang === 'zh' ? '总样本数' : 'Total Samples',
                value: stats.total,
                percentage: '100%'
            },
            {
                category: lang === 'zh' ? '阳性样本数' : 'Positive Samples',
                value: stats.positive,
                percentage: `${stats.positiveRate.toFixed(2)}%`
            },
            {
                category: lang === 'zh' ? '阴性样本数' : 'Negative Samples',
                value: stats.negative,
                percentage: `${stats.negativeRate.toFixed(2)}%`
            },
            {
                category: lang === 'zh' ? '灰区样本数' : 'Gray Zone Samples',
                value: stats.grayZone,
                percentage: `${stats.grayZoneRate.toFixed(2)}%`
            },
            {
                category: lang === 'zh' ? '空白孔数' : 'Blank Wells',
                value: stats.blank,
                percentage: `${stats.blankRate.toFixed(2)}%`
            },
            {
                category: lang === 'zh' ? '标准孔数' : 'Standard Wells',
                value: stats.standard,
                percentage: `${stats.standardRate.toFixed(2)}%`
            },
            {
                category: lang === 'zh' ? '质控孔数' : 'QC Wells',
                value: stats.qc,
                percentage: `${stats.qcRate.toFixed(2)}%`
            }
        ];

        // 添加表头
        const headerRow = worksheet.addRow([
            lang === 'zh' ? '类别' : 'Category',
            lang === 'zh' ? '数量' : 'Count',
            lang === 'zh' ? '百分比' : 'Percentage'
        ]);
        headerRow.eachCell((cell) => {
            cell.font = { bold: true };
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE3F0FC' }
            };
        });

        // 添加数据行
        statisticsData.forEach((item) => {
            const row = worksheet.addRow([item.category, item.value, item.percentage]);
            row.getCell(1).font = { bold: true };
        });

        // 添加边框
        this.addBorders(worksheet, 2, statisticsData.length + 2, 3);
    }

    /**
     * 创建原始数据工作表
     */
    private async createRawDataSheet(record: TestRecord, lang: 'zh' | 'en'): Promise<void> {
        const worksheet = this.workbook.addWorksheet(lang === 'zh' ? '原始数据' : 'Raw Data');

        // 设置列宽
        worksheet.columns = [
            { key: 'wellId', width: 10 },
            { key: 'sampleType', width: 15 },
            { key: 'sampleNumber', width: 15 },
            { key: 'odMain', width: 15 },
            { key: 'odRef', width: 15 },
            { key: 'odValue', width: 15 },
            { key: 'odRatio', width: 15 },
            { key: 'concentration', width: 15 },
            { key: 'result', width: 15 }
        ];

        // 添加表头
        const headers = [
            lang === 'zh' ? '孔位' : 'Well',
            lang === 'zh' ? '样本类型' : 'Sample Type',
            lang === 'zh' ? '样本编号' : 'Sample Number',
            lang === 'zh' ? '主波长OD' : 'Main OD',
            lang === 'zh' ? '参考OD' : 'Ref OD',
            lang === 'zh' ? '计算OD' : 'Calc OD',
            lang === 'zh' ? 'OD比值' : 'OD Ratio',
            lang === 'zh' ? '浓度' : 'Concentration',
            lang === 'zh' ? '结果' : 'Result'
        ];

        const headerRow = worksheet.addRow(headers);
        headerRow.eachCell((cell) => {
            cell.font = { bold: true };
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE3F0FC' }
            };
            cell.alignment = { horizontal: 'center' };
        });

        // 添加数据行
        if (record.wellData) {
            const rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
            const cols = Array.from({ length: 12 }, (_, i) => i + 1);

            rows.forEach((row) => {
                cols.forEach((col) => {
                    const wellId = `${row}${col}`;
                    const wellData = record.wellData![wellId];

                    if (wellData) {
                        const dataRow = worksheet.addRow([
                            wellId,
                            wellData.sampleType.name || '',
                            wellData.sampleNumber || '',
                            wellData.odMain?.toFixed(4) || '',
                            wellData.odRef?.toFixed(4) || '',
                            wellData.odValue?.toFixed(4) || '',
                            wellData.odRatio?.toFixed(4) || '',
                            wellData.concentration?.toFixed(2) || '',
                            wellData.result || ''
                        ]);

                        // 设置数值列格式
                        for (let i = 4; i <= 8; i++) {
                            const cell = dataRow.getCell(i);
                            if (cell.value && typeof cell.value === 'number') {
                                cell.numFmt = '0.0000';
                            }
                        }
                    }
                });
            });
        }

        // 添加边框
        this.addBorders(worksheet, 1, worksheet.rowCount, 9);
    }

    /**
     * 计算统计数据
     */
    private calculateStatistics(record: TestRecord) {
        const stats = {
            total: 0,
            positive: 0,
            negative: 0,
            grayZone: 0,
            blank: 0,
            standard: 0,
            qc: 0
        };

        if (record.wellData) {
            Object.values(record.wellData).forEach((wellData) => {
                stats.total++;

                // 统计样本类型
                const sampleType = wellData.sampleType.name;
                if (sampleType === '') {
                    stats.blank++;
                } else if (sampleType === 'std') {
                    stats.standard++;
                } else if (sampleType === 'qc') {
                    stats.qc++;
                }

                // 统计结果 // 结果 0=阴性 1=阳性 2=灰区
                const result = wellData.result;
                if (result === 1) {
                    stats.positive++;
                } else if (result === 0) {
                    stats.negative++;
                } else {
                    stats.grayZone++;
                }
            });
        }

        // 计算百分比
        const total = stats.total || 1;
        return {
            ...stats,
            positiveRate: (stats.positive / total) * 100,
            negativeRate: (stats.negative / total) * 100,
            grayZoneRate: (stats.grayZone / total) * 100,
            blankRate: (stats.blank / total) * 100,
            standardRate: (stats.standard / total) * 100,
            qcRate: (stats.qc / total) * 100
        };
    }

    /**
     * 添加边框
     */
    private addBorders(worksheet: ExcelJS.Worksheet, startRow: number, endRow: number, endCol: number): void {
        for (let row = startRow; row <= endRow; row++) {
            for (let col = 1; col <= endCol; col++) {
                const cell = worksheet.getCell(row, col);
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            }
        }
    }

    /**
     * 保存文件
     */
    private async saveFile(record: TestRecord): Promise<PrintResult> {
        try {
            const { BrowserWindow } = await import('electron');
            const mainWindow = BrowserWindow.getAllWindows()[0];
            if (!mainWindow) {
                throw new Error('主窗口不存在');
            }

            // 生成文件名
            const fileName =
                app.getPath('documents') +
                `\\ELISA_Data_${record.testProject.name}(${record.testProject.code})_${record.mtpNumber}.xlsx`;

            const result = await dialog.showSaveDialog(mainWindow, {
                title: '保存Excel文件',
                defaultPath: fileName,
                filters: [
                    { name: 'Excel文件', extensions: ['xlsx'] },
                    { name: '所有文件', extensions: ['*'] }
                ]
            });

            if (!result.canceled && result.filePath) {
                const buffer = await this.workbook.xlsx.writeBuffer();
                fs.writeFileSync(result.filePath, Buffer.from(buffer));

                logger.info('Excel文件保存成功', {
                    filePath: result.filePath,
                    component: './src/main/utils/excelExport.ts'
                });

                return {
                    success: true,
                    message: 'Excel文件保存成功',
                    data: {
                        filePath: result.filePath
                    }
                };
            } else {
                return {
                    success: false,
                    message: '用户取消了保存'
                };
            }
        } catch (error) {
            logger.error('保存Excel文件失败', error, {
                component: './src/main/utils/excelExport.ts'
            });
            throw error;
        }
    }
}
