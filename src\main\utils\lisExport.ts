import { BrowserWindow } from 'electron';
import fs from 'fs';
import { PrintRequest, PrintResult, TestRecord } from '@shared/types';
import logger from './logger';
import { store } from '../store';

export class LisExporter {
    /**
     * 导出ELISA数据到LIS格式文本文件
     */
    public async exportToLIS(data: PrintRequest): Promise<PrintResult> {
        try {
            const { testRecord } = data;

            // 生成LIS格式的内容
            const lisContent = this.generateLISContent(testRecord);

            // 保存文件
            const result = await this.saveFile(lisContent);
            return result;
        } catch (error) {
            logger.error('LIS导出失败', error, { component: './src/main/utils/lisExport.ts' });
            return {
                success: false,
                message: error instanceof Error ? error.message : 'LIS导出失败'
            };
        }
    }

    /**
     * 生成LIS格式的内容
     */
    private generateLISContent(record: TestRecord): string {
        const lines: string[] = [];

        // 获取项目信息
        const projectCode = record.testProject.code;
        const projectType = record.testProject.testType;

        // 格式化检测日期
        const testDate = this.formatDate(record.testDate);

        // 遍历所有孔位数据
        const wellIds = this.getWellIds();

        for (const wellId of wellIds) {
            const wellData = record.wellData?.[wellId];

            if (wellData && wellData.sampleNumber) {
                // 获取各项数据
                const sampleNumber = wellData.sampleNumber;
                const odValue = wellData.odValue?.toFixed(4) || '0.0000';
                const odRatio = wellData.odRatio?.toFixed(4) || '0.0000';
                const result = wellData.result === 1 ? '+' : wellData.result === 0 ? '-' : '';

                // 生成LIS格式行：孔号=[检验日期,标本号,项目代码,项目类型,结果,OD值,OD/CUTOFF]
                const line = `${wellId}=[${testDate},${sampleNumber},${projectCode},${projectType},${result},${odValue},${odRatio}]`;
                lines.push(line);
            } else if (wellData) {
                // 如果有孔位数据但没有样本号，使用默认值
                const sampleNumber = '';
                const odValue = wellData.odValue?.toFixed(4) || '0.0000';
                const odRatio = wellData.odRatio?.toFixed(4) || '0.0000';
                const result = wellData.result === 1 ? '+' : wellData.result === 0 ? '-' : '';

                const line = `${wellId}=[${testDate},${sampleNumber},${projectCode},${projectType},${result},${odValue},${odRatio}]`;
                lines.push(line);
            }
        }

        return lines.join('\n');
    }

    /**
     * 获取96孔板的所有孔位ID
     */
    private getWellIds(): string[] {
        const wellIds: string[] = [];
        const rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
        const cols = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];

        for (const row of rows) {
            for (const col of cols) {
                wellIds.push(`${row}${col}`);
            }
        }

        return wellIds;
    }

    /**
     * 格式化日期为YYYYMMDD格式
     */
    private formatDate(date: Date): string {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}${month}${day}`;
    }

    // }

    // /**
    //  * 映射检测结果
    //  */
    // private mapResult(result?: number): string {
    //     if (!result) return 'N';

    //     // 映射各种结果格式
    //     const resultMap: { [key: string]: string } = {
    //         阳性: 'P',
    //         '阳性(+)': 'P',
    //         '+': 'P',
    //         positive: 'P',
    //         阴性: 'N',
    //         '阴性(-)': 'N',
    //         '-': 'N',
    //         negative: 'N'
    //     };

    //     return resultMap[result] || 'N';
    // }

    /**
     * 保存文件
     */
    private async saveFile(content: string): Promise<PrintResult> {
        const LIS_PATH_STORE_KEY = 'app.lisPath';
        try {
            const mainWindow = BrowserWindow.getAllWindows()[0];
            if (!mainWindow) {
                throw new Error('主窗口不存在');
            }

            // 从store获取保存路径
            const lisPath = store.get(LIS_PATH_STORE_KEY) as string;

            console.log('mainWindow lisPath', lisPath);

            if (!lisPath) {
                throw new Error('未配置LIS文件保存路径，请先在设置中配置');
            }

            // 使用配置的路径 + lis.out作为文件名
            const filePath = `${lisPath}\\lis.out`;
            console.log('mainWindow filePath', filePath);

            // 写入文件
            fs.writeFileSync(filePath, content, 'utf-8');

            logger.info('LIS文件保存成功', {
                filePath: filePath,
                component: './src/main/utils/lisExport.ts'
            });

            return {
                success: true,
                message: 'LIS文件导出成功',
                data: {
                    filePath: filePath
                }
            };
        } catch (error) {
            logger.error('保存LIS文件失败', error, { component: './src/main/utils/lisExport.ts' });
            throw error;
        }
    }
}
