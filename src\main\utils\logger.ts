import { app } from 'electron';
import winston from 'winston';
import path from 'path';

// 确保日志目录存在
const LOG_DIR = path.join(app.getPath('userData'), 'logs');

// 自定义格式化函数
const customFormat = winston.format.printf(
    ({ level, message, component, data, error, ...meta }) => {
        const time = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        const componentStr = component ? `[${component}]` : '';
        let dataStr = '';

        // 处理数据对象
        if (data !== undefined) {
            try {
                dataStr = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            } catch {
                dataStr = `[无法序列化的数据: ${typeof data}]`;
            }
        }

        // 处理错误对象
        let errorStr = '';
        if (error) {
            if (error instanceof Error) {
                errorStr = `\nError: ${error.message}\nStack: ${error.stack || '无堆栈信息'}`;
            } else {
                try {
                    errorStr = `\nError: ${JSON.stringify(error, null, 2)}`;
                } catch {
                    errorStr = `\n[无法序列化的错误: ${typeof error}]`;
                }
            }
        }

        // 处理其他元数据
        const otherMeta = Object.keys(meta).length
            ? `\nMeta: ${JSON.stringify(meta, null, 2)}`
            : '';

        return `[${time}][${level.toUpperCase()}]${componentStr} ${message}${dataStr}${errorStr}${otherMeta}`.trim();
    }
);

// 创建 Winston logger 实例
const logger = winston.createLogger({
    level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
    format: winston.format.combine(winston.format.errors({ stack: true }), customFormat),
    transports: [
        // 错误日志文件
        new winston.transports.File({
            filename: path.join(LOG_DIR, 'error.log'),
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5,
            options: { flags: 'a' },
            tailable: true,
            zippedArchive: false
        }),
        // 所有日志文件
        new winston.transports.File({
            filename: path.join(LOG_DIR, 'combined.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 5,
            options: { flags: 'a' },
            tailable: true,
            zippedArchive: false
        })
    ]
});

if (process.env.NODE_ENV === 'development') {
    logger.add(
        new winston.transports.Console({
            format: customFormat, // 直接使用相同的自定义格式
            stderrLevels: ['error'],
            consoleWarnLevels: ['warn']
        })
    );
}

// 创建一个包装器来处理日志
class Logger {
    private isLogging = false; // 添加递归保护

    log(message: string, meta?: Record<string, unknown>): void {
        const level = process.env.NODE_ENV === 'development' ? 'debug' : 'info';
        this.logWithProtection(level, message, meta);
    }

    info(message: string, meta?: Record<string, unknown>): void {
        this.logWithProtection('info', message, meta);
    }

    warn(message: string, meta?: Record<string, unknown>): void {
        this.logWithProtection('warn', message, meta);
    }

    error(message: string, error?: Error | unknown, meta?: Record<string, unknown>): void {
        this.logWithProtection('error', message, { error, ...meta });
    }

    debug(message: string, meta?: Record<string, unknown>): void {
        this.logWithProtection('debug', message, meta);
    }

    private logWithProtection(
        level: string,
        message: string,
        meta?: Record<string, unknown>
    ): void {
        if (this.isLogging) {
            // 避免无限递归，直接使用console.error
            console.error('检测到日志记录器递归:', message);
            return;
        }

        this.isLogging = true;
        try {
            logger.log(level, message, meta);
        } catch (logError) {
            // 避免无限递归，直接使用console.error
            console.error('日志记录器失败:', message, logError);
        } finally {
            this.isLogging = false;
        }
    }
}

export default new Logger();
