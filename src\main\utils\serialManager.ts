import { SerialPort } from 'serialport';
import logger from './logger';
import { SerialPortOptions, PortsList } from '@shared/types';

export class SerialManager {
    private port: SerialPort | null = null;
    private buffer: Buffer = Buffer.alloc(0);
    // 简化的数据接收机制
    private currentReceivePromise: {
        resolve: (data: string) => void;
        reject: (error: Error) => void;
        timer: NodeJS.Timeout;
    } | null = null;

    // 数据完整性检查相关
    private dataTimeoutTimer: NodeJS.Timeout | null = null;
    private readonly DATA_TIMEOUT_MS = 2000; // 数据到达后等待2000ms判断是否完整

    // 默认串口配置
    private static readonly DEFAULT_OPTIONS: Partial<Omit<SerialPortOptions, 'timeout'>> = {
        baudRate: 4800 as const,
        dataBits: 8 as const,
        stopBits: 1 as const,
        parity: 'none' as const
    };

    // 缓冲区配置
    private static readonly MAX_BUFFER_SIZE = 1024 * 512; // 0.5MB
    // private static readonly MAX_UFFER_SIZE = 1024 * 1024; // 1MB

    /**
     * 获取系统所有可用串口列表
     */
    async listPorts(): Promise<PortsList[]> {
        try {
            const ports = await SerialPort.list();
            return ports.map((port) => ({ path: port.path }));
        } catch (error) {
            logger.error('获取串口列表失败', error, {
                component: './src/main/utils/serialManager.ts'
            });
            return [];
        }
    }

    /**
     * @param portPath 端口路径
     * @param options 串口选项
     * @returns
     */
    async openPort(options: Partial<Omit<SerialPortOptions, 'timeout'>>): Promise<boolean> {
        // 关闭已打开的端口
        if (this.port?.isOpen) {
            logger.info('关闭已打开的串口', {
                component: './src/main/utils/serialManager.ts'
            });
            this.port.removeAllListeners();
            await this.closePort();
        }

        // 合并默认选项和用户选项
        const finalOptions = {
            ...SerialManager.DEFAULT_OPTIONS,
            ...options
        };

        if (!finalOptions.path) {
            logger.error('串口端口不能为空', {
                component: './src/main/utils/serialManager.ts'
            });
            return false;
        }

        try {
            return await new Promise<boolean>((resolve, reject) => {
                this.port = new SerialPort({
                    path: finalOptions.path!,
                    baudRate: finalOptions.baudRate!,
                    dataBits: finalOptions.dataBits!,
                    stopBits: finalOptions.stopBits!,
                    parity: finalOptions.parity!,
                    autoOpen: false
                });

                if (!this.port) {
                    reject(new Error('串口实例创建失败'));
                    return;
                }

                this.port.once('open', () => {
                    logger.info(`串口 ${finalOptions.path} 打开成功`, {
                        component: './src/main/utils/serialManager.ts'
                    });
                    this.setupDataHandler();
                    resolve(true);
                });

                this.port.once('error', (err) => {
                    logger.error(`串口 ${finalOptions.path} 打开失败`, err, {
                        component: './src/main/utils/serialManager.ts'
                    });
                    this.cleanup();
                    reject(err);
                });

                this.port.open();
            });
        } catch (error) {
            logger.error('打开串口失败', error, {
                component: './src/main/utils/serialManager.ts'
            });
            this.cleanup();
            return false;
        }
    }
    /**
     * 设置串口数据、关闭、错误处理
     */
    private setupDataHandler() {
        if (!this.port) return;

        this.port.on('data', (chunk: Buffer) => {
            // logger.info('serial data received', {
            //     chunkLength: chunk.length,
            //     totalBufferLength: this.buffer.length + chunk.length,
            //     component: './src/main/utils/serialManager.ts'
            // });

            if (this.buffer.length + chunk.length > SerialManager.MAX_BUFFER_SIZE) {
                logger.warn('串口缓冲区已满，数据可能丢失', {
                    component: './src/main/utils/serialManager.ts'
                });
                return;
            }
            this.buffer = Buffer.concat([this.buffer, chunk]);

            // 清除之前的定时器
            if (this.dataTimeoutTimer) {
                clearTimeout(this.dataTimeoutTimer);
            }

            // 设置新的定时器，等待更多数据
            this.dataTimeoutTimer = setTimeout(() => {
                logger.info('串口收数据间隔(1秒)超时定时器触发,准备处理数据', {
                    data: {
                        timeoutMs: '[' + this.DATA_TIMEOUT_MS + ']ms'
                    },
                    component: './src/main/utils/serialManager.ts'
                });
                this.processBuffer();
            }, this.DATA_TIMEOUT_MS);
        });

        this.port.on('close', () => {
            logger.info('串口已关闭', { component: './src/main/utils/serialManager.ts' });
            this.cleanupCurrentReceive(new Error('串口已关闭'));
            this.cleanup();
        });

        this.port.on('error', (err) => {
            logger.error('串口错误', err, {
                component: './src/main/utils/serialManager.ts'
            });
            this.cleanupCurrentReceive(err);
        });
    }

    /**
     * 清理当前等待的接收请求
     */
    private cleanupCurrentReceive(error: Error) {
        if (this.currentReceivePromise) {
            const { reject, timer } = this.currentReceivePromise;
            clearTimeout(timer);
            reject(error);
            this.currentReceivePromise = null;
        }
    }

    /**
     * 清理资源
     */
    private cleanup() {
        this.buffer = Buffer.alloc(0);
        this.currentReceivePromise = null;

        // 清理数据超时定时器
        if (this.dataTimeoutTimer) {
            clearTimeout(this.dataTimeoutTimer);
            this.dataTimeoutTimer = null;
        }
    }

    /**
     * 关闭串口
     */
    async closePort(): Promise<boolean> {
        if (!this.port) return false;
        return new Promise<boolean>((resolve) => {
            this.cleanupCurrentReceive(new Error('串口已关闭'));
            const port = this.port;
            if (!port) {
                resolve(false);
                return;
            }
            port.close((err) => {
                if (err) {
                    logger.error('关闭串口失败', err, {
                        component: './src/main/utils/serialManager.ts'
                    });
                    resolve(false);
                }
                port.removeAllListeners();
                this.port = null;
                this.cleanup();
                resolve(true);
            });
        });
    }

    /**
     * 串口是否已打开
     */
    get isOpen(): boolean {
        return this.port?.isOpen || false;
    }

    /**
     * 发送数据
     */
    async sendData(data: string): Promise<boolean> {
        if (!this.port?.isOpen) {
            logger.error('串口打开失败', {
                component: './src/main/utils/serialManager.ts'
            });
            throw new Error('串口打开失败.');
            return false;
        }
        return new Promise<boolean>((resolve) => {
            this.port!.write(data, (err) => {
                if (err) {
                    logger.error('发送数据失败', err, {
                        component: './src/main/utils/serialManager.ts'
                    });

                    resolve(false);
                } else {
                    resolve(true);
                }
            });
        });
    }

    /**
     * 处理缓冲区，直接返回数据
     * 简化版本：数据超时后直接返回，无需复杂判断
     */
    private processBuffer() {
        logger.info('1-processBuffer 处理缓冲区数据', {
            data: {
                hasCurrentPromise: !!this.currentReceivePromise,
                bufferLength: this.buffer.length
            },
            component: './src/main/utils/serialManager.ts'
        });

        // 如果有等待的接收请求且有数据，直接返回
        if (this.currentReceivePromise && this.buffer.length > 0) {
            const { resolve, timer } = this.currentReceivePromise;
            clearTimeout(timer);

            const dataString = this.buffer.toString('utf8'); // 直接转换为字符串
            const dataLength = this.buffer.length;
            this.buffer = Buffer.alloc(0); // 清空缓冲区
            this.currentReceivePromise = null; // 清空当前请求

            logger.info('2-data 数据处理完成\r\n', {
                data: {
                    dataLength: dataLength,
                    data: dataString
                },
                component: './src/main/utils/serialManager.ts'
            });

            resolve(dataString);
        }
    }

    /**
     * 接收原始数据（带超时）
     */
    async receiveRawData(timeoutMs = 30000): Promise<string> {
        if (!this.port?.isOpen) {
            logger.error('串口未打开', {
                component: './src/main/utils/serialManager.ts'
            });
            throw new Error('串口打开失败.');
        }

        // 如果已有等待的请求，拒绝新的请求
        if (this.currentReceivePromise) {
            throw new Error('已有等待的数据接收请求，请稍后再试.');
        }

        logger.info('receiveRawData 接收原始数据', {
            data: {
                timeoutMs: timeoutMs
            },
            component: './src/main/utils/serialManager.ts'
        });

        return new Promise<string>((resolve, reject) => {
            const timer = setTimeout(() => {
                logger.info('receiveRawData 接收原始数据超时', {
                    data: {
                        timeoutMs: timeoutMs
                    },
                    component: './src/main/utils/serialManager.ts'
                });
                this.currentReceivePromise = null;
                reject(new Error('读取数据超时，请检查串口连接/仪器是否处于电脑控制状态.'));
            }, timeoutMs);

            this.currentReceivePromise = { resolve, reject, timer };

            logger.info('receiveRawData Promise 创建成功', {
                data: {
                    bufferLength: this.buffer.length
                },
                component: './src/main/utils/serialManager.ts'
            });
        });
    }

    /**
     * 解析酶标板数据
     * 将原始数据解析为结构化的酶标板数据
     */
    public parseElisaPlateData(rawData: string): Array<Array<number>> {
        const dataString = rawData;
        const lines = dataString.split('\n').filter((line) => line.trim().length > 0);

        const plateData: Array<Array<number>> = [];

        // 只处理前8行（A-H）
        for (let i = 0; i < Math.min(8, lines.length); i++) {
            const line = lines[i].trim();
            const parts = line.split(/\s+/).filter((part) => part.length > 0);

            // 跳过行首字母，提取12个数值
            const rowData: Array<number> = [];
            for (let j = 1; j <= 12 && j < parts.length; j++) {
                const value = parseFloat(parts[j]);
                rowData.push(isNaN(value) ? 0 : value);
            }

            // 如果数值不足12个，用0填充
            while (rowData.length < 12) {
                rowData.push(0);
            }

            plateData.push(rowData);
        }

        // 如果行数不足8行，用0填充
        while (plateData.length < 8) {
            plateData.push(new Array(12).fill(0));
        }

        logger.info('酶标板数据解析完成', {
            data: {
                rows: plateData.length,
                columns: plateData[0]?.length || 0
            },
            component: './src/main/utils/serialManager.ts'
        });

        return plateData;
    }
}

const serialManager = new SerialManager();
export default serialManager;
