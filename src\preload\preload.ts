import { context<PERSON><PERSON>, ipc<PERSON><PERSON><PERSON> } from 'electron';
import type { DeviceConfig, ApiResponse, PortsList, SerialPortOptions, Project, PlateData } from '@shared/types';
import { IPCChannels } from '@shared/ipcChannels';

import { LogMessage } from '@shared/logger';
import logger from './utils/logger';

import type { ApiIF } from './type';
import { PrintRequest, PrintResult } from '@shared/types';
import { TestRecord } from '@shared/types';
import type { PlateTemplate } from '@prisma/client';

// Custom APIs for renderer
const customApi: ApiIF = {
    configInfo: {
        // 设备配置参数
        getDeviceConfigInfo: async (): Promise<DeviceConfig[]> => {
            try {
                const response = await ipcRenderer.invoke(IPCChannels.configInfo.GetDeviceConfigInfo);

                if (response === undefined) {
                    return [];
                }
                return response as DeviceConfig[];
            } catch (error) {
                logger.error('获取设备配置失败', error, {
                    component: './src/preload/preload.ts'
                });
                return [];
            }
        },

        // 检测记录管理
        getTestRecordList: async (): Promise<ApiResponse<TestRecord[]>> => {
            const response = await ipcRenderer.invoke(IPCChannels.configInfo.GetTestRecordList);
            return response as ApiResponse<TestRecord[]>;
        },
        addTestRecord: async (testRecord: TestRecord): Promise<ApiResponse<TestRecord>> => {
            const response = await ipcRenderer.invoke(IPCChannels.configInfo.AddTestRecord, testRecord);
            return response as ApiResponse<TestRecord>;
        },
        deleteTestRecord: async (id: string): Promise<ApiResponse> => {
            const response = await ipcRenderer.invoke(IPCChannels.configInfo.DeleteTestRecord, id);
            return response as ApiResponse;
        },
        updateTestRecord: async (testRecord: TestRecord): Promise<ApiResponse<TestRecord>> => {
            const response = await ipcRenderer.invoke(IPCChannels.configInfo.UpdateTestRecord, testRecord);
            return response as ApiResponse<TestRecord>;
        },
        getTestRecordById: async (id: string): Promise<ApiResponse<TestRecord>> => {
            const response = await ipcRenderer.invoke(IPCChannels.configInfo.GetTestRecordById, id);
            return response as ApiResponse<TestRecord>;
        },
        getTestRecordByMtpNumber: async (mtpNumber: string): Promise<ApiResponse<TestRecord[]>> => {
            const response = await ipcRenderer.invoke(IPCChannels.configInfo.GetTestRecordByMtpNumber, mtpNumber);
            return response as ApiResponse<TestRecord[]>;
        },
        getTestRecordByTestDate: async (startDate: string, endDate: string): Promise<ApiResponse<TestRecord[]>> => {
            const response = await ipcRenderer.invoke(
                IPCChannels.configInfo.GetTestRecordByTestDate,
                startDate,
                endDate
            );
            return response as ApiResponse<TestRecord[]>;
        },

        // 项目管理
        getProjectList: async (): Promise<ApiResponse<Project[]>> => {
            const response = await ipcRenderer.invoke(IPCChannels.configInfo.GetProjectList);
            return response as ApiResponse<Project[]>;
        },
        addProject: async (project: Project): Promise<ApiResponse<Project>> => {
            const response = await ipcRenderer.invoke(IPCChannels.configInfo.AddProject, project);
            return response as ApiResponse<Project>;
        },
        deleteProject: async (id: string): Promise<ApiResponse> => {
            const response = await ipcRenderer.invoke(IPCChannels.configInfo.DeleteProject, id);
            return response as ApiResponse;
        },
        updateProject: async (project: Project): Promise<ApiResponse<Project>> => {
            const response = await ipcRenderer.invoke(IPCChannels.configInfo.UpdateProject, project);
            return response as ApiResponse<Project>;
        },

        // 模板相关 API
        addMtpTemplate: async (data: {
            name: string;
            plateData: PlateData;
            createdBy?: string;
        }): Promise<ApiResponse<PlateTemplate>> => {
            try {
                const response = await ipcRenderer.invoke(IPCChannels.configInfo.AddMtpTemplate, data);
                return response as ApiResponse<PlateTemplate>;
            } catch (error) {
                logger.error('保存模板失败', error, {
                    component: './src/preload/preload.ts'
                });
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '保存模板失败'
                };
            }
        },
        getAllMtpTemplates: async (): Promise<ApiResponse<PlateTemplate[]>> => {
            try {
                const response = await ipcRenderer.invoke(IPCChannels.configInfo.GetAllMtpTemplates);
                return response as ApiResponse<PlateTemplate[]>;
            } catch (error) {
                logger.error('获取模板列表失败', error, {
                    component: './src/preload/preload.ts'
                });
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '获取模板列表失败'
                };
            }
        },
        getMtpTemplateById: async (id: string): Promise<ApiResponse<PlateTemplate>> => {
            try {
                const response = await ipcRenderer.invoke(IPCChannels.configInfo.GetMtpTemplateById, id);
                return response as ApiResponse<PlateTemplate>;
            } catch (error) {
                logger.error('获取模板失败', error, {
                    component: './src/preload/preload.ts'
                });
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '获取模板失败'
                };
            }
        },
        updateMtpTemplate: async (
            id: string,
            data: { name?: string; plateData?: PlateData }
        ): Promise<ApiResponse<PlateTemplate>> => {
            try {
                const response = await ipcRenderer.invoke(IPCChannels.configInfo.UpdateMtpTemplate, id, data);
                return response as ApiResponse<PlateTemplate>;
            } catch (error) {
                logger.error('更新模板失败', error, {
                    component: './src/preload/preload.ts'
                });
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '更新模板失败'
                };
            }
        },
        deleteMtpTemplate: async (id: string): Promise<ApiResponse> => {
            try {
                const response = await ipcRenderer.invoke(IPCChannels.configInfo.DeleteMtpTemplate, id);
                return response as ApiResponse;
            } catch (error) {
                logger.error('删除模板失败', error, {
                    component: './src/preload/preload.ts'
                });
                return {
                    success: false,
                    error: error instanceof Error ? error.message : '删除模板失败'
                };
            }
        }

        // // 滤光片相关
        // loadFilters: (deviceId: string): Promise<FilterData[]> =>
        //     ipcRenderer.invoke(IPCChannels.CONFIG.LoadFilters, deviceId),

        // createFilters: (data: FilterInfoData[]): Promise<CreateFiltersResponse> =>
        //     ipcRenderer.invoke(IPCChannels.CONFIG.CreateFilters, data),

        // deleteFilter: (deviceId: string, number: number = 0): Promise<boolean> =>
        //     ipcRenderer.invoke(IPCChannels.CONFIG.DeleteFilter, deviceId, number)
    },
    store: {
        get: async <T>(key: string, defaultValue?: T): Promise<ApiResponse<T>> => {
            try {
                const response = await ipcRenderer.invoke(IPCChannels.STORE.Get, { key });
                if (response.data === undefined && defaultValue !== undefined) {
                    return {
                        success: true,
                        data: defaultValue
                    };
                }
                return response as ApiResponse<T>;
            } catch (error) {
                logger.error('get store value error:', error, {
                    data: { key },
                    component: './src/preload/preload.ts'
                });
                return {
                    success: false,
                    error: error instanceof Error ? error.message : 'Unknown error'
                };
            }
        },
        set: async <T>(key: string, value: T): Promise<ApiResponse<void>> => {
            try {
                return await ipcRenderer.invoke(IPCChannels.STORE.Set, { key, value });
            } catch (error) {
                logger.error('set store value error:', error, {
                    data: { key, value },
                    component: './src/preload/preload.ts'
                });

                return {
                    success: false,
                    error: error instanceof Error ? error.message : 'Unknown error'
                };
            }
        },
        delete: async (key: string): Promise<ApiResponse<void>> => {
            try {
                return await ipcRenderer.invoke(IPCChannels.STORE.Delete, { key });
            } catch (error) {
                logger.error('delete store value error:', error, {
                    data: { key },
                    component: './src/preload/preload.ts'
                });
                return {
                    success: false,
                    error: error instanceof Error ? error.message : 'Unknown error'
                };
            }
        }
    },
    serial: {
        // // 设备配置相关
        // getSerialConfig: (): Promise<SerialConfigData> =>
        //     ipcRenderer.invoke(IPCChannels.SERIAL.GetSerialConfig),

        // saveSerialConfig: (config: SerialConfigData): Promise<boolean> =>
        //     ipcRenderer.invoke(IPCChannels.SERIAL.SaveSerialConfig, config),

        getSerialPorts: (): Promise<PortsList[]> => ipcRenderer.invoke(IPCChannels.SERIAL.GetSerialPorts),

        openSerialPort: (options: SerialPortOptions): Promise<boolean> =>
            ipcRenderer.invoke(IPCChannels.SERIAL.OpenSerialPort, options),

        closeSerialPort: (): Promise<boolean> => ipcRenderer.invoke(IPCChannels.SERIAL.CloseSerialPort),

        sendData: (data: string): Promise<boolean> => ipcRenderer.invoke(IPCChannels.SERIAL.SendData, data),

        receiveData: (timeoutMs: number): Promise<string> =>
            ipcRenderer.invoke(IPCChannels.SERIAL.ReceiveData, timeoutMs)
    },
    app: {
        getAppVersion: (): Promise<string> => ipcRenderer.invoke(IPCChannels.APP.GetAppVersion),
        getUserPath: (): Promise<string> => ipcRenderer.invoke(IPCChannels.APP.GetUserPath),
        getAppPath: (): Promise<string> => ipcRenderer.invoke(IPCChannels.APP.GetAppPath),

        openFileDialog: (options: Electron.OpenDialogOptions): Promise<Electron.OpenDialogReturnValue> =>
            ipcRenderer.invoke(IPCChannels.APP.OpenFileDialog, options),
        openFolderDialog: (options: Electron.OpenDialogOptions): Promise<Electron.OpenDialogReturnValue> =>
            ipcRenderer.invoke(IPCChannels.APP.OpenFolderDialog, options),
        saveFileDialog: (options: Electron.SaveDialogOptions): Promise<Electron.SaveDialogReturnValue> =>
            ipcRenderer.invoke(IPCChannels.APP.SaveFileDialog, options),
        saveFolderDialog: (options: Electron.SaveDialogOptions): Promise<Electron.SaveDialogReturnValue> =>
            ipcRenderer.invoke(IPCChannels.APP.SaveFolderDialog, options)
        // 应用设置相关
        // getAppSettings: (): Promise<AppSettings | null> =>
        //     ipcRenderer.invoke(IPCChannels.APP.GetAppSettings),
        // updateAppSettings: (settings: Partial<AppSettings>): Promise<boolean> =>
        //     ipcRenderer.invoke(IPCChannels.APP.UpdateAppSettings, settings)
    },
    log: {
        send: async (data: LogMessage) => {
            try {
                return await ipcRenderer.invoke(IPCChannels.LOG.Send, data);
            } catch (error) {
                // 避免无限递归，直接使用console.error
                console.error('send log to main process error:', error);
                return { success: false };
            }
        }
    },
    print: {
        getPrinters: (): Promise<string[]> => ipcRenderer.invoke(IPCChannels.PRINT.GetPrinters),

        printPreview: (data: PrintRequest): Promise<PrintResult> =>
            ipcRenderer.invoke(IPCChannels.PRINT.PrintPreview, data),
        printToPdf: (data: PrintRequest, options?: Electron.PrintToPDFOptions): Promise<PrintResult> =>
            ipcRenderer.invoke(IPCChannels.PRINT.PrintToPdf, data, options),
        exportToExcel: (data: PrintRequest): Promise<PrintResult> =>
            ipcRenderer.invoke(IPCChannels.PRINT.ExportToExcel, data),
        exportToCsv: (data: PrintRequest): Promise<PrintResult> =>
            ipcRenderer.invoke(IPCChannels.PRINT.ExportToCsv, data),

        exportToLIS: (data: PrintRequest): Promise<PrintResult> =>
            ipcRenderer.invoke(IPCChannels.PRINT.ExportToLIS, data),

        print: (data: PrintRequest, options?: Electron.WebContentsPrintOptions): Promise<PrintResult> =>
            ipcRenderer.invoke(IPCChannels.PRINT.Print, data, options)
    }
};

if (process.contextIsolated) {
    try {
        // contextBridge.exposeInMainWorld('electron', electronAPI);
        contextBridge.exposeInMainWorld('customApi', customApi);
    } catch (error) {
        // 避免无限递归，直接使用console.error
        console.error('expose api to renderer process error:', error);
    }
} else {
    window.customApi = customApi;
}
