import {
    // FilterData,
    // FilterInfoData,
    // CreateFiltersResponse,
    // SerialConfigData,
    // AppSettings,
    DeviceConfig,
    ApiResponse,
    LogMessage,
    PortsList,
    SerialPortOptions,
    Project,
    PlateData,
    TestRecord
} from '@shared/types';
import { PrintRequest, PrintResult } from '@shared/types';

// 导入 Prisma 生成的 PlateTemplate 类型
import type { PlateTemplate } from '@prisma/client';

// 定义自定义 API 接口
interface ConfigInfoApi {
    getDeviceConfigInfo(): Promise<DeviceConfig[]>;

    // 检测记录管理
    getTestRecordList(): Promise<ApiResponse<TestRecord[]>>;
    addTestRecord(testRecord: TestRecord): Promise<ApiResponse<TestRecord>>;
    deleteTestRecord(id: string): Promise<ApiResponse>;
    updateTestRecord(testRecord: TestRecord): Promise<ApiResponse<TestRecord>>;
    getTestRecordById(id: string): Promise<ApiResponse<TestRecord>>;
    getTestRecordByMtpNumber(mtpNumber: string): Promise<ApiResponse<TestRecord[]>>;
    getTestRecordByTestDate(startDate: string, endDate: string): Promise<ApiResponse<TestRecord[]>>;

    // 项目管理
    getProjectList(): Promise<ApiResponse<Project[]>>;
    addProject(project: Project): Promise<ApiResponse<Project>>;
    deleteProject(id: string): Promise<ApiResponse>;
    updateProject(project: Project): Promise<ApiResponse<Project>>;

    // 模板相关 API
    addMtpTemplate(data: {
        name: string;
        plateData: PlateData;
        createdBy?: string;
    }): Promise<ApiResponse<PlateTemplate>>;
    getAllMtpTemplates(): Promise<ApiResponse<PlateTemplate[]>>;
    getMtpTemplateById(id: string): Promise<ApiResponse<PlateTemplate>>;
    updateMtpTemplate(id: string, data: { name?: string; plateData?: PlateData }): Promise<ApiResponse<PlateTemplate>>;
    deleteMtpTemplate(id: string): Promise<ApiResponse>;

    // loadFilters(deviceId: string): Promise<FilterData[]>;
    // createFilters(data: FilterInfoData[]): Promise<CreateFiltersResponse>;
    // deleteFilter(deviceId: string, number: number = 0): Promise<boolean>;
}

// 存储相关 API
interface StoreApi {
    get: <T>(key: string, defaultValue?: T) => Promise<ApiResponse<T>>;
    set: <T>(key: string, value: T) => Promise<ApiResponse<void>>;
    delete: (key: string) => Promise<ApiResponse<void>>;
}

// 串口相关 API
interface SerialApi {
    // getSerialConfig(): Promise<SerialConfigData>;
    // saveSerialConfig(config: SerialConfigData): Promise<boolean>;

    getSerialPorts(): Promise<PortsList[]>;

    openSerialPort(options: SerialPortOptions): Promise<boolean>;

    closeSerialPort(): Promise<boolean>;

    sendData(data: string): Promise<boolean>;

    receiveData(timeoutMs: number): Promise<string>;
}

// 应用相关 API
interface AppApi {
    getAppVersion(): Promise<string>;

    getUserPath(): Promise<string>;
    getAppPath(): Promise<string>;

    openFileDialog(options: Electron.OpenDialogOptions): Promise<Electron.OpenDialogReturnValue>;
    openFolderDialog(options: Electron.OpenDialogOptions): Promise<Electron.OpenDialogReturnValue>;
    saveFileDialog(options: Electron.SaveDialogOptions): Promise<Electron.SaveDialogReturnValue>;
    saveFolderDialog(options: Electron.SaveDialogOptions): Promise<Electron.SaveDialogReturnValue>;

    // getAppSettings(): Promise<AppSettings | null>;
    // updateAppSettings(settings: Partial<AppSettings>): Promise<boolean>;
}

interface PrintApi {
    getPrinters(): Promise<string[]>;
    printPreview(data: PrintRequest): Promise<PrintResult>;
    printToPdf(data: PrintRequest, options?: Electron.PrintToPDFOptions): Promise<PrintResult>;
    print(data: PrintRequest, options?: Electron.WebContentsPrintOptions): Promise<PrintResult>;

    exportToExcel(data: PrintRequest): Promise<PrintResult>;
    exportToCsv(data: PrintRequest): Promise<PrintResult>;
    exportToLIS(data: PrintRequest): Promise<PrintResult>;
}

interface logApi {
    send: (data: LogMessage) => Promise<{ success: boolean }>;
}

// 完整的 API 接口
export interface ApiIF {
    configInfo: ConfigInfoApi;
    store: StoreApi;
    serial: SerialApi;
    app: AppApi;
    log: logApi;
    print: PrintApi;
}

// 扩展 window 接口
declare global {
    interface Window {
        electron: ElectronAPI;
        customApi: ApiIF;
    }
}
