import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { IPCChannels } from '@shared/ipcChannels';
import type { LoggerInterface } from '@shared/logger';

// 将Error对象转换为可序列化的格式
const serializeError = (error: Error | unknown): unknown => {
    if (error instanceof Error) {
        return {
            name: error.name,
            message: error.message,
            stack: error.stack
            // 移除可能导致循环引用的属性
        };
    }

    // 处理其他类型的错误
    if (typeof error === 'object' && error !== null) {
        try {
            // 尝试序列化对象
            JSON.stringify(error);
            return error;
        } catch {
            // 如果无法序列化，返回字符串表示
            return {
                type: typeof error,
                stringValue: String(error)
            };
        }
    }

    return error;
};

const logger: LoggerInterface = {
    log: (message: string, meta?: Record<string, unknown>) => {
        try {
            ipcRenderer.invoke(IPCChannels.LOG.Send, {
                level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
                message,
                ...meta
            });
        } catch (error) {
            logger.error('send log to main process error:', error, {
                component: './src/preload/utils/logger.ts'
            });
        }
    },

    info: (message: string, meta?: Record<string, unknown>) => {
        try {
            ipcRenderer.invoke(IPCChannels.LOG.Send, {
                level: 'info',
                message,
                ...meta
            });
        } catch (error) {
            logger.error('send log to main process error:', error, {
                component: './src/preload/utils/logger.ts'
            });
        }
    },

    warn: (message: string, meta?: Record<string, unknown>) => {
        try {
            ipcRenderer.invoke(IPCChannels.LOG.Send, {
                level: 'warn',
                message,
                ...meta
            });
        } catch (error) {
            logger.error('send log to main process error:', error, {
                component: './src/preload/utils/logger.ts'
            });
        }
    },

    error: (message: string, error?: Error | unknown, meta?: Record<string, unknown>) => {
        try {
            ipcRenderer.invoke(IPCChannels.LOG.Send, {
                level: 'error',
                message,
                error: serializeError(error),
                ...meta
            });
        } catch (logError) {
            logger.error('send log to main process error:', logError, {
                component: './src/preload/utils/logger.ts'
            });
        }
    },

    debug: (message: string, meta?: Record<string, unknown>) => {
        if (process.env.NODE_ENV === 'development') {
            try {
                ipcRenderer.invoke(IPCChannels.LOG.Send, {
                    level: 'debug',
                    message,
                    ...meta
                });
            } catch (error) {
                logger.error('send log to main process error:', error, {
                    component: './src/preload/utils/logger.ts'
                });
            }
        }
    }
};

export default logger;
