import './i18n';
import React, { useEffect } from 'react';
import { Tabs, TabList, Tab, TabPanels, TabPanel, Box } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';

import HomePage from '@pages/1_home';
import ElisaControlPage from '@pages/2_elisacontrol';
import ResultDataPage from '@pages/3_resultdata';

const App: React.FC = () => {
    const { t } = useTranslation(['common']);

    useEffect(() => {
        // 设置应用标题为软件名称
        document.title = t('common:appName');
    }, [t]);

    // 定义导航标签，使用翻译键
    const tabsNav = [
        {
            key: 'home',
            label: t('common:mainWindow.tabs.home')
        },
        {
            key: 'elisaControl',
            label: t('common:mainWindow.tabs.elisaControl')
        },
        {
            key: 'resultData',
            label: t('common:mainWindow.tabs.resultData')
        }
    ];

    return (
        <Box
            h="100vh"
            w="100vw"
            bg="gray.50"
            bgGradient="linear(to-br, gray.50, blue.50, gray.50)"
            position="relative"
            overflow="hidden"
            _before={{
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '300px',
                bgGradient: 'linear(to-b, blue.500 -10%, transparent)',
                opacity: 0.03,
                pointerEvents: 'none'
            }}
        >
            <Tabs
                defaultIndex={0}
                align="center"
                variant="unstyled"
                h="100%"
                display="flex"
                flexDirection="column"
            >
                <Box
                    position="relative"
                    bg="rgba(255, 255, 255, 0.8)"
                    backdropFilter="blur(10px) saturate(180%)"
                    boxShadow="0 4px 20px rgba(0, 0, 0, 0.05)"
                    borderBottom="1px solid rgba(255, 255, 255, 0.3)"
                    zIndex="1"
                    py={2}
                    _after={{
                        content: '""',
                        position: 'absolute',
                        bottom: 0,
                        left: '5%',
                        right: '5%',
                        height: '1px',
                        background:
                            'linear-gradient(90deg, transparent, rgba(49,130,206,0.2), transparent)'
                    }}
                >
                    <TabList
                        px={{ base: '4', md: '8' }}
                        pb={2}
                        gap={{ base: '4', md: '6' }}
                        maxW="1800px"
                        mx="auto"
                        justifyContent={{ base: 'center', md: 'flex-start' }}
                        position="relative"
                    >
                        {tabsNav.map((link) => (
                            <Tab
                                key={link.key}
                                fontSize={{ base: 'md', md: 'lg' }}
                                fontFamily="MiSans-Medium"
                                px={6}
                                py={2.5}
                                borderRadius="md"
                                color="gray.600"
                                bg="transparent"
                                minW={{ base: '140px', md: '180px' }}
                                position="relative"
                                transition="all 0.2s cubic-bezier(0.4, 0, 0.2, 1)"
                                _after={{
                                    content: '""',
                                    position: 'absolute',
                                    bottom: '2px',
                                    left: '10%',
                                    right: '10%',
                                    height: '2px',
                                    background:
                                        'linear-gradient(90deg, transparent 0%, rgba(160,174,192,0.7) 20%, rgba(160,174,192,0.7) 80%, transparent 100%)',
                                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)'
                                }}
                                _hover={{
                                    color: 'blue.500',
                                    transform: 'translateY(-3px)',
                                    boxShadow: 'xl',
                                    _after: {
                                        background:
                                            'linear-gradient(90deg, transparent 0%, rgba(49,130,206,0.8) 20%, rgba(49,130,206,0.8) 80%, transparent 100%)',
                                        boxShadow: '0 3px 6px rgba(49,130,206,0.3)',
                                        height: '2.5px'
                                    }
                                }}
                                _selected={{
                                    color: 'white',
                                    background: 'linear-gradient(135deg, #4299E1 0%, #3182CE 100%)',
                                    fontFamily: 'MiSans-Bold',
                                    transform: 'translateY(-1px)',
                                    boxShadow: 'lg',
                                    backdropFilter: 'blur(8px)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    _hover: {
                                        transform: 'translateY(-3px)',
                                        boxShadow: 'xl',
                                        background:
                                            'linear-gradient(135deg, #3182CE 0%, #2B6CB0 100%)'
                                    },
                                    _after: {
                                        content: '""',
                                        position: 'absolute',
                                        bottom: '-12px',
                                        left: '50%',
                                        transform: 'translateX(-50%)',
                                        width: '12px',
                                        height: '12px',
                                        background:
                                            'linear-gradient(135deg, #4299E1 0%, #3182CE 100%)',
                                        clipPath: 'polygon(50% 100%, 0 0, 100% 0)',
                                        filter: 'drop-shadow(0 3px 4px rgba(0,0,0,0.2))',
                                        transition: 'all 0.02s cubic-bezier(0.4, 0, 0.2, 1)'
                                        // transition: 'all 0.005s linear'
                                    },
                                    _before: {
                                        content: '""',
                                        position: 'absolute',
                                        inset: '-1px',
                                        padding: '1px',
                                        borderRadius: 'md',
                                        background:
                                            'linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05))',
                                        WebkitMask:
                                            'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                                        WebkitMaskComposite: 'xor',
                                        maskComposite: 'exclude'
                                    }
                                }}
                            >
                                {link.label}
                            </Tab>
                        ))}
                    </TabList>
                </Box>

                <TabPanels
                    flex="1"
                    position="relative"
                    // overflow="hidden"
                    bg="white"
                    // backdropFilter="blur(8px)"
                    boxShadow="inset 0 2px 10px rgba(0, 0, 0, 0.03)"
                >
                    <TabPanel h="100%" p={0}>
                        <HomePage />
                    </TabPanel>
                    <TabPanel h="100%" p={0}>
                        <ElisaControlPage />
                    </TabPanel>
                    <TabPanel h="100%" p={0}>
                        <ResultDataPage />
                    </TabPanel>
                </TabPanels>
            </Tabs>
        </Box>
    );
};

export default App;
