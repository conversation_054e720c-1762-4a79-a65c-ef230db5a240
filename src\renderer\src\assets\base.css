:root {
    --ev-c-white: #ffffff;                /* 纯白色 */
    --ev-c-white-soft: #f8f8f8;           /* 柔和白 */
    --ev-c-white-mute: #f2f2f2;           /* 静音白 */

    --ev-c-black: #1b1b1f;                /* 纯黑色 */
    --ev-c-black-soft: #222222;           /* 柔和黑 */
    --ev-c-black-mute: #282828;           /* 静音黑 */

    --ev-c-gray-1: #515c67;               /* 深灰 */
    --ev-c-gray-2: #414853;               /* 中灰 */
    --ev-c-gray-3: #32363f;               /* 浅灰 */

    --ev-c-text-1: rgba(255, 255, 245, 0.86);  /* 主文本色，亮色主题下用 */
    --ev-c-text-2: rgba(235, 235, 245, 0.6);   /* 次文本色 */
    --ev-c-text-3: rgba(235, 235, 245, 0.38);  /* 辅助文本色 */

    --ev-button-alt-border: transparent;       /* 备用按钮边框色 */
    --ev-button-alt-text: var(--ev-c-text-1);  /* 备用按钮文本色 */
    --ev-button-alt-bg: var(--ev-c-gray-3);    /* 备用按钮背景色 */
    --ev-button-alt-hover-border: transparent; /* 备用按钮悬停边框色 */
    --ev-button-alt-hover-text: var(--ev-c-text-1); /* 备用按钮悬停文本色 */
    --ev-button-alt-hover-bg: var(--ev-c-gray-2);   /* 备用按钮悬停背景色 */
}

:root {
    --color-background: var(--ev-c-black);           /* 主背景色 */
    --color-background-soft: var(--ev-c-black-soft); /* 柔和背景色 */
    --color-background-mute: var(--ev-c-black-mute); /* 静音背景色 */

    --color-text: var(--ev-c-text-1);                /* 主文本色 */
}

*,
*::before,
*::after {
    box-sizing: border-box;   /* 盒模型以 border 计算，方便布局 */
    margin: 0;                /* 默认去除外边距 */
    font-weight: normal;      /* 默认字体不加粗 */
}

ul {
    list-style: none;         /* 去除 ul 的默认圆点 */
}

body {
    min-height: 100vh;                /* body 最小高度为视口高度，保证页面至少铺满全屏 */
    min-width: 100vw;                 /* body 最小宽度为视口宽度，防止内容太窄 */
    color: var(--color-text);         /* 字体颜色使用 CSS 变量，便于主题切换 */
    background: var(--color-background); /* 背景色同样用变量，方便统一管理和换肤 */
    line-height: 1.6;                 /* 设置基础行高，提升可读性 */
    font-family:
    'MiSans-Normal',              /* 优先使用小米正常字体 */
    'MiSans-Regular',             /* 备用小米常规字体 */
    Inter,                        /* 优先使用 Inter 字体 */
        -apple-system,                /* 苹果系统默认字体 */
        BlinkMacSystemFont,           /* Chrome on macOS 默认字体 */
        'Segoe UI',                   /* Windows 默认字体 */
        Roboto, Oxygen, Ubuntu,       /* 各平台常用字体 */
        Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;                   /* 最后兜底用无衬线字体 */
    text-rendering: optimizeLegibility;      /* 优化文字渲染，提升可读性 */
    -webkit-font-smoothing: antialiased;     /* Mac 下抗锯齿，字体更平滑 */
    -moz-osx-font-smoothing: grayscale;      /* Firefox/Mac 下字体平滑 */
}

/* 为不同字重设置默认字体 */
h1, h2, h3, h4, h5, h6 {
    font-family: 'MiSans-Bold', 'MiSans-Heavy', sans-serif;
}

strong, b {
    font-family: 'MiSans-Bold', sans-serif;
}

/* 确保字体文件正确加载 */
@font-face {
    font-family: 'MiSans-Normal';
    src: url('./fonts/MiSans-Normal.ttf') format('truetype');
    font-display: swap; /* 优化字体加载性能 */
}

@font-face {
    font-family: 'MiSans-Bold';
    src: url('./fonts/MiSans-Bold.ttf') format('truetype');
    font-display: swap;
}