@import './base.css';

/* 设置 body 的基础布局和行为 */
body {
    display: flex; /* 使用弹性盒子布局 */
    align-items: center; /* 垂直居中内容 */
    justify-content: center; /* 水平居中内容 */
    overflow: hidden; /* （可选）隐藏超出 body 的内容 */
    /* background-image: url('./wavy-lines.svg'); */ /* （可选）设置背景图片 */
    background-size: cover; /* 背景图片铺满整个页面 */
    user-select: none; /* 禁止用户选中页面内容 */
    background: rgba(245, 245, 245, 0.95); /* 半透明背景 */
}

/* 设置 <code> 标签的样式，提升代码片段可读性 */
code {
    font-weight: 600; /* 字体加粗 */
    padding: 3px 5px; /* 内边距 */
    border-radius: 2px; /* 圆角 */
    background-color: var(--color-background-mute); /* 背景色用主题变量 */
    font-family:
        ui-monospace,
        SFMono-Regular,
        SF Mono,
        Menlo,
        Consolas,
        Liberation Mono,
        monospace; /* 设置等宽字体 */
    font-size: 85%; /* 字体稍小 */
}

/* root 是 React 应用的挂载点 */
#root {
    display: flex; /* 弹性布局 */
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    flex-direction: column; /* 垂直排列子元素 */
    margin-bottom: 20px; /* 底部外边距 */
    background: var(--color-background); /* 在根组件设置背景 */
    min-height: 100vh;
}

/* .logo 设置 logo 图片的尺寸、不可拖拽、动画过渡。*/
.logo {
    margin-bottom: 20px; /* 底部外边距 */
    -webkit-user-drag: none; /* 禁止拖拽图片（Webkit内核） */
    height: 128px; /* 高度 */
    width: 128px; /* 宽度 */
    will-change: filter; /* 优化即将变化的 filter 属性的性能 */
    transition: filter 300ms; /* filter 属性变化时动画过渡 */
}

/* .logo:hover 鼠标悬停时添加发光阴影效果。 */
.logo:hover {
    filter: drop-shadow(0 0 1.2em #6988e6aa); /* 添加蓝色发光阴影 */
}

/* .creator 用于显示作者信息 */
.creator {
    font-size: 14px; /* 字体大小 */
    line-height: 16px; /* 行高 */
    color: var(--ev-c-text-2); /* 颜色用主题变量 */
    font-weight: 600; /* 字体加粗 */
    margin-bottom: 10px; /* 底部外边距 */
}

/* .text 用于主标题或大号文本 */
.text {
    font-size: 28px; /* 字体大 */
    color: var(--ev-c-text-1); /* 颜色用主题变量 */
    font-weight: 700; /* 加粗 */
    line-height: 32px; /* 行高 */
    text-align: center; /* 居中 */
    margin: 0 10px; /* 左右外边距 */
    padding: 16px 0; /* 上下内边距 */
}

/* tip 用于提示信息*/
.tip {
    font-size: 16px; /* 字体大小 */
    line-height: 24px; /* 行高 */
    color: var(--ev-c-text-2); /* 颜色用主题变量 */
    font-weight: 600; /* 加粗 */
}

/* .react 用于 React 关键字，设置渐变文字效果 */
.react {
    background: -webkit-linear-gradient(315deg, #087ea4 55%, #7c93ee); /* 渐变背景 */
    background-clip: text; /* 背景裁剪为文字 */
    -webkit-background-clip: text; /* 兼容 Webkit */
    -webkit-text-fill-color: transparent; /* 文字填充为透明，显示背景渐变 */
    font-weight: 700; /* 加粗 */
}

/* .ts 用于 TypeScript 关键字，设置渐变文字效果 */
.ts {
    background: -webkit-linear-gradient(315deg, #3178c6 45%, #f0dc4e); /* 渐变背景 */
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
}

/* .actions 是按钮容器，横向排列、自动换行 */
.actions {
    display: flex; /* 弹性布局 */
    padding-top: 32px; /* 顶部内边距 */
    margin: -6px; /* 负外边距，抵消子元素间距 */
    flex-wrap: wrap; /* 自动换行 */
    justify-content: flex-start; /* 左对齐 */
}

/* .action 是单个按钮的外层 */
.action {
    flex-shrink: 0; /* 不缩小 */
    padding: 6px; /* 内边距 */
}

/* .action a 是按钮样式，圆角、加粗、主题色，悬停时变色 */
.action a {
    cursor: pointer; /* 鼠标悬停为手型 */
    text-decoration: none; /* 去除下划线 */
    display: inline-block; /* 行内块元素 */
    border: 1px solid transparent; /* 边框透明 */
    text-align: center; /* 文字居中 */
    font-weight: 600; /* 加粗 */
    white-space: nowrap; /* 不换行 */
    border-radius: 20px; /* 圆角 */
    padding: 0 20px; /* 左右内边距 */
    line-height: 38px; /* 行高 */
    font-size: 14px; /* 字体大小 */
    border-color: var(--ev-button-alt-border); /* 边框色用主题变量 */
    color: var(--ev-button-alt-text); /* 字体色用主题变量 */
    background-color: var(--ev-button-alt-bg); /* 背景色用主题变量 */
}

/* 按钮悬停时变色 */
.action a:hover {
    border-color: var(--ev-button-alt-hover-border);
    color: var(--ev-button-alt-hover-text);
    background-color: var(--ev-button-alt-hover-bg);
}

/* .versions 用于显示版本信息，固定在底部，圆角、半透明背景 */
.versions {
    position: absolute; /* 绝对定位 */
    bottom: 30px; /* 距底部 30px */
    margin: 0 auto; /* 水平居中 */
    padding: 15px 0; /* 上下内边距 */
    font-family: 'Menlo', 'Lucida Console', monospace; /* 等宽字体 */
    display: inline-flex; /* 行内弹性盒子 */
    overflow: hidden; /* 超出隐藏 */
    align-items: center; /* 垂直居中 */
    border-radius: 22px; /* 圆角 */
    background-color: #202127; /* 背景色 */
    backdrop-filter: blur(24px); /* 毛玻璃效果 */
}

/* .versions li 是每个版本号，分隔线、间距、字体设置 */
.versions li {
    display: block; /* 块级元素 */
    float: left; /* 左浮动 */
    border-right: 1px solid var(--ev-c-gray-1); /* 右侧分隔线 */
    padding: 0 20px; /* 左右内边距 */
    font-size: 14px; /* 字体大小 */
    line-height: 14px; /* 行高 */
    opacity: 0.8; /* 透明度 */
    &:last-child {
        border: none; /* 最后一个无分隔线 */
    }
}

/* 响应式设计：屏幕宽度小于 720px 时，.text 字体变小 */
@media (max-width: 720px) {
    .text {
        font-size: 20px;
    }
}

/* 屏幕宽度小于 620px 时，隐藏 .versions */
@media (max-width: 620px) {
    .versions {
        display: none;
    }
}

/* 屏幕宽度小于 350px 时，隐藏 .tip 和 .actions，适配超小屏设备 */
@media (max-width: 350px) {
    .tip,
    .actions {
        display: none;
    }
}

/* 括号、逗号、分号等符号 */
.token.punctuation {
    color: #a50690 !important;
}

/* 运算符 */
.token.operator {
    color: #000000 !important;
}

/* 函数名 */
.token.function {
    color: #850391b7 !important;
}

/* 数字 */
.token.number {
    color: #4400ff !important;
}

/* 关键字 */
.token.keyword {
    color: #f321bf !important;
}
