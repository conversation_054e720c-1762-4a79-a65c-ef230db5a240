import React from 'react';
import { Modal, ModalOverlay, ModalContent, ModalHeader, ModalBody, ModalCloseButton, Box, Text, VStack, HStack, Badge } from '@chakra-ui/react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine, Cell } from 'recharts';
import { WellData } from '@shared/types/plateData';
import { useTranslation } from 'react-i18next';

interface CutoffChartProps {
    isOpen: boolean;
    onClose: () => void;
    wellData: Record<string, WellData>;
    cutOffValue: number;
    mtpNumber?: string;
}

interface ChartDataPoint {
    wellId: string;
    sampleNumber: string;
    sampleType: string;
    odValue: number;
    result: number;
    displayName: string;
}

const CutoffChart: React.FC<CutoffChartProps> = ({ isOpen, onClose, wellData, cutOffValue, mtpNumber }) => {
    const { t } = useTranslation(['common', 'pages']);

    // 处理数据，转换为图表所需格式
    const chartData: ChartDataPoint[] = React.useMemo(() => {
        const data: ChartDataPoint[] = [];

        Object.entries(wellData).forEach(([wellId, well]) => {
            // 只显示非空孔位且有OD值的数据
            if (well.sampleType.type !== 'none' && well.odValue !== undefined) {
                const displayName = well.sampleNumber || well.sampleType.name || wellId;
                data.push({
                    wellId,
                    sampleNumber: well.sampleNumber,
                    sampleType: well.sampleType.name || well.sampleType.type,
                    odValue: well.odValue,
                    result: well.result || 0,
                    displayName
                });
            }
        });

        // 按孔位ID排序 (A1, A2, ..., B1, B2, ...)
        return data.sort((a, b) => {
            const aRow = a.wellId.charAt(0);
            const aCol = parseInt(a.wellId.slice(1));
            const bRow = b.wellId.charAt(0);
            const bCol = parseInt(b.wellId.slice(1));

            if (aRow !== bRow) {
                return aRow.localeCompare(bRow);
            }
            return aCol - bCol;
        });
    }, [wellData]);

    // 获取结果颜色
    const getResultColor = (result: number): string => {
        switch (result) {
            case 0:
                return '#4299E1'; // 阴性 - 蓝色
            case 1:
                return '#F56565'; // 阳性 - 红色
            case 2:
                return '#ED8936'; // 灰区 - 橙色
            default:
                return '#A0AEC0'; // 未知 - 灰色
        }
    };

    // 获取结果文本
    const getResultText = (result: number): string => {
        switch (result) {
            case 0:
                return t('common:label.negative');
            case 1:
                return t('common:label.positive');
            case 2:
                return t('common:label.grayZone');
            default:
                return t('common:label.undefined');
        }
    };

    // 为每个数据点添加颜色
    const chartDataWithColors = chartData.map((item) => ({
        ...item,
        fill: getResultColor(item.result)
    }));

    // 自定义Tooltip
    const CustomTooltip = ({ active, payload }: { active?: boolean; payload?: any[] }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <Box bg="white" p={3} border="1px solid" borderColor="gray.200" borderRadius="md" shadow="md">
                    <VStack align="start" spacing={1}>
                        <Text fontWeight="bold">{`${t('pages:resultData.chart.tooltip.wellId')}: ${data.wellId}`}</Text>
                        <Text>{`${t('pages:resultData.chart.tooltip.sample')}: ${data.displayName}`}</Text>
                        <Text>{`${t('pages:resultData.chart.tooltip.type')}: ${data.sampleType}`}</Text>
                        <Text>{`${t('pages:resultData.chart.tooltip.odValue')}: ${data.odValue.toFixed(3)}`}</Text>
                        <HStack>
                            <Text>{`${t('pages:resultData.chart.tooltip.result')}:`}</Text>
                            <Badge colorScheme={data.result === 1 ? 'red' : data.result === 2 ? 'orange' : 'blue'}>{getResultText(data.result)}</Badge>
                        </HStack>
                    </VStack>
                </Box>
            );
        }
        return null;
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} size="6xl">
            <ModalOverlay />
            <ModalContent maxW="90vw" maxH="90vh">
                <ModalHeader>
                    <VStack align="start" spacing={1}>
                        <Text fontSize="xl" fontWeight="bold">
                            {t('pages:resultData.chart.title')}
                        </Text>
                        {mtpNumber && (
                            <Text fontSize="sm" color="gray.600">
                                {t('pages:resultData.chart.microplateNumber')}: {mtpNumber}
                            </Text>
                        )}
                    </VStack>
                </ModalHeader>
                <ModalCloseButton />
                <ModalBody pb={6}>
                    <VStack spacing={4} align="stretch">
                        {/* 图例说明 */}
                        <HStack spacing={6} justify="center">
                            <HStack>
                                <Box w={4} h={4} bg="#F56565" borderRadius="sm" />
                                <Text fontSize="md">{t('common:label.positive')}</Text>
                            </HStack>
                            <HStack>
                                <Box w={4} h={4} bg="#4299E1" borderRadius="sm" />
                                <Text fontSize="md">{t('common:label.negative')}</Text>
                            </HStack>
                            <HStack>
                                <Box w={4} h={4} bg="#ED8936" borderRadius="sm" />
                                <Text fontSize="md">{t('common:label.grayZone')}</Text>
                            </HStack>
                            <HStack>
                                <Box w={4} h={2} bg="red" />
                                <Text fontSize="md"> Cutoff ({cutOffValue.toFixed(3)})</Text>
                            </HStack>
                        </HStack>

                        {/* 图表 */}
                        <Box h="500px" w="100%">
                            <ResponsiveContainer width="100%" height="100%">
                                <BarChart
                                    data={chartData}
                                    margin={{
                                        top: 20,
                                        right: 30,
                                        left: 20,
                                        bottom: 60
                                    }}
                                >
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="wellId" angle={-45} textAnchor="end" height={80} fontSize={12} />
                                    <YAxis
                                        label={{
                                            value: 'OD',
                                            angle: -90,
                                            position: 'insideLeft'
                                        }}
                                        fontSize={12}
                                    />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Bar dataKey="odValue">
                                        {chartDataWithColors.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={entry.fill} />
                                        ))}
                                    </Bar>
                                    {/* Cutoff阈值线 */}
                                    <ReferenceLine
                                        y={cutOffValue}
                                        stroke="red"
                                        strokeWidth={2}
                                        strokeDasharray="5 5"
                                        label={{
                                            value: `Cutoff: ${cutOffValue.toFixed(3)}`,
                                            position: 'top'
                                        }}
                                    />
                                </BarChart>
                            </ResponsiveContainer>
                        </Box>

                        {/* 统计信息 */}
                        <HStack justify="center" spacing={8}>
                            <VStack>
                                <Text fontSize="sm" color="gray.600">
                                    {t('pages:resultData.chart.totalSamples')}
                                </Text>
                                <Text fontSize="lg" fontWeight="bold">
                                    {chartData.length}
                                </Text>
                            </VStack>
                            <VStack>
                                <Text fontSize="sm" color="gray.600">
                                    {t('pages:resultData.chart.positiveSamples')}
                                </Text>
                                <Text fontSize="lg" fontWeight="bold" color="red.500">
                                    {chartData.filter((d) => d.result === 1).length}
                                </Text>
                            </VStack>
                            <VStack>
                                <Text fontSize="sm" color="gray.600">
                                    {t('pages:resultData.chart.negativeSamples')}
                                </Text>
                                <Text fontSize="lg" fontWeight="bold" color="blue.500">
                                    {chartData.filter((d) => d.result === 0).length}
                                </Text>
                            </VStack>
                            <VStack>
                                <Text fontSize="sm" color="gray.600">
                                    {t('pages:resultData.chart.grayZoneSamples')}
                                </Text>
                                <Text fontSize="lg" fontWeight="bold" color="orange.500">
                                    {chartData.filter((d) => d.result === 2).length}
                                </Text>
                            </VStack>
                        </HStack>
                    </VStack>
                </ModalBody>
            </ModalContent>
        </Modal>
    );
};

export default CutoffChart;
