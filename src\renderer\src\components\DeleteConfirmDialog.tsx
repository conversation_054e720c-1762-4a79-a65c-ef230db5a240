import React from 'react';
import {
    AlertDialog,
    AlertDialogOverlay,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogBody,
    AlertDialogFooter,
    Button,
    Text
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';

export interface DeleteConfirmDialogProps {
    isOpen: boolean;

    onClose: () => void;

    onConfirm: () => void;
    /** 要删除的项目名称 */
    itemName: string;
    /** 要删除的项目标识（可选，如代码、ID等） */
    itemIdentifier?: string;

    /** 自定义描述文本（可选） */
    description?: string;

    isLoading?: boolean;
}

const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
    isOpen,
    onClose,
    onConfirm,
    itemName,
    itemIdentifier,
    description,
    isLoading = false
}) => {
    const { t } = useTranslation(['components']);
    const cancelRef = React.useRef<HTMLButtonElement>(null);

    const handleConfirm = () => {
        onConfirm();
    };

    const defaultDescription = itemIdentifier?.trim()
        ? `${t('components:DeleteConfirmDialog.delete_confirm_description')} "${itemName}" (${itemIdentifier}) ${t('components:DeleteConfirmDialog.delete_question')}`
        : `${t('components:DeleteConfirmDialog.delete_confirm_description')} "${itemName}" ${t('components:DeleteConfirmDialog.delete_question')}`;

    return (
        <AlertDialog isOpen={isOpen} leastDestructiveRef={cancelRef} onClose={onClose} isCentered>
            <AlertDialogOverlay>
                <AlertDialogContent>
                    <AlertDialogHeader fontSize="lg" fontWeight="bold">
                        {t('components:DeleteConfirmDialog.title')}
                    </AlertDialogHeader>

                    <AlertDialogBody>
                        <Text>{defaultDescription.trim() || description}</Text>
                        <Text fontSize="sm" color="gray.500" mt={2}>
                            {/* 此操作不可撤销，请谨慎操作! */}
                            {t('components:DeleteConfirmDialog.description_warn')}
                        </Text>
                    </AlertDialogBody>

                    <AlertDialogFooter>
                        <Button ref={cancelRef} onClick={onClose} disabled={isLoading}>
                            {t('components:DeleteConfirmDialog.delete_cancel')}
                        </Button>
                        <Button
                            colorScheme="red"
                            onClick={handleConfirm}
                            ml={3}
                            isLoading={isLoading}
                            // loadingText="删除中..."
                            loadingText={t('components:DeleteConfirmDialog.delete_ing')}
                        >
                            {/* 确认删除 */}
                            {t('components:DeleteConfirmDialog.delete_confirm')}
                        </Button>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialogOverlay>
        </AlertDialog>
    );
};

export default DeleteConfirmDialog;
