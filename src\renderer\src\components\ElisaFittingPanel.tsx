import React from 'react';
import {
    VStack,
    HStack,
    Button,
    Select,
    Text,
    Alert,
    AlertIcon,
    Stat,
    StatLabel,
    StatNumber,
    StatHelpText,
    Box,
    Divider
} from '@chakra-ui/react';
import { useElisaFitting } from '@renderer/hooks/useElisaFitting';
import {
    FittingModel,
    StandardCurveData,
    FittingResult,
    fittingModelType,
    getFittingModelTypeLabel
} from '@shared/types';

interface ElisaFittingPanelProps {
    standardData: StandardCurveData;
    onResultChange?: (result: FittingResult) => void;
}

export const ElisaFittingPanel: React.FC<ElisaFittingPanelProps> = ({ standardData, onResultChange }) => {
    const [selectedModel, setSelectedModel] = React.useState<FittingModel>('LOGISTIC_4P');

    const { result, isLoading, error, fit, clear, isValid, rSquared, equation } = useElisaFitting();

    const handleFit = () => {
        fit(standardData, { model: selectedModel });
    };

    React.useEffect(() => {
        if (result && onResultChange) {
            onResultChange(result);
        }
    }, [result, onResultChange]);

    return (
        <Box p={4} borderWidth={1} borderRadius="md" bg="gray.50">
            <VStack spacing={4} align="stretch">
                <Text fontSize="lg" fontWeight="bold">
                    标准曲线拟合
                </Text>

                <HStack>
                    <Text minW="80px">拟合模型:</Text>
                    <Select
                        value={selectedModel}
                        onChange={(e) => setSelectedModel(e.target.value as FittingModel)}
                        disabled={isLoading}
                        bg="white"
                    >
                        {/* <option value="LINEAR">线性</option>
                        <option value="LOG_LOG">双对数</option>
                        <option value="LOGISTIC_3P">Logistic 3P</option>
                        <option value="LOGISTIC_4P">Logistic 4P</option>
                        <option value="LOGISTIC_5P">Logistic 5P</option>
                        <option value="CUBIC_SPLINE">三次样条</option> */}

                        {fittingModelType.map((item) => (
                            <option key={item} value={item}>
                                {getFittingModelTypeLabel(item as FittingModel)}
                            </option>
                        ))}
                    </Select>
                </HStack>

                <HStack>
                    <Button
                        onClick={handleFit}
                        isLoading={isLoading}
                        loadingText="拟合中..."
                        colorScheme="blue"
                        disabled={standardData.concentrations.length < 3}
                        size="sm"
                    >
                        开始拟合
                    </Button>
                    <Button onClick={clear} disabled={!result} size="sm">
                        清除结果
                    </Button>
                </HStack>

                {error && (
                    <Alert status="error" size="sm">
                        <AlertIcon />
                        {error}
                    </Alert>
                )}

                {result && (
                    <>
                        <Divider />
                        <VStack spacing={3} align="stretch">
                            <Alert status={isValid ? 'success' : 'warning'} size="sm">
                                <AlertIcon />
                                拟合{isValid ? '成功' : '完成但质量较差'}
                            </Alert>

                            <HStack spacing={4}>
                                <Stat size="sm">
                                    <StatLabel>R²</StatLabel>
                                    <StatNumber fontSize="md">{rSquared.toFixed(4)}</StatNumber>
                                    <StatHelpText>拟合优度</StatHelpText>
                                </Stat>

                                <Stat size="sm">
                                    <StatLabel>收敛性</StatLabel>
                                    <StatNumber fontSize="md">{result.convergence ? '是' : '否'}</StatNumber>
                                    <StatHelpText>迭代: {result.iterations}</StatHelpText>
                                </Stat>
                            </HStack>

                            <Text fontSize="xs" color="gray.600" wordBreak="break-all">
                                方程: {equation}
                            </Text>

                            {result.qualityMetrics?.warnings && result.qualityMetrics.warnings.length > 0 && (
                                <Alert status="warning" size="sm">
                                    <AlertIcon />
                                    <Text fontSize="sm">{result.qualityMetrics.warnings.join('; ')}</Text>
                                </Alert>
                            )}
                        </VStack>
                    </>
                )}
            </VStack>
        </Box>
    );
};
