import React from 'react';
import { Box, Text, VStack } from '@chakra-ui/react';
import { WellData } from '@shared/types/plateData';
import { getSampleTypeColor } from '@shared/commondefines';
import WellTooltip from './WellTooltip';
import { useTranslation } from 'react-i18next';

interface ElisaPlateDataFormProps {
    isReadOnly?: boolean;
    resultShowType: 'result' | 'original' | 'sco';
    wellData: { [wellId: string]: WellData }; // 孔位数据
}

const DEFAULT_COLS: number = 12;

const ElisaPlateDataForm: React.FC<ElisaPlateDataFormProps> = ({
    isReadOnly = false,
    resultShowType,
    wellData
}) => {
    const { t } = useTranslation(['common', 'pages']);
    // 格式化数值显示
    const formatValue = (value: number | undefined) => {
        if (value === undefined || value === null) return '';
        return value.toFixed(3);
    };
    // 列标题 (1-12)
    const columnHeaders = Array.from({ length: DEFAULT_COLS }, (_, i) => (i + 1).toString());
    // 行标题 (A-H)
    const rowHeaders = Array.from('ABCDEFGH');

    return (
        <VStack
            // position="relative"
            p={4}
            width="100%"
            height="100%"
            minHeight="600px"
            // display="flex"
            // flexDirection="column"
        >
            {/* 96孔板容器 - 使用CSS Grid实现等距离布局 */}
            <Box
                flex="1"
                display="grid"
                gridTemplateColumns="30px repeat(12, 1fr)"
                gridTemplateRows="30px repeat(8, 1fr)"
                gap={1}
                width="100%"
                height="100%"
                // minHeight="500px"
                // bg="gray.100"
                p={1}
                borderRadius="lg"
                border="2px solid"
                // borderColor={'red'}
                borderColor="gray.300"
            >
                {/* 左上角空白 */}
                <Box
                    // bg="gray.200"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    borderRadius="md"
                >
                    {/* <Text fontSize="xs" fontWeight="bold" color="gray.600">
                        孔板
                    </Text> */}
                </Box>

                {/* 列标题 (1-12) */}
                {columnHeaders.map((header) => (
                    <Box
                        key={header}
                        // bg="blue.50"
                        // border="1px solid"
                        // borderColor="blue.200"
                        // h="28px"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        borderRadius="md"
                    >
                        <Text fontSize="sm" fontWeight="bold" color="blue.700">
                            {header}
                        </Text>
                    </Box>
                ))}

                {/* 行标题 (A-H) 和孔位数据 */}
                {rowHeaders.map((rowHeader) => (
                    <React.Fragment key={rowHeader}>
                        {/* 行标题 */}
                        <Box
                            // bg="green.50"
                            // bg="blue.50"
                            // border="1px solid"
                            // borderColor="blue.200"
                            // h = "30px"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                            borderRadius="md"
                        >
                            <Text fontSize="sm" fontWeight="bold" color="green.700">
                                {rowHeader}
                            </Text>
                        </Box>

                        {/* 该行的12个孔位 */}
                        {columnHeaders.map((_, colIndex) => {
                            const wellId = `${rowHeader}${colIndex + 1}`;
                            let plateData: WellData = wellData[wellId];
                            if (!plateData) {
                                plateData = {
                                    sampleType: { name: '', type: 'none' },
                                    sampleNumber: '',
                                    odMain: 0,
                                    odRef: 0,
                                    odValue: 0,
                                    odRatio: 0,
                                    result: 0
                                };
                            }
                            return (
                                <WellTooltip
                                    key={`${rowHeader}-${colIndex + 1}`}
                                    wellId={wellId}
                                    wellData={plateData}
                                    resultShowType={resultShowType}
                                    placement="top-start"
                                >
                                    <Box
                                        // bg={isReadOnly ? 'gray.50' : 'white'}

                                        border="2px solid"
                                        borderColor="gray.300"
                                        borderRadius="md"
                                        p={0}
                                        cursor={isReadOnly ? 'default' : 'pointer'}
                                        opacity={isReadOnly ? 0.8 : 1}
                                        _hover={{
                                            bg: 'blue.50',
                                            borderColor: 'blue.400'

                                            // transform: 'scale(1.02)'
                                            // transition: 'all 0.2s'
                                        }}
                                        // transition="border-color 0.2s, background-color 0.2s"
                                        // position="relative"
                                        overflow="hidden"
                                        alignContent={'center'}
                                        justifyContent={'center'}
                                    >
                                        {/* 孔位内容 - 使用CSS Grid进行内部布局 */}
                                        <Box
                                            display="grid"
                                            gridTemplateRows="1fr 1fr 1fr 1fr"
                                            gap={0}
                                            p={0}
                                            // height="100%"
                                            // h="60px"
                                            minHeight="0px"
                                        >
                                            {/* 样本类型 */}
                                            <Box
                                                bg={
                                                    plateData.sampleType.type === 'sample'
                                                        ? 'transparent'
                                                        : getSampleTypeColor(
                                                              plateData.sampleType.type
                                                          ).bg
                                                }
                                                display="flex"
                                                alignItems="center"
                                                justifyContent="center"
                                                p={0}
                                                borderRadius="lg"
                                                w="100%"
                                                minW="0"
                                            >
                                                <Text
                                                    fontSize="xs"
                                                    fontFamily="MiSans-Normal"
                                                    color="gray.700"
                                                    textAlign="center"
                                                    lineHeight="1"
                                                    whiteSpace="nowrap"
                                                    overflow="hidden"
                                                    textOverflow="ellipsis"
                                                    w="100%"
                                                    minW="0"
                                                    fontWeight={
                                                        plateData.sampleType.type === 'sample'
                                                            ? 'normal'
                                                            : 'bold'
                                                    }
                                                >
                                                    {plateData.sampleType.type === 'sample'
                                                        ? plateData.sampleNumber
                                                        : plateData.sampleType.name}
                                                </Text>
                                            </Box>

                                            {resultShowType === 'result' &&
                                                plateData.sampleType.type !== 'none' && (
                                                    <>
                                                        {/* OD 结果值 */}
                                                        <Box
                                                            // bg="blue.50"
                                                            display="flex"
                                                            alignItems="center"
                                                            justifyContent="center"
                                                            p={0}
                                                            borderRadius="sm"
                                                        >
                                                            <Text
                                                                fontSize="sm"
                                                                fontFamily="MiSans-Normal"
                                                                color="blue.700"
                                                                textAlign="center"
                                                                lineHeight="1"
                                                            >
                                                                {formatValue(plateData.odValue)}
                                                            </Text>
                                                        </Box>

                                                        {/* OD比值 */}
                                                        <Box
                                                            // bg="orange.50"
                                                            display="flex"
                                                            alignItems="center"
                                                            justifyContent="center"
                                                            p={0}
                                                            borderRadius="sm"
                                                        >
                                                            <Text
                                                                fontSize="sm"
                                                                fontFamily="MiSans-Normal"
                                                                color="orange.700"
                                                                textAlign="center"
                                                                lineHeight="1"
                                                            >
                                                                {formatValue(plateData.odRatio)}
                                                            </Text>
                                                        </Box>
                                                    </>
                                                )}

                                            {resultShowType === 'original' &&
                                                plateData.sampleType.type !== 'none' && (
                                                    <>
                                                        {/* OD 主波长 */}
                                                        <Box
                                                            // bg="blue.50"
                                                            display="flex"
                                                            alignItems="center"
                                                            justifyContent="center"
                                                            p={0}
                                                            // borderRadius="sm"
                                                        >
                                                            <Text
                                                                fontSize="sm"
                                                                fontFamily="MiSans-Normal"
                                                                // color="blue.700"
                                                                textAlign="center"
                                                                lineHeight="1"
                                                            >
                                                                {formatValue(plateData.odMain)}
                                                            </Text>
                                                        </Box>

                                                        {/* OD 参考波长 */}
                                                        <Box
                                                            display="flex"
                                                            alignItems="center"
                                                            justifyContent="center"
                                                            p={0}
                                                        >
                                                            <Text
                                                                fontSize="sm"
                                                                fontFamily="MiSans-Normal"
                                                                // color="orange.700"
                                                                textAlign="center"
                                                                lineHeight="1"
                                                            >
                                                                {formatValue(plateData.odRef)}
                                                            </Text>
                                                        </Box>
                                                    </>
                                                )}

                                            {/* 结果 */}
                                            <Box
                                                display="flex"
                                                alignItems="center"
                                                justifyContent="center"
                                                p="2px"
                                                borderRadius="sm"
                                            >
                                                <Text
                                                    fontSize="xs"
                                                    fontFamily="MiSans-Normal"
                                                    color={
                                                        plateData.result === 1
                                                            ? 'red.500'
                                                            : plateData.result === 0
                                                              ? 'green.500'
                                                              : 'gray.500'
                                                    }
                                                    textAlign="center"
                                                    lineHeight="1"
                                                    // fontWeight="bold"
                                                >
                                                    {plateData.sampleType.type !== 'none'
                                                        ? plateData.result === 1
                                                            ? t('common:label.positive') // 阳性
                                                            : t('common:label.negative') // 阴性
                                                        : ''}
                                                </Text>
                                            </Box>
                                        </Box>

                                        {/* 孔位ID悬浮显示 */}
                                        {/* <Box
                                            position="absolute"
                                            bottom="-3px"
                                            left="-3px"
                                            bg="gray.600"
                                            color="white"
                                            fontSize="xs"
                                            px="2px"
                                            py="1px"
                                            borderRadius="md"
                                            opacity="0.7"
                                            pointerEvents="none"
                                        >
                                            {wellId}
                                        </Box> */}
                                    </Box>
                                </WellTooltip>
                            );
                        })}
                    </React.Fragment>
                ))}
            </Box>

            {/* 图例说明 */}
            {/* <Box
                mt={4}
                p={3}
                bg="gray.50"
                borderRadius="md"
                border="1px solid"
                borderColor="gray.200"
            >
                <Text fontSize="sm" fontFamily="MiSans-Normal" color="gray.600" mb={2}>
                    孔位信息说明：
                </Text>
                <Box display="grid" gridTemplateColumns="repeat(4, 1fr)" gap={2}>
                    <Box display="flex" alignItems="center" gap={1}>
                        <Box
                            w="3"
                            h="3"
                            bg="gray.50"
                            borderRadius="sm"
                            border="1px solid"
                            borderColor="gray.300"
                        />
                        <Text fontSize="xs">样本类型</Text>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1}>
                        <Box
                            w="3"
                            h="3"
                            bg="blue.50"
                            borderRadius="sm"
                            border="1px solid"
                            borderColor="blue.300"
                        />
                        <Text fontSize="xs">OD原始值</Text>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1}>
                        <Box
                            w="3"
                            h="3"
                            bg="orange.50"
                            borderRadius="sm"
                            border="1px solid"
                            borderColor="orange.300"
                        />
                        <Text fontSize="xs">OD比值</Text>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1}>
                        <Box
                            w="3"
                            h="3"
                            bg="red.50"
                            borderRadius="sm"
                            border="1px solid"
                            borderColor="red.300"
                        />
                        <Text fontSize="xs">检测结果</Text>
                    </Box>
                </Box>
            </Box> */}
        </VStack>
    );
};

export default ElisaPlateDataForm;
