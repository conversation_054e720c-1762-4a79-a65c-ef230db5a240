import React, { useEffect, useState } from 'react';
import { FormControl, FormLabel, HStack, Input, Select, InputGroup, InputLeftAddon, InputRightAddon, Spacer } from '@chakra-ui/react';
import logger from '../../utils/logger';

interface AdditionalInfoFormProps {
    id?: string;
}

const AdditionalInfoForm: React.FC<AdditionalInfoFormProps> = ({ id }) => {
    const [reviewers, setReviewers] = useState<string[]>([]);
    const [selectedReviewer, setSelectedReviewer] = useState<string>('');

    useEffect(() => {
        const fetchReviewers = async () => {
            try {
                const response = await fetch('/api/reviewers');
                if (!response.ok) {
                    throw new Error(`Network response code:[${response.status}] ${response.statusText}`);
                }
                const data = await response.json();
                if (Array.isArray(data)) {
                    setReviewers(data);
                } else {
                    setReviewers([]);
                }
            } catch (error) {
                logger.error('Failed to fetch reviewers:', error, {
                    component: './src/renderer/src/components/ElisaPlate/AdditionalInfoForm.tsx'
                });

                // 可选：接口失败时用本地模拟数据兜底
                setReviewers([]);
            }
        };

        fetchReviewers();
    }, []);

    const handleReviewerChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedReviewer(event.target.value);
    };

    return (
        <FormControl id={id} w="100%" as="fieldset" borderWidth="1px" borderRadius="md" p={2}>
            <FormLabel as="legend">附加信息</FormLabel>
            <HStack>
                <InputGroup display="flex" alignItems="center" flex="1">
                    <InputLeftAddon bg="transparent" border="none" pl={0}>
                        温度
                    </InputLeftAddon>
                    <Input size="sm" />
                    <InputRightAddon bg="transparent" border="none">
                        度
                    </InputRightAddon>
                </InputGroup>
                <InputGroup display="flex" alignItems="center" flex="1">
                    <InputLeftAddon bg="transparent" border="none" pl={0}>
                        湿度
                    </InputLeftAddon>
                    <Input size="sm" />
                    <InputRightAddon bg="transparent" border="none">
                        %
                    </InputRightAddon>
                </InputGroup>
                <InputGroup display="flex" alignItems="center" flex="1">
                    <InputLeftAddon bg="transparent" border="none" pl={0}>
                        审核者
                    </InputLeftAddon>
                    <Select size="sm" value={selectedReviewer} onChange={handleReviewerChange}>
                        {reviewers.map((reviewer) => (
                            <option key={reviewer} value={reviewer}>
                                {reviewer}
                            </option>
                        ))}
                    </Select>
                </InputGroup>
                <Spacer />
            </HStack>
        </FormControl>
    );
};

export default AdditionalInfoForm;
