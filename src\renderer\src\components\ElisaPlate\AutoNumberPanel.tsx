import React, { useState } from 'react';
import { useTranslation } from 'react-i18next'; // 导入国际化 hook
import {
    Box,
    VStack,
    HStack,
    FormControl,
    FormLabel,
    Select,
    Button,
    NumberInput,
    NumberInputField,
    NumberInputStepper,
    NumberIncrementStepper,
    NumberDecrementStepper,
    Text,
    Divider,
    RadioGroup,
    Radio,
    Checkbox,
    Center
} from '@chakra-ui/react';

// import { getAllWellIds } from './wellUtils';

export interface AutoNumberPanelProps {
    id?: string;
    selectedWell?: string; // 当前选中的孔位 (如: "A1", "B2")
    onStartWellChange?: (wellId: string) => void; // 孔位变化回调
    selectedCellsCount?: number; // 选中的孔位数量
    onApplyNumbering?: (config: NumberingConfig) => void; // 应用编号回调
    onClearSelection?: () => void; // 清除选择回调
    maxNumber?: number; // 当前最大编号值
    selectedSampleType?: string; // 当前选择的样本类型
    // startWell: WellId | null;
    // sampleStartNumber: number;
    // samplePrefix: string;
    // numberDirection: 'horizontal' | 'vertical';
    // selectedWellsCount: number;
    // selectedWellsList: WellId[];
    // isReadOnly?: boolean;
    // onStartWellChange: (wellId: WellId) => void;
    // onStartNumberChange: (value: number) => void;
    // onPrefixChange: (value: string) => void;
    // onDirectionChange: (direction: 'horizontal' | 'vertical') => void;
    // onApplyNumbering: () => void;
    // onClearSelection: () => void;
    // onWheelEvent: (event: React.WheelEvent) => void;
}

export interface NumberingConfig {
    startWell: string;
    startNumber: number;
    sampleCount: number;
    direction: 'horizontal' | 'vertical';
    isReverse: boolean;
}

export const AutoNumberPanel: React.FC<AutoNumberPanelProps> = ({
    id,
    selectedWell = '',
    onStartWellChange,
    selectedCellsCount = 0,

    onApplyNumbering,
    onClearSelection,
    maxNumber = 0,
    selectedSampleType = ''
    // startWell,
    // sampleStartNumber,
    // samplePrefix,
    // numberDirection,
    // selectedWellsCount,
    // selectedWellsList,
    // isReadOnly = false,
    // onStartWellChange,
    // onStartNumberChange,
    // onPrefixChange,
    // onDirectionChange,
    // onApplyNumbering,
    // onClearSelection,
    // onWheelEvent
}) => {
    // 使用国际化 hook
    const { t } = useTranslation('components');

    // 内部状态管理
    const [startNumber, setStartNumber] = useState<number>(1);
    const [sampleCount, setSampleCount] = useState<number>(8);
    const [direction, setDirection] = useState<'horizontal' | 'vertical'>('vertical');
    const [isReverse, setIsReverse] = useState<boolean>(false);

    // 当最大编号值变化时，自动更新起始编号
    React.useEffect(() => {
        if (maxNumber > 0) {
            setStartNumber(maxNumber + 1);
        }
    }, [maxNumber]);
    // 生成所有孔位选项 (A1-H12)
    const generateWellOptions = () => {
        const options: JSX.Element[] = [];
        const rowLabels = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];

        for (let row = 0; row < 8; row++) {
            for (let col = 1; col <= 12; col++) {
                const wellId = `${rowLabels[row]}${col}`;
                options.push(
                    <option key={wellId} value={wellId}>
                        {wellId}
                    </option>
                );
            }
        }
        return options;
    };

    // 处理起始孔位变化
    const handleStartWellChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const wellId = event.target.value;
        onStartWellChange?.(wellId);
    };

    // 处理起始编号变化
    const handleStartNumberChange = (value: string | number) => {
        const num = typeof value === 'string' ? parseInt(value) : value;
        setStartNumber(num || 1);
    };

    // 处理样本数量变化
    const handleSampleCountChange = (value: string | number) => {
        const num = typeof value === 'string' ? parseInt(value) : value;
        setSampleCount(num || 1);
    };

    // 处理编号方向变化
    const handleDirectionChange = (value: string) => {
        setDirection(value as 'horizontal' | 'vertical');
    };

    // 处理逆序选择变化
    const handleReverseChange = (checked: boolean) => {
        setIsReverse(checked);
    };

    // 应用编号
    const handleApplyNumbering = () => {
        if (!selectedWell) {
            alert(t('autoNumberPanel.messages.selectStartWell')); // 请先选择起始孔位
            return;
        }

        const config: NumberingConfig = {
            startWell: selectedWell,
            startNumber,
            sampleCount,
            direction,
            isReverse
        };

        onApplyNumbering?.(config);
    };

    // 清除选择
    const handleClearSelection = () => {
        // 恢复起始编号为默认值1
        setStartNumber(1);
        setIsReverse(false);
        setDirection('vertical');

        onClearSelection?.();
    };

    return (
        <Box flex="0 0 300px" minW="300px">
            <VStack spacing={3} align="stretch" p={4}>
                <Text fontSize="lg" fontWeight="bold">
                    {t('components:autoNumberPanel.title')} {/* 样本编号设置 */}
                </Text>
                <Divider />

                {/* 起始孔位选择 */}
                <FormControl>
                    <HStack spacing={3} align="center">
                        <FormLabel
                            htmlFor={`${id}-start-well`}
                            mb={0}
                            fontSize="sm"
                            fontWeight="medium"
                            minW="70px"
                        >
                            {t('components:autoNumberPanel.fields.startWell.label')}:{' '}
                            {/* 起始孔位: */}
                        </FormLabel>
                        <Select
                            id={`${id}-start-well`}
                            value={selectedWell}
                            onChange={handleStartWellChange}
                            // onWheel={onWheelEvent}
                            size="sm"
                            flex="1"
                            // isDisabled={isReadOnly}
                        >
                            <option value="">{t('common:button.select')}</option> {/* 选择 */}
                            {generateWellOptions()}
                        </Select>
                    </HStack>
                </FormControl>

                {/* 起始编号 */}
                <FormControl>
                    <HStack spacing={3} align="center">
                        <FormLabel
                            htmlFor={`${id}-start-number`}
                            mb={0}
                            fontSize="sm"
                            fontWeight="medium"
                            minW="70px"
                        >
                            {t('components:autoNumberPanel.fields.startNumber.label')}:{' '}
                            {/* 起始编号: */}
                        </FormLabel>
                        <NumberInput
                            id={`${id}-start-number`}
                            value={startNumber}
                            onChange={handleStartNumberChange}
                            min={1}
                            size="sm"
                            flex="1"
                        >
                            <NumberInputField />
                            <NumberInputStepper>
                                <NumberIncrementStepper />
                                <NumberDecrementStepper />
                            </NumberInputStepper>
                        </NumberInput>
                    </HStack>
                </FormControl>

                {/* 样本数量 */}
                <FormControl>
                    <HStack spacing={3} align="center">
                        <FormLabel
                            htmlFor={`${id}-sample-count`}
                            mb={0}
                            fontSize="sm"
                            fontWeight="medium"
                            minW="70px"
                        >
                            {t('components:autoNumberPanel.fields.sampleCount.label')}:{' '}
                            {/* 样本数量: */}
                        </FormLabel>
                        <NumberInput
                            id={`${id}-sample-count`}
                            value={sampleCount}
                            onChange={handleSampleCountChange}
                            min={1}
                            max={96}
                            size="sm"
                            flex="1"
                        >
                            <NumberInputField />
                            <NumberInputStepper>
                                <NumberIncrementStepper />
                                <NumberDecrementStepper />
                            </NumberInputStepper>
                        </NumberInput>
                    </HStack>
                </FormControl>

                {/* 编号方向 */}
                <FormControl>
                    <VStack spacing={2} align="stretch">
                        <RadioGroup
                            id={`${id}-direction`}
                            value={direction}
                            onChange={handleDirectionChange}
                            size="sm"
                        >
                            <HStack spacing={4}>
                                <Radio value="vertical" size="sm">
                                    {t('components:autoNumberPanel.fields.direction.vertical')}{' '}
                                    {/* 纵向编号 */}
                                </Radio>
                                <Radio value="horizontal" size="sm">
                                    {t('components:autoNumberPanel.fields.direction.horizontal')}{' '}
                                    {/* 横向编号 */}
                                </Radio>
                            </HStack>
                        </RadioGroup>
                        <Checkbox
                            id={`${id}-reverse`}
                            isChecked={isReverse}
                            onChange={(e) => handleReverseChange(e.target.checked)}
                            size="sm"
                        >
                            {t('components:autoNumberPanel.fields.reverse.label')} {/* 逆序编号 */}
                        </Checkbox>
                    </VStack>
                </FormControl>
                <Divider />
                {/* 操作按钮 */}
                <Center gap={6}>
                    <Button
                        colorScheme="blue"
                        size="sm"
                        onClick={handleApplyNumbering}
                        isDisabled={!selectedWell || selectedSampleType !== 'sample'}
                    >
                        {t('components:autoNumberPanel.buttons.autoNumber')} {/* 自动编号 */}
                    </Button>
                    <Button colorScheme="blue" size="sm" onClick={handleClearSelection}>
                        {t('components:autoNumberPanel.buttons.clearNumbering')} {/* 清除编号 */}
                    </Button>
                </Center>

                {/* 选择信息 */}
                <Box>
                    <Text fontSize="sm" color="gray.600">
                        {
                            selectedCellsCount > 0
                                ? `${t('components:autoNumberPanel.messages.selectedWells')}: ${selectedWell}` // 已选择孔位: ${selectedWell}
                                : t('components:autoNumberPanel.messages.noSelectedWells') // 未选择孔位
                        }
                    </Text>
                </Box>
            </VStack>
        </Box>
    );
};
