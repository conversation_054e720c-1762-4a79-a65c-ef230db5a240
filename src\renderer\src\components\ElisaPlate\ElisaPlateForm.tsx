import React, { useState } from 'react';
import {
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    Box,
    Flex,
    Text,
    TableContainer,
    Center,
    Tooltip,
    Divider
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next'; // 导入国际化hook
import { MTPLayoutType, SampleType, getSampleTypeColor } from '@shared/commondefines';
// import DetectionInfoForm from './DetectionInfoForm';

import { PlateData, Project } from '@shared/types';
import { NumberingConfig, AutoNumberPanel } from './AutoNumberPanel';

/**
 * ELISA酶标板表单组件属性接口
 * 用于配置酶标板的显示和交互行为
 */
interface ElisaPlateFormProps {
    data?: Array<Array<number | null>>; // 8x12 的数据矩阵，存储OD值
    onWellClick?: (row: number, col: number) => void; // 孔位点击回调函数
    layoutType?: MTPLayoutType; // 布局类型，用于控制项目区域的可见性
    plateData?: PlateData; // 酶标板数据，包含孔位的样本类型信息
    selectedProject?: Project; // 当前选中的项目
    selectedProjects?: Project[]; // 所有已分配的项目列表
    onPlateDataChange?: (plateData: PlateData) => void; // 酶标板数据变化回调
    refreshKey?: number; // 刷新键，用于强制重新渲染
    selectedSampleType?: SampleType; // 当前选择的样本类型
}

/**
 * ELISA酶标板表单组件
 * 提供96孔酶标板的可视化界面，支持样本类型设置、自动编号等功能
 */
const ElisaPlateForm: React.FC<ElisaPlateFormProps> = ({
    data = [],
    onWellClick,
    layoutType,
    plateData,
    selectedProject = {} as Project,
    selectedProjects = [],
    onPlateDataChange,
    refreshKey,
    selectedSampleType = ''
}) => {
    // 使用国际化翻译hook
    const { t } = useTranslation(['components']);

    // 格式化数值显示（已注释，保留以备后用）
    // const formatValue = (value: number | undefined) => {
    //     if (value === undefined || value === null) return '--';
    //     return value.toFixed(3);
    // };

    // 定义表格布局常量
    const rowLabels = Array.from('ABCDEFGH'); // 行标签 A-H
    const columnLabels = Array.from({ length: 12 }, (_, i) => i + 1); // 列标签 1-12
    const wellHeight = '35px'; // 孔位高度
    const headerHeight = '28px'; // 表头高度
    const columnMinWidth = '50px'; // 列最小宽度
    // const gapSize = '2px'; // 间距大小

    // 布局尺寸变量
    const rowProjectWidth = '120px'; // 行项目信息区宽度
    const tableHeaderWidth = '32px'; // 行标签A~H宽度
    // const topAreaHeight = '28px'; // 顶部区域高度
    // const buttonAreaHeight = '40px'; // 功能按钮区高度

    const borderSpacing = '2px'; // 边框间距
    // const cornerWidth = `calc(${rowProjectWidth} + ${gapSize} + ${tableHeaderWidth})`;

    // 添加状态管理
    const [selectedWell, setSelectedWell] = useState<string>(''); // 格式: "A1", "B2" 等，当前选中的孔位
    const [selectedCells, setSelectedCells] = useState<Set<string>>(new Set()); // 选中的孔位集合

    // 根据layoutType判断是否显示项目信息
    const showColumnProjects = layoutType === MTPLayoutType.MultiProjectVerticalLayout; // 显示列项目信息（垂直多项目布局）
    const showRowProjects = layoutType === MTPLayoutType.MultiProjectHorizontalLayout; // 显示行项目信息（水平多项目布局）
    const isSingleProjectLayout = layoutType === MTPLayoutType.SingleProjectLayout; // 是否为单项目布局

    // 当selectedProjects变化时，可以在这里处理相关逻辑
    // 现在项目信息直接存储在projectAssignments中，不再需要projectMap
    React.useEffect(() => {
        // 可以在这里处理selectedProjects变化时的逻辑
    }, [selectedProjects]);

    // 当refreshKey变化时，清空选择状态
    React.useEffect(() => {
        if (refreshKey) {
            setSelectedWell('');
            setSelectedCells(new Set());
        }
    }, [refreshKey]);

    /**
     * 将行列索引转换为孔位标识 (如: 0,0 -> "A1")
     * @param row 行索引 (0-7)
     * @param col 列索引 (0-11)
     * @returns 孔位标识字符串
     */
    const getWellId = (row: number, col: number): string => {
        return `${rowLabels[row]}${col + 1}`;
    };

    // 将孔位标识转换为行列索引 (如: "A1" -> [0,0]) - 已注释，保留以备后用
    // const parseWellId = (wellId: string): [number, number] | null => {
    //     if (!wellId) return null;
    //     const match = wellId.match(/^([A-H])(\d+)$/);
    //     if (!match) return null;
    //     const row = match[1].charCodeAt(0) - 'A'.charCodeAt(0);
    //     const col = parseInt(match[2]) - 1;
    //     if (row < 0 || row >= 8 || col < 0 || col >= 12) return null;
    //     return [row, col];
    // };

    /**
     * 处理表格孔位点击事件
     * 实现单选模式：点击已选中的孔位取消选中，点击未选中的孔位选中当前孔位
     * @param row 行索引
     * @param col 列索引
     */
    const handleWellClick = (row: number, col: number) => {
        const wellId = getWellId(row, col);

        // 单选模式：如果点击的是已选中的孔位，则取消选中；否则选中当前孔位
        if (selectedCells.has(wellId)) {
            // 取消选中
            setSelectedWell('');
            setSelectedCells(new Set());
        } else {
            // 选中当前孔位（清空其他选中的孔位）
            setSelectedWell(wellId);
            setSelectedCells(new Set([wellId]));
        }

        // 调用原有的点击回调
        onWellClick?.(row, col);
    };

    /**
     * 处理ControlPanel中起始孔位的变化
     * @param wellId 新的起始孔位标识
     */
    const handleStartWellChange = (wellId: string) => {
        setSelectedWell(wellId);

        // 单选模式：如果有选中孔位，则只选中当前孔位；否则清空选中
        if (wellId) {
            setSelectedCells(new Set([wellId]));
        } else {
            setSelectedCells(new Set());
        }
    };

    /**
     * 将孔位标识转换为行列索引
     * @param wellId 孔位标识字符串
     * @returns 行列索引数组或null
     */
    const parseWellId = (wellId: string): [number, number] | null => {
        if (!wellId) return null;
        const match = wellId.match(/^([A-H])(\d+)$/);
        if (!match) return null;
        const row = match[1].charCodeAt(0) - 'A'.charCodeAt(0);
        const col = parseInt(match[2]) - 1;
        if (row < 0 || row >= 8 || col < 0 || col >= 12) return null;
        return [row, col];
    };

    /**
     * 生成编号序列
     * 根据配置生成孔位编号映射
     * @param config 编号配置
     * @returns 孔位编号映射
     */
    const generateNumberingSequence = (config: NumberingConfig): Map<string, number> => {
        const { startWell, startNumber, sampleCount, direction, isReverse } = config;
        const startPos = parseWellId(startWell);

        if (!startPos) return new Map();

        const [startRow, startCol] = startPos;
        const positions: string[] = [];

        if (direction === 'horizontal') {
            // 横向编号：从左向右，从上到下
            let currentRow = startRow;
            let currentCol = startCol;
            let count = 0;

            while (count < sampleCount && currentRow < 8) {
                while (currentCol < 12 && count < sampleCount) {
                    positions.push(getWellId(currentRow, currentCol));
                    currentCol++;
                    count++;
                }
                currentRow++;
                currentCol = 0; // 下一行从第一列开始
            }
        } else {
            // 纵向编号：从上到下，从左向右
            let currentRow = startRow;
            let currentCol = startCol;
            let count = 0;

            while (count < sampleCount && currentCol < 12) {
                while (currentRow < 8 && count < sampleCount) {
                    positions.push(getWellId(currentRow, currentCol));
                    currentRow++;
                    count++;
                }
                currentCol++;
                currentRow = 0; // 下一列从第一行开始
            }
        }

        // 如果逆序，则反转位置数组
        if (isReverse) {
            positions.reverse();
        }

        // 生成编号映射
        const numberMap = new Map<string, number>();
        positions.forEach((wellId, index) => {
            numberMap.set(wellId, startNumber + index);
        });

        return numberMap;
    };

    /**
     * 应用编号
     * 将生成的编号应用到选中的孔位
     * @param config 编号配置
     */
    const handleApplyNumbering = (config: NumberingConfig) => {
        const newNumberMap = generateNumberingSequence(config);

        // 更新wellData中的sampleNumber字段
        if (onPlateDataChange && plateData) {
            const newWellData = { ...plateData.wellData };

            newNumberMap.forEach((number, wellId) => {
                // 如果该孔位已有数据，更新sampleNumber；否则创建新的WellData
                if (newWellData[wellId]) {
                    newWellData[wellId] = {
                        ...newWellData[wellId],
                        sampleType: {
                            name: 'SAMPLE',
                            type: 'sample'
                        },
                        sampleNumber: number.toString()
                    };
                } else {
                    // 创建默认的WellData
                    newWellData[wellId] = {
                        sampleType: {
                            name: 'SAMPLE',
                            type: 'sample'
                        },
                        sampleNumber: number.toString(),
                        odValue: undefined,
                        odRatio: undefined,
                        result: undefined
                    };
                }
            });

            const newPlateData: PlateData = {
                layoutType: plateData.layoutType,
                singleProjectAssignments: plateData.singleProjectAssignments,
                multiProjectAssignments: plateData.multiProjectAssignments,
                wellData: newWellData
            };

            onPlateDataChange(newPlateData);
        }

        // 更新选中的孔位集合，添加新编号的孔位
        setSelectedCells((prevSelected) => {
            const newSelected = new Set(prevSelected);
            newNumberMap.forEach((_, wellId) => {
                newSelected.add(wellId);
            });
            return newSelected;
        });
    };

    /**
     * 清除选择
     * 清除所有选中的孔位和编号
     */
    const handleClearSelection = () => {
        setSelectedWell('');
        setSelectedCells(new Set());

        // 清除wellData中的sampleNumber
        if (onPlateDataChange && plateData) {
            const newWellData = { ...plateData.wellData };

            // 只清除样本类型为'sample'的孔位信息
            Object.keys(newWellData).forEach((wellId) => {
                if (newWellData[wellId] && newWellData[wellId].sampleType.type === 'sample') {
                    newWellData[wellId] = {
                        ...newWellData[wellId],
                        sampleType: {
                            name: '',
                            type: 'none'
                        },
                        sampleNumber: ''
                    };
                }
                // 其他类型的孔位（blank、nc、pc、qc、std）保持不变
            });

            const newPlateData: PlateData = {
                layoutType: plateData.layoutType,
                singleProjectAssignments: plateData.singleProjectAssignments,
                multiProjectAssignments: plateData.multiProjectAssignments,
                wellData: newWellData
            };

            onPlateDataChange(newPlateData);
        }
    };

    /**
     * 计算当前最大编号值
     * @returns 最大编号值
     */
    const getMaxNumber = (): number => {
        if (!plateData?.wellData) return 0;

        const sampleNumbers = Object.values(plateData.wellData)
            .map((well) => well.sampleNumber)
            .filter((num) => num && !isNaN(Number(num)))
            .map((num) => Number(num));

        return sampleNumbers.length > 0 ? Math.max(...sampleNumbers) : 0;
    };

    /**
     * 处理行项目信息点击
     * 在多项目水平布局模式下，为行分配或清除项目
     * @param rowIndex 行索引
     */
    const handleRowProjectClick = (rowIndex: number) => {
        if (!onPlateDataChange) return;

        const rowLabel = rowLabels[rowIndex];
        const currentAssignment = plateData?.multiProjectAssignments?.[rowLabel];

        // 如果当前行已经分配了项目，则清除分配；否则分配当前选中的项目
        const newProjectAssignments = { ...plateData?.multiProjectAssignments };

        if (currentAssignment && selectedProject?.id === currentAssignment.id) {
            // 如果点击的是当前选中的项目，则清除分配
            delete newProjectAssignments[rowLabel];
        } else if (selectedProject?.id) {
            // 分配新项目，直接保存Project对象
            newProjectAssignments[rowLabel] = selectedProject;
        }

        const newPlateData: PlateData = {
            layoutType: plateData?.layoutType || MTPLayoutType.SingleProjectLayout,
            singleProjectAssignments: plateData?.singleProjectAssignments || ({} as Project),
            multiProjectAssignments: newProjectAssignments,
            wellData: plateData?.wellData || {}
        };

        onPlateDataChange(newPlateData);
    };

    /**
     * 处理列项目信息点击
     * 在多项目垂直布局模式下，为列分配或清除项目
     * @param colIndex 列索引
     */
    const handleColumnProjectClick = (colIndex: number) => {
        if (!onPlateDataChange) return;

        const colLabel = (colIndex + 1).toString();
        const currentAssignment = plateData?.multiProjectAssignments?.[colLabel];

        // 如果当前列已经分配了项目，则清除分配；否则分配当前选中的项目
        const newProjectAssignments = { ...plateData?.multiProjectAssignments };

        if (currentAssignment && selectedProject?.id === currentAssignment.id) {
            // 如果点击的是当前选中的项目，则清除分配
            delete newProjectAssignments[colLabel];
        } else if (selectedProject?.id) {
            // 分配新项目，直接保存Project对象
            newProjectAssignments[colLabel] = selectedProject;
        }

        const newPlateData: PlateData = {
            layoutType: plateData?.layoutType || MTPLayoutType.SingleProjectLayout,
            singleProjectAssignments: plateData?.singleProjectAssignments || ({} as Project),
            multiProjectAssignments: newProjectAssignments,
            wellData: plateData?.wellData || {}
        };

        onPlateDataChange(newPlateData);
    };
    // const columnHeaderHeight = '28px'; // 表头高度

    return (
        <>
            <Flex p={1} gap={2} flex="1">
                <Flex
                    flex={1}
                    direction="column"
                    p={1}
                    gap={0}
                    border="1px solid"
                    borderColor="gray.300"
                    borderRadius={'md'}
                >
                    {/* 上方：横向排列 列项目信息区域 */}
                    <Flex align="center" p={0} gap={1}>
                        {/* 左上角项目信息区域 */}
                        <Box
                            flexShrink={0}
                            w={rowProjectWidth}
                            // h={'100%'}
                            border="1px solid"
                            borderColor={
                                isSingleProjectLayout && plateData?.singleProjectAssignments?.id
                                    ? 'blue.300'
                                    : 'gray.300'
                            }
                            fontSize="md"
                            fontWeight="bold"
                            // textAlign="left"

                            alignItems="center"
                            justifyContent="center"
                            borderRadius="md"
                            py={1}
                            // bg={
                            //     isSingleProjectLayout && plateData?.singleProjectAssignments?.id
                            //         ? 'blue.50'
                            //         : 'transparent'
                            // }
                        >
                            {isSingleProjectLayout && plateData?.singleProjectAssignments?.code
                                ? plateData.singleProjectAssignments.code
                                : isSingleProjectLayout
                                  ? t('components:elisaPlateForm.messages.selectProject') // xuzne
                                  : t('components:elisaPlateForm.messages.multiProject')}{' '}
                            {/* 使用国际化文本 */}
                        </Box>
                        {/* 左侧空白区域，对应表格左侧行标签列 */}
                        <Box
                            flexShrink={0}
                            w={tableHeaderWidth}
                            h={'28px'}
                            // border="1px solid green"
                        />
                        {/* 12列项目信息 */}
                        <Flex
                            gap={borderSpacing}
                            w="100%"
                            border="0px solid gray"
                            borderRadius="md"
                            align="center"
                            justify="center"
                        >
                            {/* 1~12 列 项目信息 */}
                            {columnLabels.map((colIndex) => {
                                const colLabel = colIndex.toString();
                                const assignedProject =
                                    plateData?.multiProjectAssignments?.[colLabel] || null;

                                return (
                                    <Box
                                        key={colIndex}
                                        minWidth={columnMinWidth}
                                        height={wellHeight}
                                        bg="transparent"
                                        border="1px solid"
                                        borderColor={assignedProject ? 'blue.300' : 'gray.300'}
                                        borderRadius="md"
                                        p={0}
                                        fontSize="sm"
                                        display="flex"
                                        flex={1}
                                        alignItems="center"
                                        justifyContent="center"
                                        visibility={
                                            showColumnProjects && !isSingleProjectLayout
                                                ? 'visible'
                                                : 'hidden'
                                        }
                                        cursor="pointer"
                                        _hover={{
                                            bg: 'blue.200 !important',
                                            zIndex: 1
                                        }}
                                        _active={{
                                            bg: 'blue.500 !important',
                                            color: 'white.500 !important',
                                            fontWeight: 'bold !important'
                                            // bg: assignedProject ? 'blue.50' : 'gray.50',
                                            // borderColor: assignedProject ? 'blue.400' : 'gray.400'
                                        }}
                                        onClick={() => handleColumnProjectClick(colIndex - 1)}
                                        title={
                                            assignedProject
                                                ? assignedProject.name
                                                : `${t('components:elisaPlateForm.messages.clickToSetProject')}${colIndex}`
                                        }
                                    >
                                        {assignedProject ? assignedProject.code : ``}
                                    </Box>
                                );
                            })}
                        </Flex>
                    </Flex>

                    {/* 下方：行项目信息 + 表格 */}
                    <Flex flex="1" p={0} m={0}>
                        {/* 左侧：8行项目信息 */}
                        <Flex
                            p={1}
                            direction="column"
                            flexShrink={0}
                            // gap={2}
                            style={{ gap: borderSpacing }}
                            // h="100%"
                            border="0px solid blue"
                            // alignItems="center"
                            justifyContent="space-between"
                        >
                            {/* 左侧上面空白区域，对应表格上侧列标签 */}
                            <Flex gap={1}>
                                <Box
                                    w={rowProjectWidth}
                                    h={headerHeight}
                                    // border="1px solid green"
                                    flexShrink={0}
                                />
                                <Box
                                    // key={'table-header'}
                                    w={tableHeaderWidth}
                                    h={headerHeight}
                                    flexShrink={0}
                                    bg="gray.400" // 填充颜色
                                    // clipPath="polygon(0% 0%, 100% 0%, 0% 100%)" // 左上直角三角形
                                    clipPath="polygon(100% 0%, 100% 100%, 0% 100%)"
                                    // border="1px solid green"
                                    // bgGradient="linear(to-br, transparent 49%, green 49%, green 51%, transparent 51%)"
                                    // fontSize="sm"
                                    // fontWeight="bold"
                                ></Box>
                            </Flex>
                            {rowLabels.map((rowLabel, rowIndex) => {
                                const assignedProject =
                                    plateData?.multiProjectAssignments?.[rowLabel] || null;

                                return (
                                    <Flex key={`${rowLabel}-row-project-info`} flex={1} gap={1}>
                                        <Box
                                            key={`${rowLabel}-row-project-info-box`}
                                            width={rowProjectWidth}
                                            // height={wellHeight}
                                            bg="transparent"
                                            border="1px solid"
                                            borderColor={assignedProject ? 'blue.300' : 'gray.300'}
                                            borderRadius="md"
                                            // p={0}
                                            // m={0}
                                            fontSize="sm"
                                            display="flex"
                                            flex={1}
                                            alignItems="center"
                                            justifyContent="center"
                                            visibility={
                                                showRowProjects && !isSingleProjectLayout
                                                    ? 'visible'
                                                    : 'hidden'
                                            }
                                            cursor="pointer"
                                            _hover={{
                                                bg: 'blue.200 !important'
                                                // bg: assignedProject ? 'blue.50' : 'gray.50',
                                                // borderColor: assignedProject
                                                //     ? 'blue.400'
                                                //     : 'gray.400'
                                            }}
                                            onClick={() => handleRowProjectClick(rowIndex)}
                                            title={
                                                assignedProject
                                                    ? assignedProject.name
                                                    : `${t('components:elisaPlateForm.messages.clickToSetProject')}${rowLabel}`
                                            }
                                        >
                                            {assignedProject ? assignedProject.code : ``}
                                        </Box>
                                        <Center
                                            key={`${rowLabel}-table-header`}
                                            width={tableHeaderWidth}
                                            flexShrink={0}
                                            border="1px solid green"
                                            fontSize="sm"
                                            fontWeight="bold"
                                        >
                                            {rowLabel.trim()}
                                        </Center>
                                    </Flex>
                                );
                            })}
                        </Flex>

                        {/* 96 孔表格 */}
                        <TableContainer display="flex" flex="1">
                            <Table
                                style={{
                                    borderSpacing: borderSpacing,
                                    borderCollapse: 'separate',
                                    tableLayout: 'fixed'
                                }}
                            >
                                {/* 表头 */}
                                <Thead>
                                    <Tr>
                                        {columnLabels.map((columnLabel) => (
                                            <Th
                                                key={`${columnLabel}-table-header`}
                                                height={headerHeight}
                                                p={1}
                                                textAlign="center"
                                                border="1px solid green"
                                                fontSize="sm"
                                                fontWeight="bold"
                                            >
                                                {columnLabel}
                                            </Th>
                                        ))}
                                    </Tr>
                                </Thead>
                                <Tbody>
                                    {rowLabels.map((rowLabel, rowIndex) => (
                                        <Tr
                                            key={`${rowLabel}-table-row`}
                                            bg={rowIndex % 2 === 1 ? 'gray.50' : 'transparent'}
                                        >
                                            {Array.from({ length: 12 }, (_, colIndex) => {
                                                const value = data[rowIndex]?.[colIndex];
                                                const wellId = getWellId(rowIndex, colIndex);
                                                const wellData = plateData?.wellData?.[wellId] || {
                                                    sampleType: { name: '', type: 'none' },
                                                    sampleNumber: '',
                                                    odMain: undefined,
                                                    odRef: undefined,
                                                    odValue: undefined,
                                                    odRatio: undefined,
                                                    concentration: undefined,
                                                    result: undefined
                                                };
                                                const sampleTypeColor = getSampleTypeColor(
                                                    wellData?.sampleType.type as SampleType
                                                );

                                                return (
                                                    <Td
                                                        key={colIndex}
                                                        p={0}
                                                        m={0}
                                                        // textAlign="center"
                                                        height={wellHeight}
                                                        // minHeight={wellHeight}
                                                        // maxHeight={wellHeight}
                                                        // display="flex"
                                                        // alignItems="center"
                                                        // justifyContent="center"
                                                        cursor="pointer"
                                                        border="1px solid"
                                                        borderRadius="md"
                                                        borderColor={
                                                            wellData?.sampleNumber
                                                                ? 'blue.400 !important'
                                                                : selectedCells.has(wellId)
                                                                  ? 'blue.500 !important'
                                                                  : wellData?.sampleType
                                                                    ? `${sampleTypeColor.border} !important`
                                                                    : 'gray.300 !important'
                                                        }
                                                        bg={
                                                            wellData?.sampleNumber
                                                                ? 'blue.50'
                                                                : selectedCells.has(wellId)
                                                                  ? 'blue.50'
                                                                  : wellData?.sampleType
                                                                    ? sampleTypeColor.bg
                                                                    : 'transparent'
                                                        }
                                                        _hover={{
                                                            border: '1px solid !important',
                                                            borderColor: 'blue.200 !important',
                                                            bg: 'gray.300 !important',

                                                            zIndex: 1
                                                        }}
                                                        _active={{
                                                            border: '1px solid !important',
                                                            borderColor: 'blue.500 !important',
                                                            bg: 'blue.500 !important',
                                                            color: 'white.500 !important',
                                                            fontWeight: 'bold !important',
                                                            zIndex: 1
                                                        }}
                                                        onClick={() =>
                                                            handleWellClick(rowIndex, colIndex)
                                                        }
                                                    >
                                                        <Tooltip
                                                            label={
                                                                <Box p={0}>
                                                                    <Text
                                                                        fontSize="md"
                                                                        fontWeight="bold"
                                                                    >
                                                                        {t(
                                                                            'components:elisaPlateForm.messages.wellPosition'
                                                                        )}
                                                                        : {wellId}{' '}
                                                                        {/* 使用国际化文本 */}
                                                                    </Text>
                                                                    <Divider my={2} size="md" />{' '}
                                                                    {/* 分割线 */}
                                                                    <Text
                                                                        fontSize="md"
                                                                        color={sampleTypeColor.text}
                                                                    >
                                                                        {t(
                                                                            'components:elisaPlateForm.messages.sampleType'
                                                                        )}
                                                                        : {/* 使用国际化文本 */}
                                                                        {wellData?.sampleType
                                                                            .type || ''}
                                                                    </Text>
                                                                    <Text fontSize="md">
                                                                        {t(
                                                                            'components:elisaPlateForm.messages.sampleNumber'
                                                                        )}
                                                                        : {/* 使用国际化文本 */}
                                                                        {wellData?.sampleNumber ||
                                                                            ''}
                                                                    </Text>
                                                                    <Text fontSize="md">
                                                                        {t(
                                                                            'components:elisaPlateForm.messages.mainWavelengthOD'
                                                                        )}
                                                                        : {/* 使用国际化文本 */}
                                                                        {wellData?.odMain || 0}
                                                                        {/* {formatValue()} */}
                                                                    </Text>
                                                                    <Text fontSize="md">
                                                                        {t(
                                                                            'components:elisaPlateForm.messages.refWavelengthOD'
                                                                        )}
                                                                        : {/* 使用国际化文本 */}
                                                                        {wellData?.odRef || 0}
                                                                    </Text>
                                                                    {/* <Text fontSize="md">
                                                                        OD: {wellData?.odValue || 0}
                                                                    </Text>
                                                                    <Text fontSize="md">
                                                                        sco值:{' '}
                                                                        {wellData?.odRatio || 0}
                                                                    </Text> */}
                                                                </Box>
                                                            }
                                                            placement="top"
                                                            hasArrow
                                                            bg="white"
                                                            color="gray.800"
                                                            border="2px solid"
                                                            borderColor="gray.200"
                                                            borderRadius="md"
                                                            boxShadow="lg"
                                                            // maxW="200px"
                                                        >
                                                            <Box
                                                                width="100%"
                                                                height="100%"
                                                                display="flex"
                                                                alignItems="center"
                                                                justifyContent="center"
                                                                p={0}
                                                                m={0}
                                                                // border="1px solid red"
                                                                // zIndex={10}
                                                            >
                                                                {wellData?.sampleNumber ? (
                                                                    <Text
                                                                        fontSize="sm"
                                                                        fontWeight="bold"
                                                                        color="blue.600"
                                                                        whiteSpace="nowrap"
                                                                        overflow="hidden"
                                                                        textOverflow="ellipsis"
                                                                        // maxWidth="100%"
                                                                        // textAlign="center"
                                                                    >
                                                                        {wellData.sampleNumber}
                                                                    </Text>
                                                                ) : wellData?.sampleType ? (
                                                                    <Text
                                                                        fontSize="md"
                                                                        fontWeight="bold"
                                                                        color={sampleTypeColor.text}
                                                                        whiteSpace="nowrap"
                                                                        overflow="hidden"
                                                                        textOverflow="ellipsis"
                                                                        // maxWidth="100%"
                                                                        // textAlign="center"
                                                                    >
                                                                        {wellData.sampleType.name}
                                                                    </Text>
                                                                ) : value !== null ? (
                                                                    <Text
                                                                        fontSize="sm"
                                                                        textAlign="center"
                                                                    >
                                                                        {value}
                                                                    </Text>
                                                                ) : (
                                                                    <Text
                                                                        fontSize="sm"
                                                                        color="gray.400"
                                                                        textAlign="center"
                                                                    ></Text>
                                                                )}
                                                            </Box>
                                                        </Tooltip>
                                                    </Td>
                                                );
                                            })}
                                        </Tr>
                                    ))}
                                </Tbody>
                            </Table>
                        </TableContainer>
                    </Flex>
                </Flex>

                {/* 样本自动编号面板 */}
                <Box
                    flexShrink={0}
                    border="1px solid"
                    borderColor="gray.300"
                    borderRadius="md"
                    w="300px"
                >
                    <AutoNumberPanel
                        selectedWell={selectedWell}
                        onStartWellChange={handleStartWellChange}
                        selectedCellsCount={selectedCells.size}
                        onApplyNumbering={handleApplyNumbering}
                        onClearSelection={handleClearSelection}
                        maxNumber={getMaxNumber()}
                        selectedSampleType={selectedSampleType}
                    />
                </Box>
            </Flex>
        </>
    );
};

export default ElisaPlateForm;
