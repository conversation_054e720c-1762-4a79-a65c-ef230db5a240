import React from 'react';
import { RadioGroup, Radio, FormControl, FormLabel, SimpleGrid, Text } from '@chakra-ui/react';
import { SampleTypeVariable, PREDEFINED_SAMPLE_TYPES, getSampleTypeColor, SampleType } from '@shared/commondefines';
import { useTranslation } from 'react-i18next';

interface SampleTypeFormProps {
    id?: string;
    initialType?: SampleTypeVariable;
    onTypeChange?: (type: SampleTypeVariable) => void;
}

const SampleTypeForm: React.FC<SampleTypeFormProps> = ({ id, initialType, onTypeChange }) => {
    const { t } = useTranslation(['components', 'common']);
    // 设置默认值为 SAMPLE
    const defaultType: SampleTypeVariable = { name: 'SAMPLE', type: 'sample' };

    const [selectedType, setSelectedType] = React.useState<SampleTypeVariable>(defaultType || initialType);

    const handleChange = (value: SampleTypeVariable) => {
        setSelectedType(value);
        onTypeChange?.(value);
        console.log('sampleType', value);
    };

    // 当 initialType 改变时，更新 selectedType
    React.useEffect(() => {
        if (initialType) {
            setSelectedType(initialType);
            // if (initialType.type === 'none') {
            //     setSelectedType(defaultType);
            // } else {
            //     setSelectedType(initialType);
            // }
        }
    }, [initialType]);

    // 按 type 分组
    const groupedTypes = {
        sampleAndBlank: PREDEFINED_SAMPLE_TYPES.filter((type) => type.type === 'sample' || type.type === 'blank'),
        nc: PREDEFINED_SAMPLE_TYPES.filter((type) => type.type === 'nc'),
        pc: PREDEFINED_SAMPLE_TYPES.filter((type) => type.type === 'pc'),
        qc: PREDEFINED_SAMPLE_TYPES.filter((type) => type.type === 'qc'),
        std: PREDEFINED_SAMPLE_TYPES.filter((type) => type.type === 'std')
    };

    // 生成悬浮效果的函数
    const getHoverEffect = (type: SampleType) => ({
        bg: getSampleTypeColor(type).bg,
        borderRadius: 'md',
        borderWidth: '1px',
        borderColor: getSampleTypeColor(type).border
    });

    // 渲染Radio组件的函数
    const renderRadio = (type: SampleTypeVariable, gridColumn?: string) => (
        <Radio
            key={type.name}
            value={type.name}
            colorScheme={getSampleTypeColor(type.type).scheme}
            gridColumn={gridColumn}
            size="lg"
            _hover={getHoverEffect(type.type)}
        >
            <Text
                as="span"
                color={getSampleTypeColor(type.type).text}
                fontSize="md"
                _hover={{
                    color: getSampleTypeColor(type.type).text,
                    fontWeight: 'semibold',
                    borderWidth: '2px',

                    borderColor: getSampleTypeColor(type.type).border,
                    // borderColor: 'gray.500',
                    borderRadius: 'md'
                }}
            >
                {type.name}
            </Text>
        </Radio>
    );

    return (
        <FormControl
            id={id}
            as="fieldset"
            borderWidth="1px"
            borderRadius="md"
            p={{ base: 1, md: 2, lg: 3 }}
            pt={0}
            h="100%"
            display="flex"
            flexDirection="column"
            // alignItems="center"
            // justifyContent="center"
            // p={2}
            // pt={0}
        >
            <FormLabel as="legend" fontSize="md">
                {/* 标本类型 */}
                {t('components:sampleTypeForm.title')}
            </FormLabel>

            <RadioGroup
                value={selectedType.name}
                onChange={(name) => {
                    const foundType = PREDEFINED_SAMPLE_TYPES.find((item) => item.name === name);
                    if (foundType) {
                        handleChange(foundType);
                    } else {
                        handleChange(defaultType);
                    }
                }}
                flex="1"
                display="flex"
                flexDirection="column"
            >
                <SimpleGrid columns={4} spacing={2} pt={0} flex="1" alignItems="center">
                    {/* Sample 和 Blank 各占两列 */}
                    {groupedTypes.sampleAndBlank.map((type, index) =>
                        renderRadio(type, index === 0 ? '1 / 3' : '3 / 5')
                    )}

                    {groupedTypes.nc.map((type) => renderRadio(type))}

                    {groupedTypes.pc.map((type) => renderRadio(type))}

                    {groupedTypes.qc.map((type) => renderRadio(type))}

                    {/* {groupedTypes.std.map((type) => renderRadio(type))} */}
                </SimpleGrid>
            </RadioGroup>
        </FormControl>
    );
};

export default SampleTypeForm;
