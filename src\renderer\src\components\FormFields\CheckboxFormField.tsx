import React from 'react';
import { FormControl, Checkbox, Flex } from '@chakra-ui/react';
import { styles } from '@renderer/utils/theme';

interface CheckboxFormFieldProps {
    label: string;
    isChecked: boolean;
    onChange: (checked: boolean) => void;
    isDisabled?: boolean;
    gridColumn?: string;
}

const CheckboxFormField: React.FC<CheckboxFormFieldProps> = ({ label, isChecked, onChange, isDisabled = false, gridColumn }) => {
    return (
        <FormControl isDisabled={isDisabled} gridColumn={gridColumn}>
            <Flex align="center">
                <Checkbox sx={styles.label_normal} px={5} isChecked={isChecked} onChange={(e) => onChange(e.target.checked)}>
                    {label}
                </Checkbox>
            </Flex>
        </FormControl>
    );
};

export default CheckboxFormField;
