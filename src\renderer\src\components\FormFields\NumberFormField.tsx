import React from 'react';
import { FormControl, FormLabel, NumberInput, NumberInputField, Flex, InputGroup, InputRightElement } from '@chakra-ui/react';
import { styles } from '@renderer/utils/theme';

interface NumberFormFieldProps {
    label: string;
    value: number | string;
    onChange: (value: string | number) => void;
    isRequired?: boolean;
    isDisabled?: boolean;
    gridColumn?: string;
    rightText?: string; // "(秒)", "(下限)", "(上限)" 等
}

const NumberFormField: React.FC<NumberFormFieldProps> = ({ label, value, onChange, isRequired = false, isDisabled = false, gridColumn, rightText }) => {
    return (
        <FormControl isRequired={isRequired} isDisabled={isDisabled} gridColumn={gridColumn}>
            <Flex align="center">
                <FormLabel sx={styles.label_normal} fontSize="lg" mb={0} minW="90px" textAlign="right">
                    {isRequired ? label : `${label}\u00A0\u00A0`}
                </FormLabel>
                <NumberInput flex="1" size="md" value={value} onChange={onChange}>
                    {rightText ? (
                        <InputGroup>
                            <InputRightElement
                                pointerEvents="none"
                                color="gray.400"
                                fontSize="sm"
                                sx={{
                                    paddingRight: '5px',
                                    whiteSpace: 'nowrap'
                                }}
                            >
                                <span>{rightText}</span>
                            </InputRightElement>
                            <NumberInputField fontSize="md" px={1} py={0} />
                        </InputGroup>
                    ) : (
                        <NumberInputField fontSize="md" px={1} py={0} />
                    )}
                </NumberInput>
            </Flex>
        </FormControl>
    );
};

export default NumberFormField;
