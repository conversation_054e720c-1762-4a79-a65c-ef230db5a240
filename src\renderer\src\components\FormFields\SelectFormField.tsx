import React from 'react';
import { FormControl, FormLabel, Select, Flex } from '@chakra-ui/react';
import { styles } from '@renderer/utils/theme';

interface SelectOption {
    readonly id: number | string;
    readonly label: string;
}

interface SelectFormFieldProps {
    label: string;
    value: number | string;
    onChange: (value: number | string) => void;
    options: readonly SelectOption[];
    isRequired?: boolean;
    isDisabled?: boolean;
    gridColumn?: string;
    placeholder?: string;
}

const SelectFormField: React.FC<SelectFormFieldProps> = ({
    label,
    value,
    onChange,
    options,
    isRequired = false,
    isDisabled = false,
    gridColumn,
    placeholder
}) => {
    return (
        <FormControl isRequired={isRequired} isDisabled={isDisabled} gridColumn={gridColumn}>
            <Flex align="center">
                <FormLabel sx={styles.label_normal} mb={0} minW="90px" textAlign="right">
                    {isRequired ? label : `${label}\u00A0\u00A0`}
                </FormLabel>
                <Select
                    flex="1"
                    fontSize="md"
                    sx={{
                        paddingLeft: 1,
                        paddingRight: 1
                    }}
                    placeholder={placeholder}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                >
                    {options.map((option) => (
                        <option key={option.id} value={option.id}>
                            {option.label}
                        </option>
                    ))}
                </Select>
            </Flex>
        </FormControl>
    );
};

export default SelectFormField;
