import React from 'react';
import { FormControl, FormLabel, Input, Flex } from '@chakra-ui/react';
import { styles } from '@renderer/utils/theme';

interface TextFormFieldProps {
    label: string;
    value: string;
    onChange: (value: string) => void;
    isRequired?: boolean;
    isDisabled?: boolean;
    gridColumn?: string;
    placeholder?: string;
    isReadOnly?: boolean;
}

const TextFormField: React.FC<TextFormFieldProps> = ({
    label,
    value,
    onChange,
    isRequired = false,
    isDisabled = false,
    gridColumn,
    placeholder,
    isReadOnly = false
}) => {
    return (
        <FormControl isRequired={isRequired} isDisabled={isDisabled} gridColumn={gridColumn}>
            <Flex align="center">
                <FormLabel
                    sx={styles.label_normal}
                    fontSize="lg"
                    mb={0}
                    minW="90px"
                    textAlign="right"
                >
                    {isRequired ? label : `${label}\u00A0\u00A0`}
                </FormLabel>
                <Input
                    flex="1"
                    fontSize="md"
                    px={1}
                    placeholder={placeholder}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    isReadOnly={isReadOnly}
                />
            </Flex>
        </FormControl>
    );
};

export default TextFormField;
