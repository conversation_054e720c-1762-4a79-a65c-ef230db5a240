/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/prop-types */
import React, { useRef, useState, useEffect, useMemo, useCallback } from 'react';
import {
    Box,
    VStack,
    HStack,
    Text,
    Button,
    FormControl,
    Tooltip,
    Badge,
    SimpleGrid,
    useToast
} from '@chakra-ui/react';
import { CheckIcon, WarningIcon, ArrowBackIcon, IconButton } from '@chakra-ui/icons';
import * as math from 'mathjs';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { EditorView } from '@codemirror/view';
import { tags } from '@lezer/highlight';
import { HighlightStyle, syntaxHighlighting } from '@codemirror/language';
import { PREDEFINED_SAMPLE_TYPES } from '@shared/commondefines';
import logger from '../utils/logger';
import { useTranslation } from 'react-i18next';

// 定义编辑器引用类型
interface EditorRef {
    view: EditorView;
}

// // 定义样本变量类型
// export interface SampleTypeVariable {
//     name: string;
//     type: 'sample' | 'blank' | 'nc' | 'pc' | 'qc' | 'std';
//     description: string;
//     category: string;
// }

// // 预定义样本的变量类型
// const PREDEFINED_SAMPLE_TYPES: SampleTypeVariable[] = [
//     { name: 'SAMPLE', type: 'sample', description: '样本', category: '样本' },
//     { name: 'BLANK', type: 'blank', description: '空白对照值', category: '空白对照' },
//     { name: 'NC1', type: 'nc', description: '阴性对照1', category: '阴性对照' },
//     { name: 'NC2', type: 'nc', description: '阴性对照2', category: '阴性对照' },
//     { name: 'NC3', type: 'nc', description: '阴性对照3', category: '阴性对照' },
//     { name: 'NC4', type: 'nc', description: '阴性对照4', category: '阴性对照' },
//     { name: 'PC1', type: 'pc', description: '阳性对照1', category: '阳性对照' },
//     { name: 'PC2', type: 'pc', description: '阳性对照2', category: '阳性对照' },
//     { name: 'PC3', type: 'pc', description: '阳性对照3', category: '阳性对照' },
//     { name: 'PC4', type: 'pc', description: '阳性对照4', category: '阳性对照' },
//     { name: 'QC1', type: 'qc', description: '质控品1', category: '质控品' },
//     { name: 'QC2', type: 'qc', description: '质控品2', category: '质控品' },
//     { name: 'QC3', type: 'qc', description: '质控品3', category: '质控品' },
//     { name: 'QC4', type: 'qc', description: '质控品4', category: '质控品' },
//     { name: 'STD0', type: 'std', description: '标准品0', category: '标准品' },
//     { name: 'STD1', type: 'std', description: '标准品1', category: '标准品' },
//     { name: 'STD2', type: 'std', description: '标准品2', category: '标准品' },
//     { name: 'STD3', type: 'std', description: '标准品3', category: '标准品' },
//     { name: 'STD4', type: 'std', description: '标准品4', category: '标准品' },
//     { name: 'STD5', type: 'std', description: '标准品5', category: '标准品' },
//     { name: 'STD6', type: 'std', description: '标准品6', category: '标准品' },
//     { name: 'STD7', type: 'std', description: '标准品7', category: '标准品' },
//     { name: 'STD8', type: 'std', description: '标准品8', category: '标准品' },
//     { name: 'STD9', type: 'std', description: '标准品9', category: '标准品' }
// ] as const;

// 默认模拟数据 - 基于预定义变量的示例值（数组形式，模拟实际检测重复孔）
const DEFAULT_SIMULATION_DATA: Record<string, number[]> = {
    BLANK: [0.001, 0.001, 0.002, 0.003, 0.005],
    NC1: [0.048, 0.052, 0.05],
    NC2: [0.058, 0.062, 0.06],
    NC3: [0.038, 0.042, 0.04],
    NC4: [0.041, 0.039, 0.04],
    PC1: [0.78, 0.82, 0.8],
    PC2: [0.83, 0.87, 0.85],
    PC3: [0.8, 0.84, 0.82],
    PC4: [0.81, 0.85, 0.83],
    QC1: [0.69, 0.73, 0.71],
    QC2: [0.7, 0.74, 0.72],
    QC3: [0.71, 0.75, 0.73],
    QC4: [0.72, 0.76, 0.74],
    STD1: [0.18, 0.22, 0.2],
    STD2: [0.23, 0.27, 0.25],
    STD3: [0.28, 0.32, 0.3],
    STD4: [0.33, 0.37, 0.35],
    STD5: [0.38, 0.42, 0.4],
    STD6: [0.43, 0.47, 0.45],
    STD7: [0.48, 0.52, 0.5],
    STD8: [0.53, 0.57, 0.55],
    STD9: [0.58, 0.62, 0.6]
};

// 预定义的函数 - 使用 mathjs 内置统计函数
// 函数定义已移动到组件内部，使用国际化资源

// // 运算符
// const OPERATORS = [
//     { symbol: '+', value: ' + ', description: '加法', category: '算术' }, // Addition
//     { symbol: '-', value: ' - ', description: '减法', category: '算术' }, // Subtraction
//     { symbol: '*', value: ' * ', description: '乘法', category: '算术' }, // Multiplication
//     { symbol: '/', value: ' / ', description: '除法', category: '算术' }, // Division
//     { symbol: '<=', value: ' <= ', description: '小于等于', category: '比较' }, // Less than or equal
//     { symbol: '>=', value: ' >= ', description: '大于等于', category: '比较' }, // Greater than or equal
//     { symbol: '<', value: ' < ', description: '小于', category: '比较' }, // Less than
//     { symbol: '>', value: ' > ', description: '大于', category: '比较' }, // Greater than
//     { symbol: '=', value: ' == ', description: '等于', category: '比较' }, // Equal
//     { symbol: '!=', value: ' != ', description: '不等于', category: '比较' }, // Not equal
//     { symbol: 'and', value: ' and ', description: '逻辑与', category: '逻辑' }, // Logical AND
//     { symbol: 'or', value: ' or ', description: '逻辑或', category: '逻辑' }, // Logical OR
//     { symbol: 'not', value: 'not ', description: '逻辑非', category: '逻辑' }, // Logical NOT
//     { symbol: '?', value: ' ? ', description: '三元运算符', category: '条件' }, // Ternary operator
//     { symbol: ':', value: ' : ', description: '三元运算符分隔符', category: '条件' }, // Ternary separator
//     { symbol: ',', value: ', ', description: '', category: '分隔符' }, // Separator
//     { symbol: '.', value: '.', description: '', category: '分隔符' }, // Decimal point
//     { symbol: '1', value: '1', description: '', category: '数字' }, // Number
//     { symbol: '2', value: '2', description: '', category: '数字' }, // Number
//     { symbol: '3', value: '3', description: '', category: '数字' }, // Number
//     { symbol: '4', value: '4', description: '', category: '数字' }, // Number
//     { symbol: '5', value: '5', description: '', category: '数字' }, // Number
//     { symbol: '6', value: '6', description: '', category: '数字' }, // Number
//     { symbol: '7', value: '7', description: '', category: '数字' }, // Number
//     { symbol: '8', value: '8', description: '', category: '数字' }, // Number
//     { symbol: '9', value: '9', description: '', category: '数字' }, // Number
//     { symbol: '0', value: '0', description: '', category: '数字' } // Number
// ];

// 运算符
const OPERATORS = [
    { symbol: '+', value: ' + ' }, // Addition
    { symbol: '-', value: ' - ' }, // Subtraction
    { symbol: '*', value: ' * ' }, // Multiplication
    { symbol: '/', value: ' / ' }, // Division
    { symbol: '<=', value: ' <= ' }, // Less than or equal
    { symbol: '>=', value: ' >= ' }, // Greater than or equal
    { symbol: '<', value: ' < ' }, // Less than
    { symbol: '>', value: ' > ' }, // Greater than
    { symbol: '=', value: ' == ' }, // Equal
    { symbol: '!=', value: ' != ' }, // Not equal
    { symbol: 'and', value: ' and ' }, // Logical AND
    { symbol: 'or', value: ' or ' }, // Logical OR
    { symbol: 'not', value: 'not ' }, // Logical NOT
    { symbol: '?', value: ' ? ' }, // Ternary operator
    { symbol: ':', value: ' : ' }, // Ternary separator
    { symbol: ',', value: ', ' }, // Separator
    { symbol: '.', value: '.' }, // Decimal point
    { symbol: '1', value: '1' }, // Number
    { symbol: '2', value: '2' }, // Number
    { symbol: '3', value: '3' }, // Number
    { symbol: '4', value: '4' }, // Number
    { symbol: '5', value: '5' }, // Number
    { symbol: '6', value: '6' }, // Number
    { symbol: '7', value: '7' }, // Number
    { symbol: '8', value: '8' }, // Number
    { symbol: '9', value: '9' }, // Number
    { symbol: '0', value: '0' } // Number
];

// 定义组件属性类型
interface FormulaEditorProps {
    initialValue: string;
    onChange: (formula: string) => void;
    onValidate?: (isValid: boolean, error?: string) => void;
    isRequired?: boolean;
    testData?: Record<string, number | number[]>;
    readOnly?: boolean;
    showCursorPosition?: boolean;
    showValidation?: boolean;
    height?: string;
}

// 语法高亮自定义
const formulaHighlight = HighlightStyle.define([
    { tag: tags.variableName, color: '#0066cc', fontWeight: 'bold' },
    { tag: tags.function(tags.variableName), color: '#9933cc', fontWeight: 'bold' },
    { tag: tags.operator, color: '#cc3300', fontWeight: 'bold' },
    { tag: tags.keyword, color: '#cc6600', fontWeight: 'bold' },
    { tag: tags.paren, color: '#666666', fontWeight: 'bold' },
    { tag: tags.number, color: '#cc0033', fontWeight: 'bold' }
]);

export const FormulaEditor = React.memo<FormulaEditorProps>(
    ({
        initialValue,
        onChange,
        onValidate,
        isRequired = false,
        testData = {},
        readOnly = false,
        showCursorPosition = true,
        showValidation = true,
        height = '100px'
    }) => {
        const toast = useToast();
        const { t } = useTranslation(['common', 'components']);

        // 使用国际化资源的函数定义
        const PREDEFINED_FUNCTIONS = useMemo(
            () => [
                {
                    name: 'mean',
                    description: t('components:formulaEditor.functions.mean.description'),
                    example: 'mean(NC1) or mean(flatten([NC1, NC2]))',
                    category: t('components:formulaEditor.functions.mean.category')
                },
                {
                    name: 'max',
                    description: t('components:formulaEditor.functions.max.description'),
                    example: 'max(NC1) or max(mean(NC1), mean(NC2))',
                    category: t('components:formulaEditor.functions.max.category')
                },
                {
                    name: 'min',
                    description: t('components:formulaEditor.functions.min.description'),
                    example: 'min(NC1) or min(mean(NC1), mean(NC2))',
                    category: t('components:formulaEditor.functions.min.category')
                },
                {
                    name: 'sum',
                    description: t('components:formulaEditor.functions.sum.description'),
                    example: 'sum(NC1) or sum(flatten([NC1, NC2]))',
                    category: t('components:formulaEditor.functions.sum.category')
                }
            ],
            [t]
        );
        const [isValid, setIsValid] = useState(true);
        const [errorMessage, setErrorMessage] = useState('');
        const [testResult, setTestResult] = useState<number | null>(null);
        const [internalValue, setInternalValue] = useState<string>(initialValue || '');
        const editorRef = useRef<EditorRef | null>(null);
        const [cursorPosition, setCursorPosition] = useState(0);

        // 自定义扩展：高亮变量/函数/运算符 // Custom extension: highlight variables/functions/operators
        const customHighlight = useMemo(() => EditorView.theme({}, { dark: false }), []);

        // 同步 initialValue 的变化到 internalValue // Sync initialValue changes to internalValue
        useEffect(() => {
            setInternalValue(initialValue || '');
        }, [initialValue]);

        // 处理内容变化 // Handle content changes
        const handleChange = useCallback(
            (value: string) => {
                // 获取当前编辑器实例 // Get current editor instance
                if (!editorRef.current) return;

                const editor = editorRef.current.view;
                const cursorPos = editor.state.selection.main.head;

                // 默认转换为小写，只有变量名需要大写 // Convert to lowercase by default, only variable names need uppercase
                let safeValue = (value || '').toLowerCase();

                // 处理中文标点符号，转换为英文标点符号 // Handle Chinese punctuation, convert to English punctuation
                safeValue = safeValue
                    .replace(/＋/g, ' + ') // 中文加号 → 英文加号
                    .replace(/－/g, ' - ') // 中文减号 → 英文减号
                    .replace(/×/g, ' * ') // 中文乘号 → 英文乘号
                    .replace(/÷/g, ' / ') // 中文除号 → 英文除号
                    .replace(/，/g, ',') // 中文逗号 → 英文逗号
                    .replace(/（/g, '(') // 中文左括号 → 英文左括号
                    .replace(/）/g, ')') // 中文右括号 → 英文右括号
                    .replace(/。/g, '.') // 中文句号 → 英文句号
                    .replace(/；/g, ';'); // 中文分号 → 英文分号

                try {
                    // 找出所有变量名并转换为大写
                    PREDEFINED_SAMPLE_TYPES.forEach(({ name }) => {
                        if (!name) return; // 跳过空名称
                        const regex = new RegExp(`\\b${name.toLowerCase()}\\b`, 'g');
                        safeValue = safeValue.replace(regex, name.toUpperCase());
                    });

                    // 如果值没有变化，不需要更新
                    if (safeValue === internalValue) return;

                    setInternalValue(safeValue);
                    onChange(safeValue);

                    // 在下一个事件循环中恢复光标位置
                    requestAnimationFrame(() => {
                        const updatedEditor = editorRef.current?.view;
                        if (updatedEditor) {
                            try {
                                // 确保新的光标位置不超出文档范围
                                const newPos = Math.min(cursorPos, safeValue.length);
                                if (newPos >= 0 && newPos <= updatedEditor.state.doc.length) {
                                    updatedEditor.dispatch({
                                        selection: { anchor: newPos, head: newPos }
                                    });
                                }
                            } catch (error) {
                                // 忽略光标位置更新错误，不影响主要功能
                                logger.error('Cursor position update skipped:', error, {
                                    component: './src/renderer/src/components/FormEditor.tsx'
                                });
                            }
                        }
                    });
                } catch (error) {
                    logger.error('Error in handleChange:', error, {
                        component: './src/renderer/src/components/FormEditor.tsx'
                    });

                    // 发生错误时至少保存当前值
                    setInternalValue(safeValue);
                    onChange(safeValue);
                }
            },
            [onChange, internalValue]
        );

        // 在光标位置插入文本
        const insertAtCursor = useCallback(
            (text: string) => {
                if (!editorRef.current) return;

                const editor = editorRef.current.view;
                const cursorPos = editor.state.selection.main.head;
                const currentContent = editor.state.doc.toString();

                // 构建新的内容：光标前的内容 + 新文本 + 光标后的内容
                const newContent =
                    currentContent.substring(0, cursorPos) +
                    text +
                    currentContent.substring(cursorPos);

                // 计算新的光标位置 - 确保使用准确的文本长度
                const newCursorPos = cursorPos + text.length;

                // 先更新编辑器内容，避免通过handleChange触发额外的处理
                editor.dispatch({
                    changes: {
                        from: cursorPos,
                        to: cursorPos,
                        insert: text
                    },
                    selection: { anchor: newCursorPos, head: newCursorPos }
                });

                // 更新内部状态
                setInternalValue(newContent);
                onChange(newContent);
            },
            [onChange]
        );

        // 处理退格键删除操作
        const handleBackspace = useCallback(
            (e: React.MouseEvent) => {
                e.preventDefault();
                const editor = editorRef.current?.view;
                if (!editor) return;

                const doc = editor.state.doc;
                const pos = editor.state.selection.main.head;

                // 检查是否可以删除
                if (pos <= 0 || pos > doc.length) return;

                // 获取当前内容
                const content = doc.toString();
                const newContent = content.slice(0, pos - 1) + content.slice(pos);

                // 计算新的光标位置
                const newPos = pos - 1;

                // 先更新内容
                handleChange(newContent);

                // 等待内容更新完成后再设置光标位置
                requestAnimationFrame(() => {
                    const updatedEditor = editorRef.current?.view;
                    if (updatedEditor && newPos >= 0 && newPos <= updatedEditor.state.doc.length) {
                        try {
                            updatedEditor.dispatch({
                                selection: { anchor: newPos, head: newPos }
                            });
                        } catch (error) {
                            logger.error('Failed to update cursor position:', error, {
                                component: './src/renderer/src/components/FormEditor.tsx'
                            });
                        }
                    }
                });
            },
            [handleChange]
        );

        // 手动校验
        const handleValidate = useCallback(() => {
            try {
                // 检查函数之间是否缺少运算符
                const formula = internalValue.trim();

                // 构建所有函数名的正则表达式
                const functionNames = PREDEFINED_FUNCTIONS.map((f) => f.name.toLowerCase()).join(
                    '|'
                );
                const functionRegex = new RegExp(`\\b(${functionNames})\\s*\\([^)]*\\)`, 'gi');

                // 获取所有函数调用
                const matches = [...formula.matchAll(functionRegex)];

                // 检查相邻函数之间是否有运算符
                for (let i = 0; i < matches.length - 1; i++) {
                    const currentMatch = matches[i];
                    const nextMatch = matches[i + 1];

                    if (!currentMatch || !nextMatch) continue;

                    const currentEnd = currentMatch.index! + currentMatch[0].length;
                    const nextStart = nextMatch.index!;
                    const textBetween = formula.substring(currentEnd, nextStart).trim();

                    // 检查函数之间是否有有效的运算符
                    const validOperators = [
                        '+',
                        '-',
                        '*',
                        '/',
                        '>',
                        '<',
                        '>=',
                        '<=',
                        '=',
                        '!=',
                        'and',
                        'or'
                    ];
                    const hasOperator = validOperators.some((op) => textBetween.includes(op));

                    if (!hasOperator) {
                        throw new Error(
                            t('components:formulaEditor.messages.functionAnd') +
                                ' ' +
                                `${currentMatch[0]}` +
                                t('components:formulaEditor.messages.and') +
                                `${nextMatch[0]}` +
                                t('components:formulaEditor.validation.missingOperator') // Missing operator between functions
                        );
                    }
                }

                // 使用默认模拟数据进行语法验证
                math.evaluate(formula, DEFAULT_SIMULATION_DATA);

                setIsValid(true);
                setErrorMessage('');
                onValidate?.(true, '');

                toast({
                    title: t('components:formulaEditor.validation.success'),
                    status: 'success',
                    duration: 3000,
                    position: 'top',
                    isClosable: true
                });
            } catch (error: unknown) {
                let errorMsg =
                    t('components:formulaEditor.validation.syntaxError') +
                    ` ${error instanceof Error ? error.message : t('components:formulaEditor.validation.unknownError')}`; // Syntax error

                // 为常见的数组函数错误提供更友好的提示
                if (error instanceof Error && error.message.includes('Scalar values expected')) {
                    const formula = internalValue.trim();
                    if (
                        formula.includes('max(') ||
                        formula.includes('min(') ||
                        formula.includes('mean(') ||
                        formula.includes('sum(')
                    ) {
                        errorMsg = t('components:formulaEditor.validation.functionUsageTips'); // Statistical function usage tips
                    }
                }

                setIsValid(false);
                setErrorMessage(errorMsg);
                onValidate?.(false, errorMsg);
                toast({
                    title: t('components:formulaEditor.validation.error'),
                    description: errorMsg,
                    status: 'error',
                    duration: 5000,
                    position: 'top',
                    isClosable: true
                });
            }
        }, [internalValue, onValidate, toast]);

        // 测试公式
        const testFormula = useCallback(() => {
            const currentValue = internalValue || '';
            if (!currentValue.trim()) {
                toast({
                    title: t('components:formulaEditor.validation.enterFormula'),
                    status: 'warning',
                    position: 'top',
                    isClosable: true
                });
                return;
            }
            try {
                // 合并默认模拟数据和用户提供的测试数据
                const simulationData = { ...DEFAULT_SIMULATION_DATA, ...testData };

                // 直接使用mathjs evaluate方法，传入scope
                const result = math.evaluate(currentValue, simulationData);

                // 处理不同类型的结果
                let finalResult: number;
                if (typeof result === 'number') {
                    finalResult = result;
                } else if (Array.isArray(result)) {
                    // 如果结果是数组，取第一个数字元素，或者计算数字元素的平均值
                    const firstElement = result.length > 0 ? result[0] : 0;
                    if (typeof firstElement === 'number') {
                        finalResult = firstElement;
                    } else if (typeof firstElement === 'boolean') {
                        finalResult = firstElement ? 1 : 0;
                    } else {
                        finalResult = 0;
                    }
                } else if (typeof result === 'boolean') {
                    finalResult = result ? 1 : 0;
                } else {
                    finalResult = 0;
                }

                setTestResult(finalResult);

                // 显示详细的测试信息
                const usedVariables = Object.keys(simulationData).filter((key) =>
                    currentValue.includes(key)
                );
                const variableInfo = usedVariables
                    .map((key) => {
                        const value = simulationData[key];
                        if (Array.isArray(value)) {
                            return `${key}=[${value.join(', ')}]`;
                        } else {
                            return `${key}=${value}`;
                        }
                    })
                    .join(', '); // Variable information

                // 格式化结果显示
                let resultDisplay = '';
                if (typeof result === 'number') {
                    resultDisplay =
                        t('components:formulaEditor.messages.calculationResult') +
                        `${result.toFixed(4)}`; // Calculation result
                } else if (Array.isArray(result)) {
                    if (result.length <= 3) {
                        resultDisplay =
                            t('components:formulaEditor.messages.calculationResult') +
                            ` [${result
                                .map((v) => {
                                    if (typeof v === 'number') {
                                        return v.toFixed(4);
                                    } else {
                                        return String(v);
                                    }
                                })
                                .join(', ')}]`; // Calculation result
                    } else {
                        resultDisplay =
                            t('components:formulaEditor.messages.calculationResult') +
                            ` [${result
                                .slice(0, 3)
                                .map((v) => {
                                    if (typeof v === 'number') {
                                        return v.toFixed(4);
                                    } else {
                                        return String(v);
                                    }
                                })
                                .join(', ')}, ...]`; // Calculation result
                    }
                } else {
                    resultDisplay =
                        t('components:formulaEditor.messages.calculationResult') + `${result}`; // Calculation result
                }

                toast({
                    title: t('components:formulaEditor.validation.testSuccess'),
                    description:
                        t('components:formulaEditor.messages.simulationData') +
                        ` ${variableInfo}\n` +
                        t('components:formulaEditor.messages.formula') +
                        ` ${currentValue}\n${resultDisplay}`, // Simulation data, Formula, Result
                    status: 'success',
                    duration: 5000,
                    position: 'top',
                    isClosable: true
                });
            } catch (error: any) {
                toast({
                    title: t('components:formulaEditor.validation.testFailed'),
                    status: 'error',
                    description:
                        error?.message || t('components:formulaEditor.validation.unknownError'), // Unknown error
                    position: 'top',
                    isClosable: true
                });
                setTestResult(null);
            }
        }, [internalValue, testData, toast]);

        return (
            <VStack align="stretch" spacing={4}>
                <FormControl isInvalid={!isValid} isRequired={isRequired}>
                    <Box
                        position="relative"
                        border="1px solid"
                        borderColor={isValid ? 'gray.300' : 'red.500'}
                        borderRadius="md"
                        overflow="hidden"
                    >
                        <CodeMirror
                            value={internalValue}
                            height={height}
                            theme={customHighlight}
                            extensions={[
                                javascript(),
                                syntaxHighlighting(formulaHighlight),
                                EditorView.lineWrapping,
                                EditorView.updateListener.of((v) => {
                                    if (v.docChanged) {
                                        handleChange(v.state.doc.toString());
                                    }
                                    // 更新光标位置显示
                                    if (v.selectionSet) {
                                        setCursorPosition(v.state.selection.main.head);
                                    }
                                })
                            ]}
                            basicSetup={{
                                lineNumbers: false,
                                highlightActiveLine: false,
                                highlightActiveLineGutter: false,
                                foldGutter: false,
                                autocompletion: false,
                                closeBrackets: false,
                                bracketMatching: true
                            }}
                            editable={!readOnly}
                            ref={editorRef}
                            style={{
                                fontFamily: 'monospace',
                                fontSize: 14,
                                background: '#f7fafc',
                                border: 'none',
                                color: '#222',
                                cursor: 'text',
                                opacity: readOnly ? 0.85 : 1,
                                width: '100%',
                                textAlign: 'left'
                            }}
                        />
                        {showCursorPosition && (
                            <Box
                                position="absolute"
                                bottom={0}
                                right={0}
                                px={2}
                                py={0.5}
                                fontSize="xs"
                                color="gray.500"
                                bg="gray.50"
                                borderTop="1px solid"
                                borderLeft="1px solid"
                                borderColor="gray.200"
                                borderTopLeftRadius="md"
                            >
                                {/* 位置: */}
                                {t('components:formulaEditor.cursorPosition')}: {cursorPosition}
                            </Box>
                        )}
                        {testResult !== null && !readOnly && (
                            <Badge
                                position="absolute"
                                right={0}
                                bottom={0}
                                colorScheme="green"
                                variant="solid"
                                zIndex={10}
                                cursor="pointer"
                                onClick={() => setTestResult(null)}
                                title={t('components:formulaEditor.closeResult')}
                                display="flex"
                                alignItems="center"
                                gap={1}
                            >
                                {/* 测试结果: */}
                                {t('components:formulaEditor.testResult')}:{' '}
                                {typeof testResult === 'number'
                                    ? testResult.toFixed(4)
                                    : String(testResult)}
                                <Text as="span" ml={1} opacity={0.8} fontSize="xs">
                                    ✕
                                </Text>
                            </Badge>
                        )}
                    </Box>
                    {showValidation && (
                        <HStack mt={2} fontSize="sm" spacing={2}>
                            <HStack color={isValid ? 'green.500' : 'red.500'}>
                                {isValid ? <CheckIcon /> : <WarningIcon />}
                                <Text>
                                    {isValid
                                        ? t('components:formulaEditor.validation.correct')
                                        : errorMessage}
                                </Text>
                            </HStack>
                        </HStack>
                    )}
                </FormControl>

                {!readOnly && (
                    <VStack spacing={4} align="stretch">
                        <HStack spacing={2} wrap="wrap">
                            <Button
                                size="sm"
                                colorScheme="green"
                                variant="solid"
                                onClick={handleValidate}
                                leftIcon={<CheckIcon />}
                            >
                                {/* 校验公式 */}
                                {t('components:formulaEditor.buttons.validate')}
                            </Button>
                            <Button
                                size="sm"
                                colorScheme="blue"
                                variant="outline"
                                onClick={testFormula}
                                isDisabled={!internalValue?.trim()}
                            >
                                {/* 测试公式 */}
                                {t('components:formulaEditor.buttons.test')}
                            </Button>
                        </HStack>

                        <VStack spacing={3}>
                            {/* 函数和运算符面板 */}
                            <Box
                                border="1px solid"
                                borderColor="gray.200"
                                borderRadius="md"
                                p={3}
                                bg="white"
                                w="100%"
                            >
                                <Text fontWeight="bold" mb={1}>
                                    {/* 函数和运算符 */}
                                    {t('components:formulaEditor.panels.functions')}
                                </Text>
                                <VStack spacing={3} align="stretch">
                                    <SimpleGrid columns={8} spacing={2}>
                                        {PREDEFINED_FUNCTIONS.map((func) => (
                                            <Tooltip key={func.name} placement="top">
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    color="#9933cc"
                                                    _hover={{
                                                        bg: '#9933cc20',
                                                        borderColor: '#9933cc'
                                                    }}
                                                    onMouseDown={(e) => {
                                                        e.preventDefault();
                                                        insertAtCursor(func.name + '()');
                                                    }}
                                                    h="9"
                                                    p={1}
                                                    fontSize="md"
                                                >
                                                    {func.name}
                                                </Button>
                                            </Tooltip>
                                        ))}

                                        {['(', ')'].map((item) => (
                                            <Button
                                                key={item}
                                                size="sm"
                                                variant="outline"
                                                onMouseDown={(e) => {
                                                    e.preventDefault();
                                                    insertAtCursor(item);
                                                }}
                                                h="9"
                                                minW="7"
                                                p={1}
                                                fontSize="md"
                                            >
                                                {item}
                                            </Button>
                                        ))}
                                        {/* 退格按钮 */}
                                        <IconButton
                                            gridColumn={'span 2'}
                                            mx={3}
                                            p={0}
                                            aria-label="Backspace"
                                            icon={<ArrowBackIcon />}
                                            size="lg"
                                            colorScheme="red"
                                            variant="solid"
                                            onMouseDown={handleBackspace}
                                            h="9"
                                            minW="7"
                                        />
                                    </SimpleGrid>
                                    <SimpleGrid columns={8} spacing={2}>
                                        {OPERATORS.map((operator) => (
                                            <Tooltip
                                                key={operator.symbol}
                                                // label={`${operator.description} (${operator.category})`}
                                                placement="top"
                                            >
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    onMouseDown={(e) => {
                                                        e.preventDefault();
                                                        insertAtCursor(operator.value);
                                                    }}
                                                    h="9"
                                                    minW="7"
                                                    p={1}
                                                    fontSize="md"
                                                >
                                                    {operator.symbol}
                                                </Button>
                                            </Tooltip>
                                        ))}
                                    </SimpleGrid>
                                </VStack>
                            </Box>

                            {/* 变量面板 */}
                            <Box
                                border="1px solid"
                                borderColor="gray.200"
                                borderRadius="md"
                                p={3}
                                bg="white"
                                w="100%"
                            >
                                <Text fontWeight="bold" mb={2}>
                                    {/* 变量 */}
                                    {t('components:formulaEditor.panels.variables')}
                                </Text>

                                <VStack spacing={2} align="stretch">
                                    {/* BLANK 变量独占一行 */}
                                    <SimpleGrid columns={4} spacing={1}>
                                        {PREDEFINED_SAMPLE_TYPES.filter(
                                            (v) => v.name === 'BLANK' || v.name === 'SAMPLE'
                                        ).map((item) => (
                                            <Button
                                                key={item.name}
                                                size="sm"
                                                variant="outline"
                                                color="#0066cc"
                                                borderColor="#0066cc"
                                                _hover={{
                                                    bg: '#0066cc20',
                                                    borderColor: '#0066cc'
                                                }}
                                                onMouseDown={(e) => {
                                                    e.preventDefault();
                                                    insertAtCursor(item.name);
                                                }}
                                                h="9"
                                                p={1}
                                                fontSize="sm"
                                                fontWeight="bold"
                                            >
                                                {item.name}
                                            </Button>
                                        ))}
                                    </SimpleGrid>

                                    {/* 其他变量 */}
                                    <SimpleGrid columns={4} spacing={2}>
                                        {PREDEFINED_SAMPLE_TYPES.filter(
                                            (v) =>
                                                v.type !== 'none' &&
                                                v.type !== 'sample' &&
                                                v.type !== 'blank' &&
                                                v.type !== 'std'
                                        ).map((item) => (
                                            <Button
                                                key={item.name}
                                                size="sm"
                                                variant="outline"
                                                color="#0066cc"
                                                borderColor="#0066cc"
                                                _hover={{
                                                    bg: '#0066cc20',
                                                    borderColor: '#0066cc'
                                                }}
                                                onMouseDown={(e) => {
                                                    e.preventDefault();
                                                    insertAtCursor(item.name);
                                                }}
                                                h="9"
                                                p={1}
                                                fontSize="sm"
                                                fontWeight="bold"
                                            >
                                                {item.name}
                                            </Button>
                                        ))}
                                    </SimpleGrid>
                                </VStack>
                            </Box>
                        </VStack>
                    </VStack>
                )}
            </VStack>
        );
    }
);

FormulaEditor.displayName = 'FormulaEditor';
