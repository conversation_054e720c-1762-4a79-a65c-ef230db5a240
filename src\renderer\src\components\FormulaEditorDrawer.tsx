/* eslint-disable react/prop-types */
import React, { useCallback } from 'react';
import {
    Drawer,
    DrawerOverlay,
    Drawer<PERSON>ontent,
    DrawerHeader,
    DrawerBody,
    DrawerFooter,
    Button,
    Box,
    useColorModeValue
} from '@chakra-ui/react';
import { FormulaEditor } from './FormulaEditor';
import { useTranslation } from 'react-i18next';

interface Props {
    isOpen: boolean;
    onClose: () => void;
    value: string;
    onChange: (val: string) => void;
    onSave: () => void;
}

const FormulaEditorDrawer = React.memo<Props>(({ isOpen, onClose, value, onChange, onSave }) => {
    // 将 useColorModeValue 移到顶层，不能在 useMemo 中调用 hooks
    const bg = useColorModeValue('white', 'gray.800');
    const { t } = useTranslation(['common', 'components']);

    // 使用 useCallback 优化事件处理函数
    const handleClose = useCallback(() => {
        onClose();
    }, [onClose]);

    const handleSave = useCallback(() => {
        onSave();
    }, [onSave]);

    const handleChange = useCallback(
        (val: string) => {
            onChange(val);
        },
        [onChange]
    );

    return (
        <Drawer
            isOpen={isOpen}
            placement="right"
            onClose={handleClose}
            size="md"
            closeOnEsc={false}
            closeOnOverlayClick={false}
        >
            <DrawerOverlay />
            <DrawerContent bg={bg}>
                <DrawerHeader borderBottomWidth="1px">
                    {t('components:formulaEditor.title')}
                </DrawerHeader>
                <DrawerBody>
                    <Box py={2}>
                        <FormulaEditor
                            initialValue={value}
                            onChange={handleChange}
                            height="100px"
                            showCursorPosition={true}
                        />
                    </Box>
                </DrawerBody>
                <DrawerFooter>
                    <Button variant="outline" mr={3} onClick={handleClose}>
                        {t('common:button.cancel')}
                    </Button>
                    <Button colorScheme="blue" onClick={handleSave}>
                        {t('common:button.save')}
                    </Button>
                </DrawerFooter>
            </DrawerContent>
        </Drawer>
    );
});

FormulaEditorDrawer.displayName = 'FormulaEditorDrawer';

export default FormulaEditorDrawer;
