/* eslint-disable react/prop-types */
import React, { useState, useCallback } from 'react';
import { Button, HStack, useDisclosure, Box, Flex, IconButton } from '@chakra-ui/react';
import { EditIcon } from '@chakra-ui/icons';
import FormulaEditorDrawer from './FormulaEditorDrawer';
import { FormulaEditor } from './FormulaEditor';

interface Props {
    value: string;
    onChange: (val: string) => void;
    singleLine?: boolean;
    showEditButton?: boolean;
}

const FormulaInputField = React.memo<Props>(
    ({ value, onChange, singleLine = false, showEditButton = true }) => {
        const { isOpen, onOpen, onClose } = useDisclosure();
        const [draft, setDraft] = useState(value);

        // 打开编辑时，初始化草稿
        const handleEdit = useCallback(() => {
            setDraft(value);
            onOpen();
        }, [value, onOpen]);

        // 保存
        const handleSave = useCallback(() => {
            onChange(draft);
            onClose();
        }, [draft, onChange, onClose]);

        const handleDraftChange = useCallback((val: string) => {
            setDraft(val);
        }, []);

        if (singleLine) {
            return (
                <>
                    <Flex align="center" w="100%">
                        <Box flex="1" px={1}>
                            <FormulaEditor
                                initialValue={value}
                                onChange={() => {}}
                                height="32px"
                                readOnly
                                showCursorPosition={false}
                                showValidation={false}
                            />
                        </Box>
                        {showEditButton && (
                            <IconButton
                                colorScheme="blue"
                                icon={<EditIcon />}
                                flexShrink={0}
                                aria-label="Edit"
                                size="md"
                                onClick={handleEdit}
                            />
                        )}
                    </Flex>
                    <FormulaEditorDrawer
                        isOpen={isOpen}
                        onClose={onClose}
                        value={draft}
                        onChange={handleDraftChange}
                        onSave={handleSave}
                    />
                </>
            );
        }

        return (
            <>
                <HStack w="100%" align="center">
                    <Box flexGrow={1} minW="80px" maxW="100%">
                        <FormulaEditor
                            initialValue={value}
                            onChange={() => {}}
                            height="32px"
                            readOnly
                            showCursorPosition={false}
                            showValidation={false}
                        />
                    </Box>
                    {showEditButton && (
                        <Button
                            leftIcon={<EditIcon />}
                            onClick={handleEdit}
                            colorScheme="blue"
                            ml={2}
                            flexShrink={0}
                        >
                            {/* 编辑 */}
                            {'common:button.edit'}
                        </Button>
                    )}
                </HStack>
                <FormulaEditorDrawer
                    isOpen={isOpen}
                    onClose={onClose}
                    value={draft}
                    onChange={handleDraftChange}
                    onSave={handleSave}
                />
            </>
        );
    }
);

FormulaInputField.displayName = 'FormulaInputField';

export default FormulaInputField;
