// IconGallery.tsx
import React from 'react';
import { SimpleGrid, Box, Text, VStack, useClipboard, useToast } from '@chakra-ui/react';
import * as ChakraIcons from '@chakra-ui/icons';

const IconGallery: React.FC = () => {
    const toast = useToast();
    const { onCopy } = useClipboard('');

    // 将所有图标转换为数组
    const icons = Object.entries(ChakraIcons).filter(([name]) => name.endsWith('Icon'));

    const handleIconClick = (iconName: string) => {
        onCopy();
        const importStatement = `import { ${iconName} } from '@chakra-ui/icons'`;
        navigator.clipboard.writeText(importStatement);
        toast({
            title: '已复制导入语句',
            description: importStatement,
            status: 'success',
            duration: 2000,
            isClosable: true
        });
    };

    return (
        <VStack spacing={4} w="full" p={4}>
            <Text fontSize="2xl" fontWeight="bold">
                Chakra UI 图标预览
            </Text>

            <SimpleGrid columns={{ base: 2, sm: 3, md: 4, lg: 6 }} spacing={4} w="full">
                {icons.map(([name, Icon]) => (
                    <Box
                        key={name}
                        p={4}
                        borderWidth="1px"
                        borderRadius="md"
                        cursor="pointer"
                        onClick={() => handleIconClick(name)}
                        _hover={{
                            bg: 'gray.50',
                            shadow: 'md'
                        }}
                        transition="all 0.2s"
                    >
                        <VStack spacing={2}>
                            <Icon boxSize={6} />
                            <Text fontSize="sm" textAlign="center">
                                {name.replace('Icon', '')}
                            </Text>
                        </VStack>
                    </Box>
                ))}
            </SimpleGrid>
        </VStack>
    );
};

export default IconGallery;

// // 使用搜索功能的增强版本
// interface IconGalleryWithSearchProps {
//     isOpen: boolean;
//     onClose: () => void;
// }

// export const IconGalleryWithSearch: React.FC<IconGalleryWithSearchProps> = ({
//     isOpen,
//     onClose
// }) => {
//     const [searchTerm, setSearchTerm] = React.useState('');

//     return (
//         <Modal isOpen={isOpen} onClose={onClose} size="full">
//             <ModalOverlay />
//             <ModalContent>
//                 <ModalHeader>Chakra UI 图标库</ModalHeader>
//                 <ModalCloseButton />

//                 <ModalBody>
//                     <VStack spacing={4} w="full">
//                         <Input
//                             placeholder="搜索图标..."
//                             value={searchTerm}
//                             onChange={(e) => setSearchTerm(e.target.value)}
//                             size="lg"
//                         />

//                         <IconGallery filter={searchTerm} />
//                     </VStack>
//                 </ModalBody>
//             </ModalContent>
//         </Modal>
//     );
// };
