import React from 'react';
import { Box, VStack, Image, Text, Button, BoxProps } from '@chakra-ui/react';

export interface InfoCardProps extends BoxProps {
    iconsrc: string;
    title: string;
    description: string;
    buttontext?: string;
    onClick: () => void;
}

const InfoCard: React.FC<InfoCardProps> = ({ iconsrc, title, description, buttontext, onClick, ...boxProps }) => {
    return (
        <Box p={6} borderWidth="1px" borderRadius="xl" display="flex" alignItems="center" {...boxProps}>
            <VStack spacing={0} align="center" justify="space-between" h="100%" w="100%">
                <VStack spacing={6} flex="1" align="center" justify="center" pt={4}>
                    <Image src={iconsrc} alt={title} boxSize="64px" objectFit="contain" />
                    <Text fontSize="xl" fontWeight="bold" fontFamily="MiSans-Heavy" textAlign="center" color="gray.700">
                        {title}
                    </Text>
                    <Text
                        fontSize="md"
                        color="gray.600"
                        textAlign="center"
                        fontFamily="MiSans-Normal"
                        maxW="280px"
                        lineHeight="1.6"
                    >
                        {description}
                    </Text>
                </VStack>
                <Box pt={8} pb={2} w="60%">
                    <Button
                        bgGradient="linear(to-r, blue.400, blue.500)"
                        color="white"
                        size="md"
                        onClick={onClick}
                        fontFamily="MiSans-Medium"
                        px={8}
                        py={5}
                        borderRadius="md"
                        w="100%"
                        _hover={{
                            bgGradient: 'linear(to-r, blue.500, blue.600)',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 4px 12px rgba(66, 153, 225, 0.3)'
                        }}
                        _active={{
                            bgGradient: 'linear(to-r, blue.600, blue.700)',
                            transform: 'translateY(0)',
                            boxShadow: '0 2px 6px rgba(66, 153, 225, 0.2)'
                        }}
                        transition="all 0.2s cubic-bezier(0.4, 0, 0.2, 1)"
                        position="relative"
                        _before={{
                            content: '""',
                            position: 'absolute',
                            inset: '-1px',
                            padding: '1px',
                            borderRadius: 'md',
                            background: 'linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1))',
                            WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                            WebkitMaskComposite: 'xor',
                            maskComposite: 'exclude',
                            opacity: 0.5
                        }}
                    >
                        {buttontext}
                    </Button>
                </Box>
            </VStack>
        </Box>
    );
};

export default InfoCard;
