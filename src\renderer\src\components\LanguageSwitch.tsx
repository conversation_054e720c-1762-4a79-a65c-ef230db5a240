import React, { useState } from 'react';
import { Select, useToast } from '@chakra-ui/react';
import { languageManager, type Language } from '../i18n';
import { useTranslation } from 'react-i18next';
import logger from '../utils/logger';

const LanguageSwitch: React.FC = () => {
    const [isChanging, setIsChanging] = useState(false);
    const toast = useToast();
    const currentLan = languageManager.getCurrentLanguage();
    const languages = languageManager.getSupportedLanguages();
    const { t } = useTranslation(['components']);

    const handleLanguageChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
        const newLang = e.target.value as Language;
        setIsChanging(true);
        try {
            await languageManager.changeLanguage(newLang);
            toast({
                title: t('components:languageSwitch.toast.title'),
                description: t('components:languageSwitch.toast.success.description'),
                status: 'success',
                duration: 2000,
                isClosable: true,
                variant: 'solid',
                containerStyle: {
                    // backgroundColor: 'gray.50',
                    bg: 'teal.500'
                },
                position: 'top'
            });
        } catch (error) {
            logger.error('Failed to change language:', error, {
                component: './src/renderer/src/components/LanguageSwitch.tsx'
            });

            toast({
                title: t('components:languageSwitch.toast.title'),
                description: t('components:languageSwitch.toast.error.description'),
                status: 'error',
                duration: 3000,
                isClosable: true,
                variant: 'solid',
                containerStyle: {
                    // backgroundColor: 'orange.50'
                    bg: 'orange.500'
                },
                position: 'top'
            });
        } finally {
            setIsChanging(false);
        }
    };

    return (
        <Select
            value={currentLan}
            onChange={handleLanguageChange}
            width="120px"
            size="lg"
            fontFamily="MiSans-Normal"
            // fontSize="md"
            isDisabled={isChanging}
            bg="white"
            borderWidth={0}
            _hover={{
                bg: 'gray.50',
                color: 'blue.600',
                cursor: 'pointer'
            }}
        >
            {Object.entries(languages).map(([langCode, lanName]) => (
                <option key={langCode} value={langCode}>
                    {lanName}
                </option>
            ))}
        </Select>
    );
};

export default LanguageSwitch;
