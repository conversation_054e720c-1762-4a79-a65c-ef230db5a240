import React, { useMemo } from 'react';
import { Box, Flex, Link } from '@chakra-ui/react';
import { useLocation, useNavigate } from 'react-router-dom';

interface NavbarProps {
    links: { label: string; href: string; onClick?: () => void }[]; // 定义链接的类型
}

const Navbar: React.FC<NavbarProps> = ({ links }) => {
    const location = useLocation();
    const navigate = useNavigate();
    const spacing = '30px';
    const fontSize = '18px';

    const navbarStyle = useMemo(
        () => ({
            backfaceVisibility: 'hidden' as const,
            WebkitBackfaceVisibility: 'hidden' as const,
            transform: 'translateZ(0)',
            WebkitTransform: 'translateZ(0)',
            willChange: 'transform',
            perspective: 1000,
            WebkitPerspective: 1000
        }),
        []
    );

    const handleNavigation = (href: string, onClick?: () => void) => (e: React.MouseEvent) => {
        e.preventDefault();
        if (location.pathname !== href) {
            navigate(href);
            onClick?.();
        }
    };

    return (
        <Box bg="teal.500" p={4} position="fixed" top={0} width="100%" zIndex={1000} style={navbarStyle} isolation="isolate">
            <Flex align="center" justify="flex-start" style={{ willChange: 'transform' }}>
                {links.map((link, index) => (
                    <Link
                        key={`nav-link-${link.label}-${index}`}
                        as="a"
                        href={link.href}
                        color="white"
                        mx={2}
                        style={{
                            marginRight: index < links.length - 1 ? spacing : '0',
                            fontFamily: 'MiSans-Demibold',
                            fontSize: fontSize,
                            transition: 'all 0.3s ease',
                            willChange: 'transform',
                            transform: 'translateZ(0)',
                            opacity: location.pathname === link.href ? 0.8 : 1,
                            textDecoration: location.pathname === link.href ? 'underline' : 'none',
                            outline: 'none'
                        }}
                        onClick={handleNavigation(link.href, link.onClick)}
                    >
                        {link.label}
                    </Link>
                ))}
            </Flex>
        </Box>
    );
};

export default Navbar;
