import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON>,
    <PERSON>dal<PERSON>nt,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>dal<PERSON>ooter,
    ModalBody,
    ModalCloseButton,
    Button,
    FormControl,
    FormLabel,
    Select,
    VStack,
    HStack
} from '@chakra-ui/react';
import { Project } from '@shared/types';
import logger from '../utils/logger';

interface ProjectSelectModalProps {
    isOpen: boolean;
    onClose: () => void;
    rowOrCol: string;
    onSelect: (rowOrCol: string, projectId: string) => void;
}

const ProjectSelectModal: React.FC<ProjectSelectModalProps> = ({ isOpen, onClose, rowOrCol, onSelect }) => {
    const [projects, setProjects] = useState<Project[]>([]);
    const [selectedProjectId, setSelectedProjectId] = useState<string>('');

    useEffect(() => {
        const fetchProjects = async () => {
            try {
                const result = await window.customApi.configInfo.getProjectList();
                if (result.success && result.data) {
                    setProjects(result.data);
                }
            } catch (error) {
                logger.error('Failed to load project list:', error, {
                    component: './src/renderer/src/components/ProjectSelectModal.tsx'
                });
            }
        };

        if (isOpen) {
            fetchProjects();
        }
    }, [isOpen]);

    const handleSave = () => {
        if (selectedProjectId) {
            onSelect(rowOrCol, selectedProjectId);
        }
        onClose();
    };

    const handleCancel = () => {
        setSelectedProjectId('');
        onClose();
    };

    return (
        <Modal isOpen={isOpen} onClose={handleCancel} size="md">
            <ModalOverlay />
            <ModalContent>
                <ModalHeader>选择项目 - {rowOrCol}</ModalHeader>
                <ModalCloseButton />
                <ModalBody>
                    <VStack spacing={4}>
                        <FormControl>
                            <FormLabel>选择项目</FormLabel>
                            <Select
                                value={selectedProjectId}
                                onChange={(e) => setSelectedProjectId(e.target.value)}
                                placeholder="请选择项目"
                            >
                                {projects.map((project) => (
                                    <option key={project.id} value={project.id}>
                                        {project.name} ({project.code})
                                    </option>
                                ))}
                            </Select>
                        </FormControl>
                    </VStack>
                </ModalBody>

                <ModalFooter>
                    <HStack spacing={3}>
                        <Button variant="ghost" onClick={handleCancel}>
                            取消
                        </Button>
                        <Button colorScheme="blue" onClick={handleSave} isDisabled={!selectedProjectId}>
                            确定
                        </Button>
                    </HStack>
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
};

export default ProjectSelectModal;
