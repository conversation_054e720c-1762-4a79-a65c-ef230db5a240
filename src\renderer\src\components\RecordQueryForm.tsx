import React, { useState } from 'react';
import { Box, Flex, FormControl, FormLabel, Input, Button, HStack, VStack } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';

interface RecordQueryFormProps {
    onQuery: (params: { startDate: string; endDate: string; mtpNumber: string }) => void;
}

const RecordQueryForm: React.FC<RecordQueryFormProps> = ({ onQuery }) => {
    const { t } = useTranslation(['common', 'components']);
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [mtpNumber, setMtpNumber] = useState('');

    const handleQuery = () => {
        onQuery({
            startDate,
            endDate,
            mtpNumber
        });
    };

    const handleReset = () => {
        setStartDate('');
        setEndDate('');
        setMtpNumber('');
        // 统一使用 handleQuery 的空条件分支
        onQuery({
            startDate: '',
            endDate: '',
            mtpNumber: ''
        });
    };

    return (
        <Box p={2} bg="white" border="1px" borderColor="gray.200" borderRadius="md" mb={1}>
            {/* <Text fontSize="md" fontFamily="MiSans-Bold" mb={3}>
                查询条件
            </Text>
            <Divider mb={3} /> */}

            <VStack spacing={3} align="stretch">
                <HStack spacing={1}>
                    <FormControl>
                        <FormLabel fontSize="sm" fontFamily="MiSans-Normal" mb={1}>
                            {/* 起始日期 */}
                            {t('components:recordQueryForm.startDate')}
                        </FormLabel>
                        <Input p={0} type="date" value={startDate} onChange={(e) => setStartDate(e.target.value)} size="sm" fontFamily="MiSans-Normal" title={t('components:recordQueryForm.startDate_title')} />
                    </FormControl>

                    <FormControl>
                        <FormLabel fontSize="sm" fontFamily="MiSans-Normal" mb={1}>
                            {/* 终止日期 */}
                            {t('components:recordQueryForm.endDate')}
                        </FormLabel>
                        <Input p={0} type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} size="sm" fontFamily="MiSans-Normal" title={t('components:recordQueryForm.endDate_title')} />
                    </FormControl>
                </HStack>

                <FormControl>
                    <HStack spacing={1} justify="space-between">
                        <FormLabel
                            // flexWrap={'nowrap'}
                            fontSize="sm"
                            fontFamily="MiSans-Normal"
                            m={0}
                            p={0}
                            whiteSpace="nowrap"
                            overflow="hidden"
                            textOverflow="ellipsis"
                            minW="fit-content"
                        >
                            {/* 微板编号 */}
                            {t('components:recordQueryForm.mtpNumber')}
                        </FormLabel>
                        <Input
                            px={1}
                            // placeholder="微板编号（支持模糊查询）"
                            placeholder={t('components:recordQueryForm.mtpNumber_placeholder')}
                            value={mtpNumber}
                            onChange={(e) => setMtpNumber(e.target.value)}
                            size="sm"
                            fontFamily="MiSans-Normal"
                            // title="支持模糊查询：输入开始或结束的部分字符..."
                            title={t('components:recordQueryForm.mtpNumber_title')}
                        />

                        <Flex gap={1} justify="flex-end">
                            <Button size="sm" variant="ghost" colorScheme="blue" onClick={handleQuery} fontFamily="MiSans-Normal">
                                {/* 查询 */}
                                {t('common:button.query')}
                            </Button>
                            <Button size="sm" variant="ghost" colorScheme="blue" onClick={handleReset} fontFamily="MiSans-Normal">
                                {/* 重置 */}
                                {t('common:button.reset')}
                            </Button>
                        </Flex>
                    </HStack>
                </FormControl>
            </VStack>
        </Box>
    );
};

export default RecordQueryForm;
