import React, { useState, useCallback, useEffect } from 'react';
import { Box, Flex, Icon } from '@chakra-ui/react';
import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';

interface ResizableSplitPaneProps {
    left: React.ReactNode;
    right: React.ReactNode;
    defaultLeftWidth?: number;
    minLeftWidth?: number;
    maxLeftWidth?: number;
    splitterWidth?: number;
    showSplitterIcon?: boolean;
    onWidthChange?: (width: number) => void;
}

const ResizableSplitPane: React.FC<ResizableSplitPaneProps> = ({
    left,
    right,
    defaultLeftWidth = 400,
    minLeftWidth = 240,
    maxLeftWidth = 600,
    splitterWidth = 4,
    showSplitterIcon = false,
    onWidthChange
}) => {
    const [leftWidth, setLeftWidth] = useState(defaultLeftWidth);
    const [isDragging, setIsDragging] = useState(false);
    const [startX, setStartX] = useState(0);
    const [startWidth, setStartWidth] = useState(0);

    const handleMouseDown = (e: React.MouseEvent) => {
        e.preventDefault();
        setIsDragging(true);
        setStartX(e.clientX);
        setStartWidth(leftWidth);

        // 设置全局样式
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';
    };

    const handleMouseMove = useCallback(
        (e: MouseEvent) => {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const newWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, startWidth + deltaX));
            setLeftWidth(newWidth);

            // 回调通知父组件宽度变化
            onWidthChange?.(newWidth);
        },
        [isDragging, startX, startWidth, minLeftWidth, maxLeftWidth, onWidthChange]
    );

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);

        // 恢复全局样式
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
    }, []);

    // 监听鼠标移动和释放事件
    useEffect(() => {
        if (isDragging) {
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            return () => {
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            };
        }
        return undefined;
    }, [isDragging, handleMouseMove, handleMouseUp]);

    // 组件卸载时清理全局样式
    useEffect(() => {
        return () => {
            document.body.style.cursor = '';
            document.body.style.userSelect = '';
        };
    }, []);

    return (
        <Flex h="100%" position="relative">
            {/* 左侧面板 */}
            <Box
                w={`${leftWidth}px`}
                minW={`${minLeftWidth}px`}
                maxW={`${maxLeftWidth}px`}
                overflow="hidden"
                flexShrink={0}
            >
                {left}
            </Box>

            {/* 拖拽分割线 */}
            <Box
                w={`${splitterWidth}px`}
                bg={isDragging ? 'blue.500' : 'blue.100'}
                cursor="col-resize"
                position="relative"
                flexShrink={0}
                _hover={{
                    bg: isDragging ? 'blue.500' : 'blue.100'
                    // transition: 'background-color 0.1s'
                }}
                onMouseDown={handleMouseDown}
                _before={{
                    content: '""',
                    position: 'absolute',
                    left: '-2px',
                    right: '-2px',
                    top: 0,
                    bottom: 0,
                    cursor: 'col-resize',
                    zIndex: 1
                }}
                transition="background-color 0.1s"
                borderLeft="1px solid"
                borderRight="1px solid"
                borderColor={isDragging ? 'blue.500' : 'blue.100'}
            >
                {showSplitterIcon && (
                    <Flex
                        position="absolute"
                        top="50%"
                        left="50%"
                        transform="translate(-50%, -50%)"
                        direction="column"
                        align="center"
                        gap={0}
                        pointerEvents="none"
                        zIndex={2}
                    >
                        <Icon
                            as={ChevronLeftIcon}
                            boxSize={4}
                            color={isDragging ? 'red.500' : 'blue.500'}
                            fontWeight="bold"
                            filter="drop-shadow(0 1px 2px rgba(0,0,0,0.1))"
                        />
                        <Icon
                            as={ChevronRightIcon}
                            boxSize={4}
                            color={isDragging ? 'red.500' : 'blue.500'}
                            fontWeight="bold"
                            filter="drop-shadow(0 1px 2px rgba(0,0,0,0.1))"
                        />
                    </Flex>
                )}
            </Box>

            {/* 右侧面板 */}
            <Box flex={1} overflow="hidden" minW={0}>
                {right}
            </Box>
        </Flex>
    );
};

export default ResizableSplitPane;
