import React from 'react';
import { Box, Table, Tbody, Tr, Td } from '@chakra-ui/react';

export interface DetectionInfo {
    project: string;
    formula: string;
    cutoff: string;
    blank: string;
    positiveControl: string;
    negativeControl: string;
    mainWavelength: string;
    referenceWavelength: string;
    batchNumber: string;
}

export const ShowDetectionInfoForm: React.FC<{ info: DetectionInfo }> = ({ info }) => (
    <Box mx={10} border="0px" borderColor="gray.200" borderRadius="md" p={0} bg="white" w="93%">
        <Table
            size="sm"
            sx={{
                borderSpacing: '0 8px',
                borderCollapse: 'separate'
            }}
        >
            <Tbody>
                <Tr>
                    <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'}>
                        项目名称
                    </Td>
                    <Td w={'20%'}>{info.project}</Td>
                    <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'}>
                        项目代号
                    </Td>
                    <Td w={'15%'}>s001</Td>
                    <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'}>
                        主波长
                    </Td>
                    <Td w={'15%'}>{info.mainWavelength}</Td>
                    <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'}>
                        参考波长
                    </Td>
                    <Td w={'15%'}>{info.referenceWavelength}</Td>
                </Tr>

                <Tr>
                    <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'}>
                        阳性公式
                    </Td>
                    <Td w={'20%'}>{info.formula}</Td>
                    <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'}>
                        Cutoff值
                    </Td>
                    <Td w={'15%'}>{info.cutoff}</Td>

                    <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'}>
                        阳性对照
                    </Td>
                    <Td w={'15%'}>{info.positiveControl}</Td>
                    <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'}>
                        阴性对照
                    </Td>
                    <Td w={'15%'}>{info.negativeControl}</Td>
                </Tr>
            </Tbody>
        </Table>
    </Box>
);

export default ShowDetectionInfoForm;
