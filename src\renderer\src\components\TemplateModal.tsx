import React, { useState } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON>lay,
    Modal<PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON>er,
    <PERSON>dal<PERSON>ooter,
    ModalBody,
    ModalCloseButton,
    Button,
    RadioGroup,
    Radio,
    Stack,
    Box,
    List,
    ListItem,
    Text,
    FormControl,
    FormLabel,
    HStack,
    Icon
} from '@chakra-ui/react';

import { SettingsIcon } from '@chakra-ui/icons';

interface TemplateModalProps {
    isOpen: boolean;
    onClose: () => void;
}

const TemplateModal: React.FC<TemplateModalProps> = ({ isOpen, onClose }) => {
    const winMaxWidth = '800px';
    const winMaxHeight = '600px';
    const [fillMethod, setFillMethod] = useState('horizontal');
    const [selectedPosition, setSelectedPosition] = useState('A1');

    const positions = Array.from({ length: 8 }, (_, row) => Array.from({ length: 12 }, (_, col) => `${String.fromCharCode(65 + row)}${col + 1}`)).flat();

    return (
        <Modal onClose={onClose} isOpen={isOpen} size="lg" isCentered>
            <ModalOverlay />
            <ModalContent maxW={winMaxWidth} maxH={winMaxHeight}>
                <ModalHeader bg="teal.500" color="white">
                    <HStack>
                        <Icon as={SettingsIcon} boxSize={6} />
                        <span>微孔板自动填充</span>
                    </HStack>
                </ModalHeader>
                <ModalCloseButton />
                <ModalBody>
                    <FormControl as="fieldset" borderWidth="1px" borderRadius="md" p={4} mb={4}>
                        <FormLabel as="legend">填充方式</FormLabel>
                        <RadioGroup value={fillMethod} onChange={setFillMethod}>
                            <Stack direction="row" spacing={4} justify="space-between">
                                <Radio value="horizontal">横向填充</Radio>
                                <Radio value="vertical">纵向填充</Radio>
                                <Radio value="reverse">逆向填充</Radio>
                                <Radio value="align">每一行开头对齐</Radio>
                            </Stack>
                        </RadioGroup>
                    </FormControl>
                    <Box display="flex" mt={4} h="300px">
                        <Box flex="1" mr={4}>
                            <FormControl as="fieldset" borderWidth="1px" borderRadius="md" p={4} h="100%">
                                <FormLabel as="legend">起始位置</FormLabel>
                                <Box borderWidth="1px" borderRadius="md" p={2} mb={2}>
                                    {selectedPosition}
                                </Box>
                                <List spacing={2} maxH="190px" overflowY="auto" borderWidth="1px" borderRadius="md" p={2}>
                                    {positions.map((position) => (
                                        <ListItem key={position} cursor="pointer" onClick={() => setSelectedPosition(position)} _hover={{ bg: 'gray.100' }}>
                                            {position}
                                        </ListItem>
                                    ))}
                                </List>
                            </FormControl>
                        </Box>
                        <Box flex="2">
                            <FormControl as="fieldset" borderWidth="1px" borderRadius="md" p={4} h="100%">
                                <FormLabel as="legend">使用描述</FormLabel>
                                <Text fontSize="sm">
                                    <p>
                                        1. 横向填充是按照从左到右、从上到下的顺序排列标本号。
                                        <br />
                                        2. 纵向填充是按照从下到上、从左到右的顺序排列标本号。
                                        <br />
                                        3. 若选中“每一行(或每一列)开头对齐”，则表示每一行(或每一列)的开头都从指定的第一行(或第一列)对应的位置开始，而不是重新从每一行的第一列(或每一列的第一行)开始排列。
                                        <br />
                                        4. 默认的起始位置是您在微孔板上最后选中的位置，若不改变则将从这个位置开始填充。若您没有在微孔板上选择任何位置，则从A01开始填充。
                                    </p>
                                </Text>
                            </FormControl>
                        </Box>
                    </Box>
                </ModalBody>
                <ModalFooter>
                    <Button colorScheme="blue" mr={3} onClick={onClose}>
                        确定
                    </Button>
                    <Button onClick={onClose}>取消</Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
};

export default TemplateModal;
