import React, { useState } from 'react';
import {
    Modal,
    ModalOverlay,
    ModalContent,
    ModalHeader,
    Modal<PERSON>ooter,
    ModalBody,
    ModalCloseButton,
    Button,
    FormControl,
    FormLabel,
    Input,
    HStack,
    Icon
} from '@chakra-ui/react';

import { SettingsIcon } from '@chakra-ui/icons';
import { useTranslation } from 'react-i18next';

interface TemplateNameModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (name: string) => void;
}

const TemplateNameModal: React.FC<TemplateNameModalProps> = ({ isOpen, onClose, onSubmit }) => {
    const [templateName, setTemplateName] = useState('');
    const { t } = useTranslation();
    const handleSubmit = () => {
        if (!templateName.trim()) {
            return; // 不提交空名称
        }
        onSubmit(templateName);
        onClose();
    };

    return (
        <Modal onClose={onClose} isOpen={isOpen} isCentered>
            <ModalOverlay />
            <ModalContent>
                <ModalHeader bg="teal.500" color="white">
                    <HStack>
                        <Icon as={SettingsIcon} boxSize={6} />
                        {/* <span>请输入模板名称</span> */}
                        <span> {t('pages:elisaControl.templateNameModal.title')}</span>
                    </HStack>
                </ModalHeader>
                <ModalCloseButton />
                <ModalBody>
                    <FormControl>
                        {/* <FormLabel>模板名称：</FormLabel> */}
                        <FormLabel>
                            {t('pages:elisaControl.templateNameModal.label_name')}
                        </FormLabel>
                        <Input
                            value={templateName}
                            onChange={(e) => setTemplateName(e.target.value)}
                            // placeholder="请输入模板名称"
                            placeholder={t('pages:elisaControl.templateNameModal.placeholder')}
                            // onKeyPress={(e) => {
                            //     if (e.key === 'Enter' && templateName.trim()) {
                            //         handleSubmit();
                            //     }
                            // }}
                        />
                    </FormControl>
                </ModalBody>
                <ModalFooter>
                    <Button
                        colorScheme="blue"
                        mr={3}
                        onClick={handleSubmit}
                        isDisabled={!templateName.trim()}
                    >
                        {/* 确定 */}
                        {t('common:button.confirm')}
                    </Button>
                    <Button onClick={onClose}>{t('common:button.cancel')}</Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
};

export default TemplateNameModal;
