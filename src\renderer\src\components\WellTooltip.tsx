import React from 'react';
import { Box, Text, VStack, H<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TooltipProps } from '@chakra-ui/react';
import { WellData } from '@shared/types/plateData';
import { useTranslation } from 'react-i18next';

interface WellTooltipProps {
    isPartial?: boolean;
    wellId: string;
    wellData: WellData;
    resultShowType: 'result' | 'original' | 'sco';
    children: React.ReactElement;
    placement?: TooltipProps['placement'];
}

const WellTooltip: React.FC<WellTooltipProps> = ({ isPartial = false, wellId, wellData, children, placement = 'top' }) => {
    const { t } = useTranslation(['common', 'pages']);
    const formatValue = (value: number | undefined) => {
        if (value === undefined || value === null) return t('common:label.undefined');
        return value.toFixed(4);
    };

    const getResultColor = (result: number | undefined) => {
        if (!result) return 'gray.500';
        if (result === 1) {
            return 'red.500';
        }
        if (result === 0) {
            return 'green.500';
        }
        return 'gray.500';
    };

    const tooltipContent = (
        <Box p={3} maxW="280px">
            <VStack spacing={2} align="stretch">
                {/* 孔位ID */}
                <HStack justify="space-between">
                    <Text fontSize="sm" fontWeight="bold" color="blue.600">
                        {/* 孔位ID: */}
                        {t('pages:resultData.wellTooltip.wellId')}
                    </Text>
                    <Text fontSize="sm" fontWeight="bold">
                        {wellId}
                    </Text>
                </HStack>

                <Divider />

                {/* 样本类型 */}
                <HStack justify="space-between">
                    <Text fontSize="sm" color="gray.600">
                        {/* 样本类型: */}
                        {t('pages:resultData.wellTooltip.sampleType')}
                    </Text>
                    <Text fontSize="sm" fontWeight="medium">
                        {wellData.sampleType.name || ''}
                    </Text>
                </HStack>

                {/* 样本编号 */}
                <HStack justify="space-between">
                    <Text fontSize="sm" color="gray.600">
                        {/* 样本编号: */}
                        {t('pages:resultData.wellTooltip.sampleNumber')}
                    </Text>
                    <Text fontSize="sm" fontWeight="medium">
                        {wellData.sampleNumber || ''}
                    </Text>
                </HStack>

                <Divider />

                {/* 根据显示类型显示不同的OD值 */}
                {/* {resultShowType === 'result' && ( <>*/}

                {!isPartial && (
                    <>
                        <HStack justify="space-between">
                            <Text fontSize="sm" color="gray.600">
                                OD:
                            </Text>
                            <Text fontSize="sm" fontWeight="medium" color="blue.600">
                                {formatValue(wellData.odValue)}
                            </Text>
                        </HStack>
                        <HStack justify="space-between">
                            <Text fontSize="sm" color="gray.600">
                                S/CO:
                            </Text>
                            <Text fontSize="sm" fontWeight="medium" color="orange.600">
                                {formatValue(wellData.odRatio)}
                            </Text>
                        </HStack>

                        {/*  </> )} */}

                        {/* {resultShowType === 'original' && (<> */}

                        <HStack justify="space-between">
                            <Text fontSize="sm" color="gray.600">
                                {/* OD主波长: */}
                                {t('pages:resultData.wellTooltip.odMain')}
                            </Text>
                            <Text fontSize="sm" fontWeight="medium" color="blue.600">
                                {formatValue(wellData.odMain)}
                            </Text>
                        </HStack>
                        <HStack justify="space-between">
                            <Text fontSize="sm" color="gray.600">
                                {/* OD参考波长: */}
                                {t('pages:resultData.wellTooltip.odRef')}
                            </Text>
                            <Text fontSize="sm" fontWeight="medium" color="purple.600">
                                {formatValue(wellData.odRef)}
                            </Text>
                        </HStack>

                        {/*   </> )} */}

                        {/* 浓度值 */}
                        {wellData.concentration !== undefined && (
                            <>
                                <HStack justify="space-between">
                                    <Text fontSize="sm" color="gray.600">
                                        {/* 浓度值: */}
                                        {t('pages:resultData.wellTooltip.concentration')}
                                    </Text>
                                    <Text fontSize="sm" fontWeight="medium" color="teal.600">
                                        {formatValue(wellData.concentration)}
                                    </Text>
                                </HStack>
                            </>
                        )}

                        <Divider />

                        {/* 检测结果 */}
                        <HStack justify="space-between">
                            <Text fontSize="sm" color="gray.600">
                                {/* 检测结果: */}
                                {t('pages:resultData.wellTooltip.result')}
                            </Text>
                            <Text fontSize="sm" fontWeight="bold" color={getResultColor(wellData.result)}>
                                {wellData.sampleType.type !== 'none'
                                    ? wellData.result === 2
                                        ? t('common:label.grayZone') // 灰区
                                        : wellData.result === 1
                                          ? t('common:label.positive') // 阳性
                                          : t('common:label.negative') // 阴性
                                    : ''}
                            </Text>
                        </HStack>
                    </>
                )}
            </VStack>
        </Box>
    );

    return (
        <Tooltip
            label={tooltipContent}
            placement={placement}
            // placement="top-start"
            hasArrow
            bg="white"
            color="gray.800"
            border="2px solid"
            borderColor="gray.200"
            borderRadius="md"
            boxShadow="lg"
            maxW="300px"
            openDelay={300}
            closeDelay={100}
        >
            {children}
        </Tooltip>
    );
};

export default WellTooltip;
