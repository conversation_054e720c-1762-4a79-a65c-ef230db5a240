import React, { useEffect } from 'react';
import {
    <PERSON>S<PERSON>ck,
    FormControl,
    SimpleGrid,
    InputGroup,
    InputLeftAddon,
    Select,
    HStack,
    Text,
    NumberInput,
    NumberInputField,
    NumberInputStepper,
    NumberIncrementStepper,
    NumberDecrementStepper,
    Box,
    InputRightAddon,
    Spacer
} from '@chakra-ui/react';

// import { devices } from '@renderer/config/devices';
import {
    DeviceConfig,
    PortsList,
    SerialPortOptions,
    DataBits,
    StopBits,
    Parity,
    FlowControl
} from '@shared/types';
import { DeviceModel } from '@shared/commondefines';
import { useTranslation } from 'react-i18next';
import { styles } from '@renderer/utils/theme';

const BAUD_RATES: number[] = [1200, 2400, 4800, 9600, 14400, 19200, 38400, 57600, 115200] as const;
const FLOW_CONTROL:FlowControl[] = ['none' ,'hardware','software'] as const;
const DATA_BITS: DataBits[] = [5, 6, 7, 8] as const;
const STOP_BITS: StopBits[] = [1, 1.5, 2] as const;
const PARITY:Parity[] = ['none', 'odd', 'even','mark', 'space'] as const;

interface DeviceSelectorProps {
    defaultDevice: DeviceModel;
    devicesList: DeviceConfig[];
    portsList: PortsList[];
    serialConfig: SerialPortOptions;
    onDefaultDeviceChange: (model: DeviceModel) => Promise<void>;
    onSerialConfigChange: (info: Partial<SerialPortOptions>) => Promise<void>;
}

const DeviceSelector: React.FC<DeviceSelectorProps> = ({
    defaultDevice,
    devicesList,
    portsList,
    serialConfig,
    onDefaultDeviceChange,
    onSerialConfigChange
}) => {
    const { t } = useTranslation(['common']);
    const label_width = '50%';

    // 当只有一个串口选项且当前未设置串口时，自动设置
    useEffect(() => {
        // 如果只有一个串口选项
        if (portsList.length === 1) {
            const singlePort = portsList[0];
            // 如果当前没有设置串口，或者当前设置的串口不在列表中，则自动设置
            if (!serialConfig.path || !portsList.find((port) => port.path === serialConfig.path)) {
                onSerialConfigChange({ path: singlePort.path });
            }
        }
        // 如果当前设置的串口不在可用列表中，清空选择
        else if (serialConfig.path && !portsList.find((port) => port.path === serialConfig.path)) {
            onSerialConfigChange({ path: '' });
        }
    }, [portsList, serialConfig.path, onSerialConfigChange]);

    // 当只有一个设备选项且当前未设置设备时，自动设置
    useEffect(() => {
        if (devicesList.length === 1 && defaultDevice === DeviceModel.unknown) {
            onDefaultDeviceChange(devicesList[0].model as DeviceModel);
        }
    }, [devicesList, defaultDevice, onDefaultDeviceChange]);

    return (
        <>
            <VStack
                spacing={4}
                align="stretch"
                // divider={<StackDivider borderColor="gray.200" borderWidth="0.5px" />}
            >
                <HStack
                    mx={4}
                    my={4}
                    w="100%"
                    justifyContent={'space-between'}
                    alignItems={'center'}
                >
                    <InputGroup>
                        <InputLeftAddon w="40%" sx={styles.label_bold}>
                            {/* <Text>默认仪器</Text> */}
                            {t('pages:deviceManager.device.defaultDevice')}
                        </InputLeftAddon>
                        <Select
                            w="50%"
                            sx={styles.label_normal}
                            // fontFamily="MiSans-Bold"
                            // fontFamily="MiSans-Normal"
                            value={defaultDevice}
                            onChange={(e) => {
                                onDefaultDeviceChange(e.target.value as DeviceModel);
                            }}
                            // isDisabled={devicesList.length === 1}
                        >
                            {devicesList.map((device) => (
                                <option key={device.model} value={device.model}>
                                    {t(device.name)}
                                </option>
                            ))}
                        </Select>
                    </InputGroup>
                </HStack>

                <Box w="100%" my={4} p={0}>
                    <FormControl>
                        <SimpleGrid columns={2} spacing={6}>
                            {/* 串口号 */}
                            <InputGroup display="flex" alignItems="center">
                                <InputLeftAddon sx={styles.label_bold} w={label_width}>
                                    {/* 串口号 */}
                                    {t('pages:deviceManager.device.port.label')}
                                </InputLeftAddon>
                                <Select
                                    value={
                                        portsList.find((port) => port.path === serialConfig.path)
                                            ? serialConfig.path
                                            : ''
                                    }
                                    onChange={(e) => {
                                        onSerialConfigChange({ path: e.target.value as string });
                                    }}
                                    sx={styles.label_normal}
                                    // isDisabled={portsList.length === 1}
                                >
                                    {portsList.map((port) => (
                                        <option key={port.path} value={port.path}>
                                            {port.path}
                                        </option>
                                    ))}
                                </Select>
                            </InputGroup>

                            {/* 波特率 */}
                            <InputGroup display="flex" alignItems="center">
                                <InputLeftAddon sx={styles.label_bold} w={label_width}>
                                    {/* 波特率 */}
                                    {t('pages:deviceManager.device.baudRate.label')}
                                </InputLeftAddon>
                                <Select
                                    value={serialConfig.baudRate}
                                    onChange={(e) => {
                                        onSerialConfigChange({
                                            baudRate: parseInt(e.target.value) as number
                                        });
                                    }}
                                    sx={styles.label_normal}
                                >
                                    {BAUD_RATES.map((rate) => (
                                        <option key={rate} value={rate}>
                                            {rate}
                                        </option>
                                    ))}
                                </Select>
                            </InputGroup>

                            {/* 数据位 */}
                            <InputGroup display="flex" alignItems="center">
                                <InputLeftAddon sx={styles.label_bold} w={label_width}>
                                    {/* 数据位 */}
                                    {t('pages:deviceManager.device.dataBits.label')}
                                </InputLeftAddon>
                                <Select
                                    value={serialConfig.dataBits}
                                    onChange={(e) =>
                                        onSerialConfigChange({
                                            dataBits: parseInt(e.target.value) as DataBits
                                        })
                                    }
                                    sx={styles.label_normal}
                                >
                                    {DATA_BITS.map((value) => (
                                        <option key={value} value={value}>
                                            {value}
                                        </option>
                                    ))}
                                </Select>
                            </InputGroup>

                            {/* 停止位 */}
                            <InputGroup display="flex" alignItems="center">
                                <InputLeftAddon sx={styles.label_bold} w={label_width}>
                                    {/* 停止位 */}
                                    {t('pages:deviceManager.device.stopBits.label')}
                                </InputLeftAddon>
                                <Select
                                    value={serialConfig.stopBits}
                                    onChange={(e) => {
                                        onSerialConfigChange({
                                            stopBits: parseFloat(e.target.value) as StopBits
                                        });
                                    }}
                                    sx={styles.label_normal}
                                >
                                    {STOP_BITS.map((value) => (
                                        <option key={value} value={value}>
                                            {value}
                                        </option>
                                    ))}
                                </Select>
                            </InputGroup>

                            {/* 校验位 */}
                            <InputGroup display="flex" alignItems="center">
                                <InputLeftAddon sx={styles.label_bold} w={label_width}>
                                    {/* 校验位 */}
                                    {t('pages:deviceManager.device.parity.label')}
                                </InputLeftAddon>
                                <Select
                                    value={serialConfig.parity}
                                    onChange={(e) => {
                                        onSerialConfigChange({
                                            parity: e.target.value as Parity
                                        });
                                    }}
                                    sx={styles.label_normal}
                                >
                                    {PARITY.map((item) => (
                                        <option key={item} value={item}>
                                            {item}
                                        </option>
                                    ))}
                                </Select>
                            </InputGroup>

                            {/* 流控制 */}
                            <InputGroup display="flex" alignItems="center">
                                <InputLeftAddon sx={styles.label_bold} w={label_width}>
                                    {/* 流控 */}
                                    {t('pages:deviceManager.device.flowControl.label')}
                                </InputLeftAddon>
                                <Select isDisabled={true} sx={styles.label_normal}>
                                  
                                    {FLOW_CONTROL.map((item) => (
                                        <option key={item} value={item}>
                                            {item}
                                        </option>
                                    ))}
                                </Select>
                            </InputGroup>
                        </SimpleGrid>
                    </FormControl>
                </Box>

                {/* 超时设置 */}
                <InputGroup
                    gap={4}
                    display="flex"
                    alignItems="center"
                    justifyContent={'space-between'}
                >
                    <InputLeftAddon sx={styles.label_bold}>
                        {/* 通讯超时(秒) */}
                        {t('pages:deviceManager.device.timeout.label')}
                    </InputLeftAddon>
                    <NumberInput
                        w="100px"
                        min={20}
                        max={120}
                        step={5}
                        allowMouseWheel
                        keepWithinRange
                        clampValueOnBlur
                        value={serialConfig.timeout}
                        onChange={(_, value) => {
                            onSerialConfigChange({ timeout: value as number });
                        }}
                    >
                        <NumberInputField sx={styles.label_normal} />
                        <NumberInputStepper>
                            <NumberIncrementStepper />
                            <NumberDecrementStepper />
                        </NumberInputStepper>
                    </NumberInput>

                    <InputRightAddon sx={styles.label_bold}>
                        {/* (秒) */}
                        <Text>{t('pages:deviceManager.device.timeout.unit')}</Text>
                        {/* <Button colorScheme="blue">测试连接</Button> */}
                    </InputRightAddon>
                    <Spacer />

                    {/* <Button mx={10} my={10} colorScheme="blue" w="100px">
                        测试连接
                    </Button> */}
                </InputGroup>
            </VStack>
        </>
    );
};

export default DeviceSelector;
