import React from 'react';
import {
    <PERSON>dal,
    ModalOverlay,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    ModalCloseButton,
    Button,
    HStack,
    Text,
    Icon
} from '@chakra-ui/react';
import { RepeatIcon, DeleteIcon } from '@chakra-ui/icons';

interface RestoreDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
}

export const RestoreFilterDialog: React.FC<RestoreDialogProps> = ({ isOpen, onClose, onConfirm }) => {
    const handleConfirm = () => {
        onConfirm();
        onClose();
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} isCentered>
            <ModalOverlay />
            <ModalContent>
                <ModalHeader fontFamily="MiSans-Bold" bg="teal.500" color="white">
                    <HStack>
                        <Icon as={RepeatIcon} boxSize={5} />
                        <Text>恢复默认设置</Text>
                    </HStack>
                </ModalHeader>
                <ModalCloseButton />

                <ModalBody>
                    <Text fontFamily="MiSans-Normal" py={4}>
                        当前滤光片设置将会被重置为默认值，是否继续？
                    </Text>
                </ModalBody>

                <ModalFooter>
                    <Button colorScheme="blue" mr={3} onClick={handleConfirm}>
                        确定
                    </Button>
                    <Button onClick={onClose}>取消</Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
};

interface DeleteDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    count: number;
}

export const DeleteFilterDialog: React.FC<DeleteDialogProps> = ({ isOpen, onClose, onConfirm, count }) => {
    const handleConfirm = () => {
        onConfirm();
        onClose();
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} isCentered>
            <ModalOverlay />
            <ModalContent>
                <ModalHeader fontFamily="MiSans-Bold" bg="teal.500" color="white">
                    <HStack>
                        <Icon as={DeleteIcon} boxSize={5} />
                        <Text>删除滤光片</Text>
                    </HStack>
                </ModalHeader>
                <ModalCloseButton />

                <ModalBody>
                    <Text fontFamily="MiSans-Normal" py={4}>
                        确定要删除选中的 {count} 个滤光片吗？
                    </Text>
                </ModalBody>

                <ModalFooter>
                    <Button colorScheme="red" mr={3} onClick={handleConfirm}>
                        确定
                    </Button>
                    <Button onClick={onClose}>取消</Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
};
