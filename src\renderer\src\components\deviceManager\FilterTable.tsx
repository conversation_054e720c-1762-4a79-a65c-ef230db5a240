import React from 'react';
import {
    TableContainer,
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    useColorModeValue
} from '@chakra-ui/react';
import type { Filters } from '@shared/types';
import { styles } from '@renderer/utils/theme';
import { useTranslation } from 'react-i18next';
// import { useState } from 'react';

interface FilterTableProps {
    filters: Filters[];
}

const FilterTable: React.FC<FilterTableProps> = ({ filters }) => {
    const hoverBg = useColorModeValue('blue.50', 'blue.900');
    const { t } = useTranslation(['common', 'pages']);
    const hoverAction = {
        bg: hoverBg,
        borderWidth: '2px',
        borderColor: 'blue.400',
        transition: 'all 0.005s ease-out'
    } as const;

    return (
        <>
            {/* <HStack align="flex-start" spacing={4}> */}
            <TableContainer>
                <Table variant="striped">
                    <Thead>
                        <Tr>
                            <Th sx={styles.tableHeader}>
                                {/* 滤光片号 */}
                                {t('pages:deviceManager.filter.filterNo')}
                            </Th>
                            <Th sx={styles.tableHeader}>
                                {/* 波长(nm) */}
                                {t('pages:deviceManager.filter.wavelength')}
                            </Th>
                        </Tr>
                    </Thead>
                    <Tbody>
                        {filters
                            .sort((a, b) => a.no - b.no)
                            .map((filter) => (
                                <Tr
                                    key={filter.no}
                                    _hover={hoverAction}
                                    // transition="all 0.05s ease-out"
                                    transition="all 0.005s linear"
                                >
                                    <Td sx={styles.tableCell}>{filter.no}</Td>
                                    <Td sx={styles.tableCell}>{filter.wavelength}</Td>
                                </Tr>
                            ))}
                    </Tbody>
                </Table>
            </TableContainer>
            {/* </HStack> */}
        </>
    );
};

export default FilterTable;
