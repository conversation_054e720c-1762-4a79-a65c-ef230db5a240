import React, { useEffect, useState } from 'react';
import { Box, FormControl, FormLabel, IconButton, Input, useToast, VStack } from '@chakra-ui/react';
import { EditIcon } from '@chakra-ui/icons';
import { useTranslation } from 'react-i18next';
import logger from '@renderer/utils/logger';

// lis文件保存路径存储key
const LIS_PATH_STORE_KEY = 'app.lisPath';

const LisPara: React.FC = () => {
    const { t } = useTranslation(['components', 'common']);
    const [lisPath, setLisPath] = useState<string>('');
    const toast = useToast();

    // 加载保存的LIS路径
    useEffect(() => {
        loadLisPath();
    }, []);

    // 从系统设置加载LIS路径
    const loadLisPath = async () => {
        // 如果没有保存的路径，使用用户目录作为默认值
        const userPath = await window.customApi.app.getUserPath();
        logger.info('userPath', {
            data: userPath,
            component: './src/renderer/src/components/report/LisPara.tsx'
        });

        try {
            const response = await window.customApi.store.get<string>(LIS_PATH_STORE_KEY);
            logger.info('Load path response:', {
                data: response,
                component: './src/renderer/src/components/report/LisPara.tsx'
            });
            if (response.success && response.data) {
                setLisPath(response.data);
            } else {
                setLisPath(userPath);
                await saveLisPath(userPath); // 保存默认路径
            }
        } catch (error) {
            logger.error('Load lis path failed:', error, {
                component: './src/renderer/src/components/report/LisPara.tsx'
            });
            setLisPath(userPath);
            await saveLisPath(userPath); // 保存默认路径
        }
    };

    const saveLisPath = async (path: string) => {
        try {
            logger.info('Saving path:', {
                data: path,
                component: './src/renderer/src/components/report/LisPara.tsx'
            });
            const response = await window.customApi.store.set(LIS_PATH_STORE_KEY, path);
            logger.info('Save path response:', {
                data: response,
                component: './src/renderer/src/components/report/LisPara.tsx'
            });
            if (!response.success) {
                throw new Error(response.error || t('common:message.saveFailed'));
            }
        } catch (error) {
            logger.error('Save lis path failed:', error, {
                component: './src/renderer/src/components/report/LisPara.tsx'
            });
            toast({
                title: t('components:lisPara.toast.error.title'),
                description: t('components:lisPara.toast.error.description'),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
        }
    };

    // 选择LIS文件保存路径
    const handleSelectPath = async () => {
        try {
            const response = await window.customApi.app.openFolderDialog({
                title: t('components:lisPara.selectPath'),
                defaultPath: lisPath,
                // buttonLabel: t('common:button.confirm'),
                properties: ['openDirectory']
            });
            if (response.filePaths.length > 0) {
                const selectedPath = response.filePaths[0];
                logger.info('Selected path from dialog:', {
                    data: selectedPath,
                    component: './src/renderer/src/components/report/LisPara.tsx'
                });
                setLisPath(selectedPath);
                await saveLisPath(selectedPath);
                toast({
                    title: t('components:lisPara.toast.success.title'),
                    description: t('components:lisPara.toast.success.description'),
                    status: 'success',
                    duration: 3000,
                    isClosable: true
                });
            } else {
                toast({
                    title: t('components:lisPara.toast.noSelection.title'),
                    description: t('components:lisPara.toast.noSelection.description'),
                    status: 'info',
                    duration: 3000,
                    isClosable: true
                });
            }
        } catch (error) {
            logger.error('Select path failed:', error, {
                component: './src/renderer/src/components/report/LisPara.tsx'
            });
            toast({
                title: t('components:lisPara.toast.error.title'),
                description: t('components:lisPara.toast.error.description'),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
        }
    };

    return (
        <>
            <VStack spacing={4} align="stretch">
                <FormControl>
                    <FormLabel>{t('components:lisPara.defaultPath')}</FormLabel>
                    <Box display="flex" gap={2}>
                        <Input
                            value={lisPath}
                            readOnly
                            placeholder={t('components:lisPara.placeholder')}
                        />
                        <IconButton
                            aria-label={t('components:lisPara.selectPath')}
                            icon={<EditIcon />}
                            onClick={handleSelectPath}
                            colorScheme="teal"
                        />
                    </Box>
                </FormControl>
            </VStack>
        </>
    );
};

export default LisPara;
