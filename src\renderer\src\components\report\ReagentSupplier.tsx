import React, { useEffect, useState } from 'react';
import { Box, Input, List, ListItem, VStack, HStack, IconButton, useToast } from '@chakra-ui/react';
import { AddIcon, DeleteIcon } from '@chakra-ui/icons';
import { useTranslation } from 'react-i18next';
import logger from '../../utils/logger';

interface ReagentSupplierProps {
    onChange?: (suppliers: string[]) => void;
    defaultSuppliers?: string[];
}

// 试剂供应商存储key
const REAGENT_SUPPLIER_STORE_KEY = 'app.reagentSupplier';

export const ReagentSupplier: React.FC<ReagentSupplierProps> = ({
    onChange,
    defaultSuppliers = []
}) => {
    const { t } = useTranslation(['components', 'common']);
    const toast = useToast();
    const [suppliers, setSuppliers] = useState<string[]>(defaultSuppliers);
    const [selectedIndex, setSelectedIndex] = useState<number>(-1);
    const [newSupplier, setNewSupplier] = useState('');

    // 加载保存的供应商列表
    useEffect(() => {
        loadReagentSupplier();
    }, []);

    // 从系统设置加载供应商列表
    const loadReagentSupplier = async () => {
        try {
            const response = await window.customApi.store.get<string[]>(REAGENT_SUPPLIER_STORE_KEY);
            if (response.success && response.data) {
                setSuppliers(response.data);
            }
        } catch (error) {
            logger.error('Failed to load reagent suppliers:', error, {
                component: './src/renderer/src/components/report/ReagentSupplier.tsx'
            });
        }
    };

    // 保存供应商列表到配置
    const saveReagentSuppliers = async (updatedSuppliers: string[]) => {
        try {
            const response = await window.customApi.store.set(
                REAGENT_SUPPLIER_STORE_KEY,
                updatedSuppliers
            );
            if (!response.success) {
                throw new Error(response.error || t('common:error.saveFailed'));
            }
            toast({
                title: t('components:reagentSupplier.toast.success.title'),
                description: t('components:reagentSupplier.toast.success.description'),
                status: 'success',
                duration: 3000,
                isClosable: true
            });
        } catch (error) {
            logger.error('Failed to save reagent suppliers:', error, {
                component: './src/renderer/src/components/report/ReagentSupplier.tsx'
            });
            toast({
                title: t('components:reagentSupplier.toast.error.title'),
                description: t('components:reagentSupplier.toast.error.description'),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
        }
    };

    const handleAddSupplier = async () => {
        if (newSupplier.trim()) {
            const updatedSuppliers = [...suppliers, newSupplier.trim()];
            setSuppliers(updatedSuppliers);
            setNewSupplier('');
            onChange?.(updatedSuppliers);
            await saveReagentSuppliers(updatedSuppliers);
        }
    };

    const handleDeleteSupplier = async () => {
        if (selectedIndex >= 0) {
            const updatedSuppliers = suppliers.filter((_, index) => index !== selectedIndex);
            setSuppliers(updatedSuppliers);
            setSelectedIndex(-1);
            onChange?.(updatedSuppliers);
            await saveReagentSuppliers(updatedSuppliers);
        }
    };

    return (
        <HStack spacing={4} align="flex-start">
            <Box
                display="flex"
                flexDirection="column"
                flex={1}
                borderWidth="1px"
                borderRadius="md"
                overflowY="auto"
                h={'400px'}
            >
                <Input
                    size="sm"
                    value={newSupplier}
                    onChange={(e) => setNewSupplier(e.target.value)}
                    placeholder={t('components:reagentSupplier.placeholder')}
                />
                <List spacing={1} h={'100%'} overflowY="auto">
                    {suppliers.map((supplier, index) => (
                        <ListItem
                            key={index}
                            px={2}
                            py={1}
                            cursor="pointer"
                            bg={selectedIndex === index ? 'blue.50' : 'transparent'}
                            _hover={{ bg: 'gray.50' }}
                            onClick={() => setSelectedIndex(index)}
                        >
                            {supplier}
                        </ListItem>
                    ))}
                </List>
            </Box>

            <VStack spacing={4} width="40px">
                <VStack spacing={2} width="100%">
                    <IconButton
                        aria-label={t('components:reagentSupplier.title')}
                        icon={<AddIcon />}
                        colorScheme="blue"
                        width="100%"
                        onClick={handleAddSupplier}
                        isDisabled={!newSupplier.trim()}
                    />
                </VStack>
                <IconButton
                    aria-label={t('components:reagentSupplier.title')}
                    icon={<DeleteIcon />}
                    colorScheme="red"
                    width="100%"
                    onClick={handleDeleteSupplier}
                    isDisabled={selectedIndex === -1}
                />
            </VStack>
        </HStack>
    );
};

export default ReagentSupplier;
