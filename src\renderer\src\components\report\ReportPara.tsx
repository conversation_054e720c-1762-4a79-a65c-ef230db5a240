import React, { useEffect, useState } from 'react';
import { VStack, InputGroup, InputLeftAddon, HStack, Text, Spacer, Input, useToast } from '@chakra-ui/react';
import logger from '@renderer/utils/logger';
import { useTranslation } from 'react-i18next';

// 报告参数存储key
const REPORT_PARA_STORE_KEY = 'app.reportPara';

// 报告参数数据结构
interface ReportParaData {
    mainTitle: string; // 报告主标题
    subTitle: string; // 报告副标题
    testMethod: string; // 检测方法
    testBasis: string; // 检测依据
    testOperator: string; // 检测人员
    reviewer: string; // 审核员
}

// 默认报告参数
const defaultReportPara: ReportParaData = {
    mainTitle: '',
    subTitle: '',
    testMethod: 'ELISA kit',
    testBasis: 'ELISA kit manual',
    testOperator: '',
    reviewer: ''
};

const ReportPara: React.FC = () => {
    const toast = useToast();
    const { t } = useTranslation(['components', 'common']);
    const maxLeftWidth = '120px';

    // 报告参数状态
    const [reportPara, setReportPara] = useState<ReportParaData>(defaultReportPara);

    useEffect(() => {
        loadReportPara();
    }, []);

    // 加载报告参数
    const loadReportPara = async () => {
        try {
            const response = await window.customApi.store.get<ReportParaData>(REPORT_PARA_STORE_KEY);
            logger.info('Load report para response:', {
                data: response,
                component: './src/renderer/src/components/report/ReportPara.tsx'
            });

            if (response.success && response.data) {
                logger.info('Loading existing data from storage:', {
                    data: response.data,
                    component: './src/renderer/src/components/report/ReportPara.tsx'
                });
                // 直接使用存储中的数据
                setReportPara(response.data);
            } else {
                // 如果没有保存的数据，使用默认值并保存
                logger.info('No existing data, using default values:', {
                    data: defaultReportPara,
                    component: './src/renderer/src/components/report/ReportPara.tsx'
                });
                setReportPara(defaultReportPara);
                await saveReportPara(defaultReportPara);
            }
        } catch (error) {
            logger.error('Load report para failed:', error, {
                component: './src/renderer/src/components/report/ReportPara.tsx'
            });
            logger.info('Error occurred, using default values:', {
                data: defaultReportPara,
                component: './src/renderer/src/components/report/ReportPara.tsx'
            });
            setReportPara(defaultReportPara);
            await saveReportPara(defaultReportPara);
        }
    };

    // 保存报告参数
    const saveReportPara = async (data: ReportParaData) => {
        try {
            logger.info('Saving report para:', {
                data: data,
                component: './src/renderer/src/components/report/ReportPara.tsx'
            });
            const response = await window.customApi.store.set(REPORT_PARA_STORE_KEY, data);
            logger.info('Save report para response:', {
                data: response,
                component: './src/renderer/src/components/report/ReportPara.tsx'
            });

            if (!response.success) {
                throw new Error(response.error || t('common:message.saveFailed'));
            }
        } catch (error) {
            logger.error('Save report para failed:', error, {
                component: './src/renderer/src/components/report/ReportPara.tsx'
            });
            toast({
                title: t('components:ReportPara.toast.error.title'),
                description: t('components:ReportPara.toast.error.description'),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
        }
    };

    // 处理输入框变化
    const handleInputChange = async (field: keyof ReportParaData, value: string) => {
        const newReportPara = { ...reportPara, [field]: value };
        setReportPara(newReportPara);
        // 实时保存
        await saveReportPara(newReportPara);
    };

    // 重置为默认值（调试用）
    const resetToDefault = async () => {
        logger.info('Resetting to default values:', {
            data: defaultReportPara,
            component: './src/renderer/src/components/report/ReportPara.tsx'
        });
        setReportPara(defaultReportPara);
        await saveReportPara(defaultReportPara);
    };

    // 临时调试：在控制台添加重置函数
    React.useEffect(() => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (window as any).resetReportPara = resetToDefault;
        return () => {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            delete (window as any).resetReportPara;
        };
    }, []);

    return (
        <>
            <HStack
                spacing={4}
                align="stretch"
                // divider={<StackDivider borderColor="gray.200" borderWidth="0.5px" />}
            >
                <VStack
                    // mx={4}
                    // my={4}
                    w="100%"
                    justifyContent={'space-between'}
                    alignItems={'center'}
                >
                    <InputGroup>
                        <InputLeftAddon w={maxLeftWidth}>
                            {/* 报告单主标题 */}
                            <Text>{t('components:ReportPara.reportMainTitile')}</Text>
                        </InputLeftAddon>
                        <Input
                            value={reportPara.mainTitle}
                            onChange={(e) => handleInputChange('mainTitle', e.target.value)}
                            placeholder={t('components:ReportPara.reportMainTitile_placeholder')}
                        />
                    </InputGroup>

                    <InputGroup>
                        <InputLeftAddon w={maxLeftWidth}>
                            {/* 报告单副标题 */}
                            <Text>{t('components:ReportPara.reportSubTitile')}</Text>
                        </InputLeftAddon>
                        <Input
                            value={reportPara.subTitle}
                            onChange={(e) => handleInputChange('subTitle', e.target.value)}
                            placeholder={t('components:ReportPara.reportSubTitile_placeholder')}
                        />
                    </InputGroup>

                    <InputGroup>
                        <InputLeftAddon w={maxLeftWidth}>
                            {/* 检测方法 */}
                            <Text>{t('components:ReportPara.testMethod')}</Text>
                        </InputLeftAddon>
                        <Input
                            value={reportPara.testMethod}
                            onChange={(e) => handleInputChange('testMethod', e.target.value)}
                            placeholder={t('components:ReportPara.testMethod_placeholder')}
                        />
                    </InputGroup>

                    <InputGroup>
                        <InputLeftAddon w={maxLeftWidth}>
                            {/* 检测依据 */}
                            <Text>{t('components:ReportPara.testBasis')}</Text>
                        </InputLeftAddon>
                        <Input
                            value={reportPara.testBasis}
                            onChange={(e) => handleInputChange('testBasis', e.target.value)}
                            placeholder={t('components:ReportPara.testBasis_placeholder')}
                        />
                    </InputGroup>

                    <InputGroup>
                        <InputLeftAddon w={maxLeftWidth}>
                            {/* 检验员 */}
                            <Text>{t('components:ReportPara.testOperator')}</Text>
                        </InputLeftAddon>
                        <Input
                            value={reportPara.testOperator}
                            onChange={(e) => handleInputChange('testOperator', e.target.value)}
                            placeholder={t('components:ReportPara.testOperator_placeholder')}
                        />
                    </InputGroup>

                    <InputGroup>
                        <InputLeftAddon w={maxLeftWidth}>
                            {/* 审核员 */}
                            <Text>{t('components:ReportPara.reviewer')}</Text>
                        </InputLeftAddon>
                        <Input
                            value={reportPara.reviewer}
                            onChange={(e) => handleInputChange('reviewer', e.target.value)}
                            placeholder={t('components:ReportPara.reviewer_placeholder')}
                        />
                    </InputGroup>

                    <Spacer />
                </VStack>
            </HStack>
        </>
    );
};

export default ReportPara;
