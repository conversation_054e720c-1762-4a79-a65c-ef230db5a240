import { useState, useEffect } from 'react';
import { DeviceModel } from '@shared/commondefines';
import logger from '@renderer/utils/logger';

const DEFAULT_DEVICE_STORE_KEY = 'app.defaultDevice';

interface UseDefaultDeviceReturn {
    defaultDevice: DeviceModel;
    setDefaultDevice: (device: DeviceModel) => void;
    saveDefaultDevice: () => Promise<boolean>;
    isLoading: boolean;
}

export const useDefaultDevice = (shouldLoad: boolean = true): UseDefaultDeviceReturn => {
    const [defaultDevice, setDefaultDeviceState] = useState<DeviceModel>(DeviceModel.unknown);
    const [isLoading, setIsLoading] = useState(false);

    const setDefaultDevice = (device: DeviceModel) => {
        setDefaultDeviceState(device);
        logger.info('设置默认设备: ' + device, {
            component: './src/renderer/src/hooks/useDefaultDevice.ts'
        });
    };

    const saveDefaultDevice = async (): Promise<boolean> => {
        try {
            const response = await window.customApi.store.set(DEFAULT_DEVICE_STORE_KEY, defaultDevice);
            if (response.success) {
                logger.info('默认设备保存成功: ' + defaultDevice, {
                    component: './src/renderer/src/hooks/useDefaultDevice.ts'
                });
                return true;
            } else {
                logger.error('保存默认设备失败', response.error, {
                    component: './src/renderer/src/hooks/useDefaultDevice.ts'
                });
                return false;
            }
        } catch (error) {
            logger.error('保存默认设备异常', error, {
                component: './src/renderer/src/hooks/useDefaultDevice.ts'
            });
            return false;
        }
    };

    const loadDefaultDevice = async () => {
        setIsLoading(true);
        try {
            const response = await window.customApi.store.get<DeviceModel>(DEFAULT_DEVICE_STORE_KEY, DeviceModel.unknown);

            let defaultDev: DeviceModel = response.data as DeviceModel;

            if (!response.success) {
                logger.error('获取默认设备信息失败', response.error, {
                    component: './src/renderer/src/hooks/useDefaultDevice.ts'
                });
                defaultDev = DeviceModel.unknown;
            }

            setDefaultDeviceState(defaultDev);
            logger.info('加载默认设备: ' + defaultDev, {
                component: './src/renderer/src/hooks/useDefaultDevice.ts'
            });
        } catch (error) {
            logger.error('加载默认设备异常', error, {
                component: './src/renderer/src/hooks/useDefaultDevice.ts'
            });
            setDefaultDeviceState(DeviceModel.unknown);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if (shouldLoad) {
            loadDefaultDevice();
        }
    }, [shouldLoad]);

    return {
        defaultDevice,
        setDefaultDevice,
        saveDefaultDevice,
        isLoading
    };
};
