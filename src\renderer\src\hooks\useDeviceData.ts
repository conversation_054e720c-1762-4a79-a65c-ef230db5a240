import { useState, useEffect, useCallback, useRef } from 'react';
import { DeviceConfig } from '@shared/types';
import { DeviceModel } from '@shared/commondefines';
import logger from '@renderer/utils/logger';

interface UseDeviceDataOptions {
    defaultDevice?: DeviceModel;
    shouldLoad?: boolean;
}

interface UseDeviceDataReturn {
    devicesList: DeviceConfig[];
    isLoading: boolean;
    error: string | null;
    reloadDeviceList: () => Promise<void>;
}

export const useDeviceData = (options: UseDeviceDataOptions = {}): UseDeviceDataReturn => {
    const { defaultDevice, shouldLoad = true } = options;

    const [devicesList, setDevicesList] = useState<DeviceConfig[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // 使用ref跟踪当前是否正在加载，防止重复请求
    const loadingRef = useRef(false);
    const lastLoadedDeviceRef = useRef<DeviceModel | undefined>();
    const devicesListRef = useRef<DeviceConfig[]>([]);

    // 同步ref和state
    devicesListRef.current = devicesList;

    const loadDeviceList = useCallback(async () => {
        // 强化的防重复加载检查
        if (loadingRef.current) {
            logger.info('跳过重复加载请求，当前正在加载中', {
                component: './src/renderer/src/hooks/useDeviceData.ts',
                data: { defaultDevice }
            });
            return;
        }

        // 如果设备没有变化且已有数据，跳过加载
        if (lastLoadedDeviceRef.current === defaultDevice && devicesListRef.current.length > 0) {
            logger.info('设备未变化且已有数据，跳过加载', {
                component: './src/renderer/src/hooks/useDeviceData.ts',
                data: {
                    defaultDevice,
                    lastDevice: lastLoadedDeviceRef.current,
                    hasData: devicesListRef.current.length > 0
                }
            });
            return;
        }

        loadingRef.current = true;
        setIsLoading(true);
        setError(null);

        try {
            logger.info('开始加载设备配置列表', {
                component: './src/renderer/src/hooks/useDeviceData.ts',
                data: { defaultDevice, lastDevice: lastLoadedDeviceRef.current }
            });

            const devInfo = await window.customApi.configInfo.getDeviceConfigInfo();

            logger.info('设备配置列表加载完成', {
                component: './src/renderer/src/hooks/useDeviceData.ts',
                data: { devInfo, count: devInfo.length }
            });

            setDevicesList(devInfo as DeviceConfig[]);
            lastLoadedDeviceRef.current = defaultDevice;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : '加载设备列表失败';
            setError(errorMessage);
            logger.error('加载设备列表失败', err, {
                component: './src/renderer/src/hooks/useDeviceData.ts'
            });
        } finally {
            setIsLoading(false);
            loadingRef.current = false;
        }
    }, [defaultDevice]); // 只依赖 defaultDevice，避免循环依赖

    // 当默认设备变化或需要加载时，重新加载设备列表
    useEffect(() => {
        if (shouldLoad) {
            loadDeviceList();
        }
    }, [defaultDevice, shouldLoad, loadDeviceList]);

    return {
        devicesList,
        isLoading,
        error,
        reloadDeviceList: loadDeviceList
    };
};
