import { useState, useEffect } from 'react';
import { Filters } from '@shared/types';
import { DeviceModel } from '@shared/commondefines';
import logger from '@renderer/utils/logger';

const DEFAULT_DEVICE_STORE_KEY = 'app.defaultDevice';

// 不使用备用，没有设备时，显示空，免得引起误解
// 备用filters，如果无法从设备配置获取则使用
// const fallbackFilters: Filters[] = [
//     { no: 1, wavelength: 405 },
//     { no: 2, wavelength: 414 },
//     { no: 3, wavelength: 450 },
//     { no: 4, wavelength: 492 },
//     { no: 5, wavelength: 540 },
//     { no: 6, wavelength: 570 },
//     { no: 7, wavelength: 630 },
//     { no: 8, wavelength: 690 }
// ] as const;

interface UseDeviceFiltersReturn {
    filters: Filters[];
    defaultDevice: DeviceModel;
    isLoading: boolean;
    error: string | null;
}

/**
 * 获取当前默认设备的滤光片配置
 * @param shouldLoad 是否应该加载数据
 * @returns 设备滤光片配置和相关状态
 */
export const useDeviceFilters = (shouldLoad: boolean): UseDeviceFiltersReturn => {
    const [filters, setFilters] = useState<Filters[]>([]);
    const [defaultDevice, setDefaultDevice] = useState<DeviceModel>(DeviceModel.unknown);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!shouldLoad) return;

        const loadDeviceFilters = async () => {
            setIsLoading(true);
            setError(null);

            try {
                logger.info('开始加载设备滤光片配置', {
                    component: './src/renderer/src/hooks/useDeviceFilters.ts'
                });

                // 1. 获取默认设备
                const response_defaultDevice = await window.customApi.store.get<DeviceModel>(
                    DEFAULT_DEVICE_STORE_KEY,
                    DeviceModel.unknown
                );

                let defaultDev: DeviceModel = DeviceModel.unknown;
                if (response_defaultDevice.success) {
                    defaultDev = response_defaultDevice.data as DeviceModel;
                } else {
                    logger.error('获取默认设备失败', response_defaultDevice.error, {
                        component: './src/renderer/src/hooks/useDeviceFilters.ts'
                    });
                }

                // 2. 获取设备配置信息
                const deviceInfo = await window.customApi.configInfo.getDeviceConfigInfo();

                // 3. 查找默认设备对应的filters
                let deviceFilters: Filters[] = [];
                if (deviceInfo && deviceInfo.length > 0) {
                    // 如果默认设备未知或无效，使用第一个设备
                    if (defaultDev === DeviceModel.unknown) {
                        defaultDev = deviceInfo[0].model as DeviceModel;
                        logger.info('默认设备未知，使用第一个设备: ' + defaultDev, {
                            component: './src/renderer/src/hooks/useDeviceFilters.ts'
                        });
                    }

                    // 查找对应设备的filters
                    const currentDevice = deviceInfo.find((d) => d.model === defaultDev);
                    if (
                        currentDevice &&
                        currentDevice.filters &&
                        currentDevice.filters.length > 0
                    ) {
                        deviceFilters = currentDevice.filters;
                        logger.info('找到设备filters', {
                            component: './src/renderer/src/hooks/useDeviceFilters.ts',
                            data: { device: defaultDev, filtersCount: deviceFilters.length }
                        });
                    } else {
                        logger.warn('设备filters不存在，使用备用filters', {
                            component: './src/renderer/src/hooks/useDeviceFilters.ts',
                            data: { device: defaultDev }
                        });
                    }
                } else {
                    logger.warn('无法获取设备配置信息，使用备用数据', {
                        component: './src/renderer/src/hooks/useDeviceFilters.ts'
                    });
                }

                setDefaultDevice(defaultDev);
                setFilters(deviceFilters);

                logger.info('设备滤光片配置加载完成', {
                    component: './src/renderer/src/hooks/useDeviceFilters.ts',
                    data: {
                        defaultDevice: defaultDev,
                        filtersCount: deviceFilters.length,
                        usingFallback: deviceFilters.length === 0
                    }
                });
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : '加载设备滤光片配置失败';
                setError(errorMessage);
                logger.error('设备滤光片配置加载失败', err, {
                    component: './src/renderer/src/hooks/useDeviceFilters.ts'
                });

                // 发生错误时使用备用数据
                setFilters([]);
            } finally {
                setIsLoading(false);
            }
        };

        loadDeviceFilters();
    }, [shouldLoad]); // 只依赖 shouldLoad，不重复加载

    return {
        filters,
        defaultDevice,
        isLoading,
        error
    };
};
