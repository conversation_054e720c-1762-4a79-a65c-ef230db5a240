import { useState, useCallback } from 'react';
import { ElisaFittingEngine } from '@renderer/utils/elisaFittingEngine';
import { FittingModel, FittingOptions, FittingResult, StandardCurveData } from '@shared/types';
import logger from '@renderer/utils/logger';

// 拟合配置接口
interface FittingConfig {
    model: FittingModel;
    options?: {
        polynomialDegree?: number; // 多项式阶数
        maxIterations?: number; // 最大迭代次数
        errorTolerance?: number; // 误差容限
        damping?: number; // 阻尼因子
    };
}

interface UseElisaFittingReturn {
    // 核心状态
    result: FittingResult | null;
    isLoading: boolean;
    error: string | null;

    // 简化操作
    fit: (data: StandardCurveData, config?: Partial<FittingConfig>) => Promise<void>;
    clear: () => void;

    // 便捷预测
    predictConcentration: (response: number) => number | null;
    predictResponse: (concentration: number) => number | null;

    // 质量指标
    isValid: boolean;
    rSquared: number;
    equation: string;
}

// 默认配置
const DEFAULT_CONFIG: FittingConfig = {
    model: 'LOGISTIC_4P',
    options: {
        polynomialDegree: 2,
        maxIterations: 1000,
        errorTolerance: 1e-8,
        damping: 1e-3
    }
};

export const useElisaFitting = (defaultConfig: Partial<FittingConfig> = {}): UseElisaFittingReturn => {
    const [result, setResult] = useState<FittingResult | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // 一键拟合函数
    const fit = useCallback(
        async (data: StandardCurveData, config: Partial<FittingConfig> = {}) => {
            // 数据验证
            if (!data?.concentrations?.length || !data?.responses?.length) {
                setError('数据不完整');
                return;
            }

            if (data.concentrations.length !== data.responses.length) {
                setError('浓度和响应数据长度不匹配');
                return;
            }

            if (data.concentrations.length < 3) {
                setError('数据点不足，至少需要3个点');
                return;
            }

            // 验证数据有效性
            if (data.concentrations.some((x) => !isFinite(x) || x <= 0)) {
                setError('浓度值必须为大于0的有效数字');
                return;
            }

            if (data.responses.some((y) => !isFinite(y))) {
                setError('响应值必须为有效数字');
                return;
            }

            setIsLoading(true);
            setError(null);

            try {
                // 合并配置
                const mergedConfig = {
                    ...DEFAULT_CONFIG,
                    ...defaultConfig,
                    ...config,
                    options: {
                        ...DEFAULT_CONFIG.options,
                        ...(defaultConfig.options || {}),
                        ...(config.options || {})
                    }
                };

                const options: FittingOptions = {
                    model: mergedConfig.model,
                    ...mergedConfig.options
                };

                logger.info('开始ELISA拟合', {
                    data: {
                        ...options,
                        points: data.concentrations.length,
                        concentrationRange: {
                            min: Math.min(...data.concentrations),
                            max: Math.max(...data.concentrations)
                        }
                    },
                    component: './src/renderer/src/hooks/useElisaFitting.ts'
                });

                const fittingResult = ElisaFittingEngine.fit(data, options);

                // 记录拟合结果
                logger.info('ELISA拟合完成', {
                    data: {
                        model: options.model,
                        polynomialDegree: options.polynomialDegree,
                        rSquared: fittingResult.rSquared,
                        iterations: fittingResult.iterations,
                        convergence: fittingResult.convergence,
                        warnings: fittingResult.qualityMetrics?.warnings
                    },
                    component: './src/renderer/src/hooks/useElisaFitting.ts'
                });

                setResult(fittingResult);
            } catch (err) {
                const errorMsg = err instanceof Error ? err.message : '拟合失败';
                setError(errorMsg);
                setResult(null);

                logger.error('ELISA拟合失败', err, {
                    component: './src/renderer/src/hooks/useElisaFitting.ts'
                });
            } finally {
                setIsLoading(false);
            }
        },
        [defaultConfig]
    );

    // 清除结果
    const clear = useCallback(() => {
        setResult(null);
        setError(null);
    }, []);

    // 预测浓度（带安全检查）
    const predictConcentration = useCallback(
        (response: number): number | null => {
            if (!result?.predictConcentration) return null;
            try {
                const concentration = result.predictConcentration(response);
                return isFinite(concentration) ? concentration : null;
            } catch {
                return null;
            }
        },
        [result]
    );

    // 预测响应（带安全检查）
    const predictResponse = useCallback(
        (concentration: number): number | null => {
            if (!result?.predict) return null;
            try {
                const response = result.predict(concentration);
                return isFinite(response) ? response : null;
            } catch {
                return null;
            }
        },
        [result]
    );

    return {
        // 核心状态
        result,
        isLoading,
        error,

        // 简化操作
        fit,
        clear,

        // 便捷预测
        predictConcentration,
        predictResponse,

        // 质量指标（直接从result提取）
        isValid: result?.qualityMetrics?.isValid ?? false,
        rSquared: result?.rSquared ?? 0,
        equation: result?.equation ?? ''
    };
};
