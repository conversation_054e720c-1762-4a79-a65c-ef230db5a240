import { useTranslation } from 'react-i18next';

export const useTranslatedOptions = () => {
    const { t } = useTranslation(['common', 'pages', 'components']);

    const translateOptions = (options: readonly { readonly id: number | string; readonly label: string }[], translationKey: string) => {
        return options.map((option) => ({
            ...option,
            label: t(`${translationKey}.${option.label}`)
        }));
    };

    return { translateOptions };
};
