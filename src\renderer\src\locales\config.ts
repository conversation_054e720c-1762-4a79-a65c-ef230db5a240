import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import zhCN from './zh';
import enUS from './en';
import logger from '../utils/logger';

// 1. 语言配置
export const LANGUAGELIST = {
    zh: '简体中文',
    en: 'English'
} as const;

export type Language = keyof typeof LANGUAGELIST;

// 2. 命名空间
export const NAMESPACES = ['common', 'components', 'pages'] as const;

// 3. 存储相关
const LANGUAGE_STORE_KEY = 'app.language';

// 4. 语言相关的方法
export const languageManager = {
    // 获取存储的语言设置
    async getStoredLanguage(): Promise<Language> {
        const response = await window.customApi.store.get<Language>(
            LANGUAGE_STORE_KEY,
            'zh' // 默认语言
        );
        if (!response.success) {
            logger.error('getStoredLanguage error: ', response.error, {
                component: './src/renderer/src/locales/config.ts'
            });

            return 'zh';
        }
        return response.data as Language;
    },

    // 保存语言设置
    async setStoredLanguage(lang: Language): Promise<void> {
        const response = await window.customApi.store.set(LANGUAGE_STORE_KEY, lang);
        if (!response.success) {
            logger.error('setStoredLanguage error: ', response.error, {
                component: './src/renderer/src/locales/config.ts'
            });
        }
    },

    // 获取当前语言
    getCurrentLanguage(): Language {
        return (i18n.language || 'zh') as Language;
    },

    // 获取支持的语言列表
    getSupportedLanguages() {
        return LANGUAGELIST;
    },

    // 切换语言
    async changeLanguage(lang: Language): Promise<void> {
        await i18n.changeLanguage(lang);
        await this.setStoredLanguage(lang);
    }
};

// 5. 初始化 i18n
export const setupI18n = async () => {
    console.log('setupI18n');
    try {
        const savedLang = await languageManager.getStoredLanguage();
        console.log('Saved language:', savedLang);
        console.log('Loading resources:', { zh: zhCN, en: enUS });

        return i18n.use(initReactI18next).init({
            resources: {
                zh: zhCN,
                en: enUS
            },
            lng: savedLang,
            fallbackLng: 'zh',
            defaultNS: 'common',
            ns: NAMESPACES,
            interpolation: {
                escapeValue: false
            }
        });
    } catch (error) {
        logger.error('i18n setup failed:', error, {
            component: './src/renderer/src/locales/config.ts'
        });

        throw error;
    }
};
