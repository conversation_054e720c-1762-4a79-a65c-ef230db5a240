const zhCN = {
    // 通用翻译
    common: {
        company: '科华（西安）生物工程有限公司',
        appName: '科华酶免ST系列系统软件',
        about: '关于...',

        mainWindow: {
            tabs: {
                home: '主页',
                elisaControl: '酶标仪控制',
                resultData: '结果数据'
            }
        },
        commonDefines: {
            qualitative: '定性',
            quantitative: '定量',
            continuous: '连续',
            step: '步进',

            sampleType: {
                normal: '普通标本',
                blank: '空白',
                negativeControl: '阴性对照',
                weakPositive: '弱阳性',
                positiveControl: '阳性对照',
                standard: '标准品',
                qualityControl: '质控品'
            },
            deviceNames: {
                unknown: '未知设备',
                st360: 'ST-360 酶标仪',
                st960: 'ST-960 酶标仪'
            },
            deviceTypes: {
                unknown: '未知设备',
                reader: '酶标仪'
            }
        },
        button: {
            entry: '进入...',
            save: '保存',
            exit: '退出',
            cancel: '取消',
            confirm: '确认',
            delete: '删除',
            edit: '编辑',
            add: '添加',
            start: '开始',
            stop: '停止',
            reset: '重置',
            next: '下一步',
            previous: '上一步',
            finish: '完成',
            close: '关闭',
            select: '选择',
            query: '查询',
            export: '导出',
            print: '打印',
            preview: '预览'
        },
        label: {
            version: '版本号：',
            positive: '阳性',
            negative: '阴性',
            grayZone: '灰区',
            undefined: '未定义',
            quantitative: '定量',
            qualitative: '定性',
            linear: '线性',
            polynomial: '多项式',
            log_log: '双对数',
            logistic_3p: 'Logistic(3P)',
            logistic_4p: 'Logistic(4P)',
            logistic_5p: 'Logistic(5P)',
            cubicSpline: '三次样条'
        },
        message: {
            saveSuccess: '保存成功',
            deleteSuccess: '删除成功',
            updateSuccess: '更新成功',
            createSuccess: '创建成功',
            uploadSuccess: '上传成功',
            downloadSuccess: '下载成功',
            importSuccess: '导入成功',
            exportSuccess: '导出成功',

            saveFailed: '保存失败',
            deleteFailed: '删除失败',
            updateFailed: '更新失败',
            createFailed: '创建失败',

            uploadFailed: '上传失败',
            downloadFailed: '下载失败',
            importFailed: '导入失败',
            exportFailed: '导出失败',

            confirm: {
                delete: '确定要删除吗？',
                cancel: '确定要取消吗？',
                discard: '确定要放弃更改吗？'
            },
            loading: {
                saving: '保存中...',
                loading: '加载中...',
                processing: '处理中...',
                uploading: '上传中...',
                downloading: '下载中...'
            }
        },
        form: {
            required: '此字段为必填项',
            invalid_format: '格式无效',
            min_length: '最小长度为 {{length}} 个字符',
            max_length: '最大长度为 {{length}} 个字符',
            min_value: '最小值为 {{value}}',
            max_value: '最大值为 {{value}}',
            invalid_email: '邮箱地址无效',
            invalid_date: '日期格式无效',
            invalid_time: '时间格式无效',
            invalid_number: '数字格式无效',
            passwords_not_match: '密码不匹配',
            select_option: '请选择一个选项',
            file_required: '请选择文件',
            file_type_error: '文件类型无效',
            file_size_error: '文件大小超出限制'
        }
    },

    // 组件翻译
    components: {
        languageSwitch: {
            toast: {
                title: '语言切换',
                success: {
                    description: '语言切换成功'
                },
                error: {
                    description: '语言切换失败,请稍后重试'
                }
            }
        },
        detectionInfoForm: {
            title: '检测信息',
            fields: {
                mtpNumber: {
                    label: '微板编号',
                    placeholder: '请输入微板编号'
                },
                reagentSupplier: {
                    label: '试剂厂商',
                    placeholder: '请选择试剂厂商'
                },
                reagentBatch: {
                    label: '试剂批号',
                    placeholder: '请输入试剂批号'
                },
                reagentExpiry: {
                    label: '试剂效期',
                    placeholder: '请选择试剂效期'
                },
                testTemperature: {
                    label: '温度',
                    placeholder: '请输入检测温度',
                    unit: '℃'
                },
                testRelativeHumidity: {
                    label: '相对湿度',
                    placeholder: '请输入相对湿度',
                    unit: '%'
                },
                sampleSource: {
                    label: '样品来源',
                    placeholder: '请输入样品来源'
                },
                sampleStatus: {
                    label: '样品状态',
                    placeholder: '请输入样品状态'
                }
            },
            messages: {
                projectChanged: '项目变更，更新微板编号:',
                testAdditionalInfo: '检测附加信息'
            }
        },
        autoNumberPanel: {
            title: '样本编号设置', // 样本编号设置
            fields: {
                startWell: {
                    label: '起始孔位', // 起始孔位
                    placeholder: '选择起始孔位' // 选择起始孔位
                },
                startNumber: {
                    label: '起始编号', // 起始编号
                    placeholder: '请输入起始编号' // 请输入起始编号
                },
                sampleCount: {
                    label: '样本数量', // 样本数量
                    placeholder: '请输入样本数量' // 请输入样本数量
                },
                direction: {
                    label: '编号方向', // 编号方向
                    vertical: '纵向编号', // 纵向编号
                    horizontal: '横向编号' // 横向编号
                },
                reverse: {
                    label: '逆序编号' // 逆序编号
                }
            },
            buttons: {
                autoNumber: '自动编号', // 自动编号
                clearNumbering: '清除编号' // 清除编号
            },
            messages: {
                selectStartWell: '请先选择起始孔位', // 请先选择起始孔位
                selectedWells: '已选择孔位', // 已选择孔位
                noSelectedWells: '未选择孔位' // 未选择孔位
            }
        },
        elisaPlateForm: {
            title: '酶标板布局', // 酶标板布局
            messages: {
                selectProject: '请选择项目', // 请选择项目
                multiProject: '多项目', // 多项目
                clickToSetProject: '点击设置项目', // 点击设置项目
                wellInfo: '孔位信息', // 孔位信息
                sampleType: '样本类型', // 样本类型
                sampleNumber: '样本编号', // 样本编号
                mainWavelengthOD: '主波长OD值', // 主波长OD值
                refWavelengthOD: '参考波长OD值', // 参考波长OD值
                wellPosition: '孔位' // 孔位
            }
        },
        sampleTypeForm: {
            title: '标本类型',

            validation: {
                type_required: '样本类型为必填项',
                volume_required: '样本体积为必填项',
                volume_range: '体积必须在 {{min}} 和 {{max}} μL 之间',
                dilution_required: '稀释比例为必填项',
                dilution_format: '无效的稀释格式'
            }
        },
        templateModal: {
            title: {
                new: '新建模板',
                edit: '编辑模板',
                save: '保存模板',
                load: '加载模板'
            },
            fields: {
                name: {
                    label: '模板名称',
                    placeholder: '请输入模板名称'
                },
                description: {
                    label: '描述',
                    placeholder: '请输入模板描述'
                },
                category: {
                    label: '分类',
                    placeholder: '请选择模板分类'
                },
                type: {
                    label: '检测类型',
                    options: {
                        qualitative: '定性',
                        quantitative: '定量',
                        semi_quantitative: '半定量'
                    }
                }
            },
            list: {
                title: '模板列表',
                empty: '暂无模板',
                search: '搜索模板',
                filter: '按分类筛选'
            },
            actions: {
                create: '创建模板',
                save: '保存模板',
                delete: '删除模板',
                load: '加载模板',
                cancel: '取消'
            },
            messages: {
                save_success: '模板保存成功',
                save_error: '模板保存失败',
                delete_confirm: '确定要删除此模板吗？',
                delete_success: '模板删除成功',
                delete_error: '模板删除失败',
                load_error: '模板加载失败'
            },
            validation: {
                name_required: '模板名称为必填项',
                name_exists: '模板名称已存在',
                category_required: '分类为必填项',
                type_required: '检测类型为必填项'
            }
        },
        infoCard: {
            device: '设备信息',
            project: '项目信息',
            status: '状态',
            serialNumber: '序列号'
        },
        projectInfoForm: {
            title: '项目信息',
            layoutType: {
                singleProject: '单项目布局',
                multiProjectHorizontal: '多项目水平布局',
                multiProjectVertical: '多项目垂直布局'
            },
            useCommonBlank: '共用空白',
            selectProject: '请选择项目',
            fields: {
                project_name: {
                    label: '项目名称',
                    placeholder: '请输入项目名称'
                },
                project_code: {
                    label: '项目代码',
                    placeholder: '请输入项目代码'
                },
                test_wavelength: {
                    label: '测试波长',
                    unit: 'nm'
                },
                ref_wavelength: {
                    label: '参考波长',
                    unit: 'nm'
                },
                positive_judgment: '阳性判断',
                description: {
                    label: '项目描述',
                    placeholder: '请输入项目描述'
                },
                operator: {
                    label: '操作员',
                    placeholder: '请输入操作员姓名'
                },
                date: {
                    label: '检测日期',
                    placeholder: '请选择检测日期'
                },
                department: {
                    label: '部门',
                    placeholder: '请选择部门'
                }
            },
            validation: {
                project_name: {
                    required: '项目名称为必填项',
                    min_length: '项目名称至少需要 {{min}} 个字符',
                    max_length: '项目名称不能超过 {{max}} 个字符'
                },
                project_code: {
                    required: '项目代码为必填项',
                    format: '项目代码格式无效'
                },
                operator: {
                    required: '操作员姓名为必填项'
                },
                date: {
                    required: '检测日期为必填项',
                    invalid: '日期格式无效'
                }
            },
            toast: {
                loadFailed: {
                    title: '加载项目列表失败',
                    description: '数据库操作失败',
                    systemError: '系统错误，请重试'
                }
            },
            messages: {
                clearProjectSelection: '清空项目选择',
                selectProject: '选择检测项目：'
            }
        },
        ReportPara: {
            title: '报告参数',

            reportMainTitile: '报告单主标题',
            reportMainTitile_placeholder: '请输入报告单主标题',
            reportSubTitile: '报告单副标题',
            reportSubTitile_placeholder: '请输入报告单副标题',
            testMethod: '检测方法',
            testMethod_placeholder: '请输入检测方法',
            testBasis: '检测依据',
            testBasis_placeholder: '请输入检测依据',
            reviewer: '审核员',
            reviewer_placeholder: '请输入审核员姓名',

            toast: {
                success: {
                    title: '保存成功',
                    description: '已更新报告参数'
                },
                error: {
                    title: '保存失败',
                    description: '报告参数保存失败，请重试'
                }
            }
        },
        reagentSupplier: {
            title: '试剂供应商',
            placeholder: '请输入供应商名称',
            toast: {
                success: {
                    title: '保存成功',
                    description: '已更新供应商列表'
                },
                error: {
                    title: '保存失败',
                    description: '保存供应商列表时发生错误'
                }
            }
        },
        lisPara: {
            title: 'LIS参数设置',
            defaultPath: 'LIS文件默认保存路径',
            selectPath: '选择LIS保存路径',
            placeholder: '请选择LIS文件保存路径',
            toast: {
                success: {
                    title: '保存成功',
                    description: '已更新LIS文件保存路径'
                },
                error: {
                    title: '保存失败',
                    description: '保存LIS路径时发生错误'
                },
                noSelection: {
                    title: '未选择路径',
                    description: '请选择LIS文件保存路径'
                }
            }
        },
        formulaEditor: {
            title: '公式编辑器',
            validation: {
                correct: '公式格式正确',
                error: '公式语法错误',
                success: '公式语法正确',
                testSuccess: '公式测试成功',
                testFailed: '公式测试失败',
                enterFormula: '请先输入公式',
                syntaxError: '语法错误:',
                unknownError: '未知错误',
                missingOperator: '之间缺少运算符',
                functionUsageTips: '语法错误: 统计函数用法提示：\n• 单个数组: max(NC1) 或 min(NC1)\n• 比较均值: max(mean(NC1), mean(NC2))\n• 所有值: max(flatten([NC1, NC2]))\n不支持: max(NC1, NC2) 等多数组参数'
            },
            buttons: {
                validate: '校验公式',
                test: '测试公式'
            },
            panels: {
                functions: '函数和运算符',
                variables: '变量'
            },
            cursorPosition: '位置',
            testResult: '测试结果',
            closeResult: '点击关闭测试结果',
            messages: {
                calculationResult: '计算结果:',
                simulationData: '模拟数据:',
                formula: '公式:',
                functionAnd: '函数',
                and: '和'
            },
            functions: {
                mean: {
                    description: '平均值函数',
                    category: '统计函数'
                },
                max: {
                    description: '最大值函数',
                    category: '统计函数'
                },
                min: {
                    description: '最小值函数',
                    category: '统计函数'
                },
                sum: {
                    description: '求和函数',
                    category: '统计函数'
                }
            }
        },
        recordQueryForm: {
            title: '记录查询',
            startDate: '开始日期',
            endDate: '结束日期',
            mtpNumber: '微板编号',
            startDate_title: '请输入开始日期',
            endDate_title: '请输入结束日期',
            mtpNumber_placeholder: '微板编号（支持模糊查询）',
            mtpNumber_title: '支持模糊查询：输入开始或结束的部分字符...'
        },
        DeleteConfirmDialog: {
            title: '删除确认',
            delete_ing: '删除中...',
            description_warn: '此操作不可撤销，请谨慎操作!',
            delete_confirm: '确认删除',
            delete_cancel: '取消',
            delete_question: '吗？',
            delete_confirm_description: '您确定要删除'
        }
    },

    // 页面翻译
    pages: {
        home: {
            infoCard: {
                device: {
                    title: '设备管理',
                    description: '默认连接设备管理,设备连接参数配置 ...'
                },
                project: {
                    title: '项目管理',
                    description: '检测项目管理（增加、修改、删除），检测参数配置、数据分析方法编辑...'
                },
                report: {
                    title: '报告管理',
                    description: '自定义检测报告，编辑打印信息，导出LIS...'
                }
            }
        },
        elisaControl: {
            title: 'ELISA 控制',

            templateNameModal: {
                title: '请输入模板名称',
                placeholder: '请输入模板名称',
                label_name: '模板名称'
            },
            messages: {
                error: '错误',
                success: '成功',
                warning: '警告',
                info: '提示',
                template: {
                    nameRequired: '请输入模板名称',
                    saveSuccess: '模板保存成功',
                    saveFailed: '保存模板失败',
                    saveError: '保存模板时发生错误',
                    loadSuccess: '模板加载成功',
                    loadFailed: '加载模板失败',
                    loadError: '加载模板时发生错误',
                    deleteSuccess: '模板删除成功',
                    deleteFailed: '删除模板失败',
                    deleteError: '删除模板时发生错误',
                    selectFirst: '请先选择要删除的模板',
                    listLoadFailed: '加载模板列表失败',
                    listLoadError: '加载模板列表时发生错误'
                },
                serial: {
                    configFailed: '获取串口配置失败',
                    configRequired: '请先配置串口参数',
                    openFailed: '打开串口失败',
                    opened: '串口已打开，发送读板命令...',
                    sendFailed: '发送读板命令失败',
                    receiving: '正在接收数据...',
                    dataIncomplete: '接收到的数据不完整，可能缺少部分酶标板数据',
                    noData: '未接收到数据',
                    readError: '读板过程中发生错误'
                },
                plate: {
                    readComplete: '读板完成',
                    dataSaved: '数据已保存完成，请到结果页面查看数据...',
                    saveFailed: '保存读板数据失败'
                }
            },
            buttons: {
                readPlate: '读板',
                reading: '读板中...',
                clearTemplate: '清空模板',
                saveAsTemplate: '另存为模板',
                selectTemplate: '选择模板',
                deleteTemplate: '删除模板',
                deleteSelectedTemplate: '删除选中的模板'
            },
            dialogs: {
                deleteTemplate: {
                    description: '删除模板后，该模板的所有配置信息将被永久清除'
                }
            },
            generateReadCommand: {
                toast: {
                    error: {
                        title: '错误',
                        description_project: '项目',
                        description_missingInfo: '缺少波长配置信息',
                        description_noProject: '没有分配任何项目',
                        description_noProject_single: '请先选择项目（单项目）',
                        description_noProject_multi: '请先分配项目（多项目）',
                        description_testWave: '的检测波长',
                        description_refWave: '的参考波长',
                        description_refWave_other: '其他项目不一致，不能读板'
                    }
                }
            }
        },
        resultData: {
            title: '检测结果',
            filter: {
                title: '筛选结果',
                date_range: {
                    label: '日期范围',
                    start: '开始日期',
                    end: '结束日期'
                },
                project: {
                    label: '项目',
                    placeholder: '选择项目'
                },
                operator: {
                    label: '操作员',
                    placeholder: '选择操作员'
                },
                status: {
                    label: '状态',
                    all: '全部',
                    completed: '已完成',
                    processing: '处理中',
                    error: '错误'
                }
            },
            table: {
                columns: {
                    id: '编号',
                    mtpNumber: '微板编号',
                    project_name: '项目名称',
                    date: '检测日期'
                },
                empty: '暂无数据，请点击查询或重置按钮加载数据',
                noMatch: '未找到匹配的记录',
                loading: '加载中...'
            },
            details: {
                title: '结果详情',
                currentPlate: '当前板号',
                projectName: '项目名称',
                projectCode: '项目代码',
                mainWavelength: '主波长',
                refWavelength: '参考波长',
                positiveFormula: '阳性公式',
                cutoffValue: 'Cutoff值',
                reagentBatch: '试剂批号',
                reagentSupplier: '试剂供应商',
                resultType: {
                    result: '结果值',
                    original: '原始吸光值'
                },
                export: '导出结果',
                print: '打印报告',
                delete: '删除结果'
            },
            wellTooltip: {
                wellId: '孔位ID',
                sampleType: '样本类型',
                sampleNumber: '样本编号',
                odMain: '主波长',
                odRef: '参考波长',
                result: '检测结果',
                concentration: '浓度值'
            },
            buttons: {
                printPreview: '打印预览',
                printReport: '打印报告',
                exportPdf: '导出PDF',
                exportExcel: '导出Excel',
                exportCsv: '导出CSV',
                exportLis: '导出LIS',
                cutoffChart: 'Cutoff图',
                deleteData: '删除此板数据'
            },
            loadingText: {
                generating: '生成中...',
                printing: '打印中...',
                exporting: '导出中...'
            },
            messages: {
                selectRecord: '请先选择一条记录',
                queryFailed: '查询失败',
                queryError: '查询检测记录失败',
                printPreviewFailed: '打印预览失败',
                printFailed: '打印失败',
                printSuccess: '打印成功',
                generatePdfFailed: 'PDF生成失败',
                generatePdfSuccess: 'PDF生成成功',
                exportPdfFailed: 'PDF导出失败',
                exportExcelFailed: 'Excel导出失败',
                exportExcelSuccess: 'Excel导出成功',
                exportLisFailed: 'LIS导出失败',
                exportLisSuccess: 'LIS导出成功',
                exportCsvFailed: 'CSV导出失败',
                exportCsvSuccess: 'CSV导出成功',
                exportLisSuccessDescription: '文件已保存到:',
                deleteSuccess: '删除成功',
                deleteFailed: '删除失败',
                noDataInRecord: '当前记录没有检测数据',
                printPreviewOpened: '打印预览已打开',
                printPreviewOpenFailed: '打印预览打开失败',
                printFailedDescription: '请使用菜单栏"文件"→"🖨️ 打印"或快捷键 Ctrl+P 进行打印',
                unknownError: '未知错误',
                mainWindowNotExist: '主窗口不存在'
            },
            chart: {
                title: ' Cutoff 图',
                microplateNumber: '微板编号',
                totalSamples: '总样本数',
                positiveSamples: '阳性样本',
                negativeSamples: '阴性样本',
                grayZoneSamples: '灰区样本',
                tooltip: {
                    wellId: '孔位ID',
                    sample: '样本',
                    type: '类型',
                    odValue: 'OD值',
                    result: '结果'
                }
            }
        },
        deviceManager: {
            title: '设备管理',
            device: {
                title: '连接设置',
                defaultDevice: '默认仪器',
                port: {
                    label: '串口',
                    placeholder: '请选择串口'
                },
                baudRate: {
                    label: '波特率',
                    placeholder: '请选择波特率'
                },
                dataBits: {
                    label: '数据位',
                    placeholder: '请选择数据位'
                },
                stopBits: {
                    label: '停止位',
                    placeholder: '请选择停止位'
                },
                parity: {
                    label: '校验位',
                    placeholder: '请选择校验位'
                },
                flowControl: {
                    label: '流控制',
                    placeholder: '请选择流控制'
                },
                timeout: {
                    label: '通讯超时(秒)',
                    unit: '（秒）',
                    placeholder: '请选择通讯超时'
                }
            },

            filter: {
                title: '滤光片参数',
                filterNo: '滤光片号',
                wavelength: '波长(nm)'
            },
            toast: {
                title_notEmpty_device: '设备型号不能为空',
                title_notEmpty_port: '通信端口不能为空',
                title_saveConfigFailed: '保存设备设置失败',

                success: {
                    title: '保存成功'
                },
                error: {
                    title: '保存失败'
                }
            }
        },
        projectManager: {
            title: '项目管理',
            search_placeholder: '搜索  [项目名称/代码]',
            isLoadingProjects: ' 正在加载项目列表...',
            emptyProjects: '无匹配项目',
            label_deletePro: '删除项目',
            label_addPro: '新增项目',
            type: '类型',
            testWave: '检测波长',
            refWave: '参考波长',
            placeholder: '必填,唯一',
            placeholder_unique: '唯一',
            placeholder_required: '必填',
            pleaseSelect: '请选择或新增一个项目',

            label: {
                title: '项目参数',
                ProjectName: '项目名称',
                ProjectCode: '项目代码',
                resultShow: '结果显示',
                resultUnit: '结果单位',
                refDown: '参考下限',
                refUp: '参考上限',
                refRange: '参考范围'
            },
            para: {
                title: '检验参数',
                projectType: '项目类型',
                blank: '空白校正',
                xAxis: 'x 轴',
                yAxis: 'y 轴',
                enteryMode: '进板模式',
                shakeTime: '振板时间',
                negativeControl: '阴性对照',
                positiveControl: '阳性对照',
                rightTxtUp: '(上限)',
                rightTxtDown: '(下限)',
                rightTxtSec: '(秒)',
                cutoffFormula: 'Cutoff =',
                positiveJudge: '阳性判定',
                grayZone: '启用灰区',
                grayZoneDown: '灰区下限',
                grayZoneUp: '灰区上限'
            },
            toast: {
                success: {
                    title: '保存成功',
                    title_delete: '删除成功',
                    description: '已更新项目参数',
                    description_delete: '已删除项目'
                },
                error: {
                    title: '保存失败',
                    title_input: '输入错误',
                    title_delete: '删除失败',
                    title_notEmpty: '项目名称和代码不能为空',
                    title_projectList: '加载项目列表失败',
                    title_device: '设备配置加载失败',
                    description_system: '系统错误，请重试',
                    description_db: '数据库操作失败，请重试',
                    description_exist: '已经存在，请修改后重试',
                    description_input: '[下限] 不能大于 [上限]值'
                }
            }
        },
        ReportManager: {
            title: '报告管理'
        }
    }
} as const;

export default zhCN;
