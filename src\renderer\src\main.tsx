import './assets/fonts.css'; // 引入小米字体 CSS
import './assets/main.css';

import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { setupI18n } from './i18n';
import { ChakraProvider } from '@chakra-ui/react';
import logger from './utils/logger';

const renderApp = async () => {
    try {
        await setupI18n();

        const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
        root.render(
            <React.StrictMode>
                <ChakraProvider>
                    <App />
                </ChakraProvider>
            </React.StrictMode>
        );
    } catch (error) {
        logger.error('i18n setup failed:', error, { component: './src/renderer/src/main.tsx' });

        throw error;
    }
};

renderApp();
