import { useState, useEffect } from 'react';
// import { DeviceConfig, devices, UNSET_DEVICE } from '@renderer/config/devices';
import { DeviceConfig, PortsList, SerialPortOptions } from '@shared/types';
import { DeviceModel } from '@shared/commondefines';
import { useDeviceData } from '@renderer/hooks/useDeviceData';
import logger from '@renderer/utils/logger';

// 3. 存储相关 key
const DEFAULT_DEVICE_STORE_KEY = 'app.defaultDevice';
const SERIAL_CONFIG_STORE_KEY = 'app.serialConfig';
// const SERIAL_PATH_STORE_KEY = 'app.serialPath';

interface DeviceSettingsReturn {
    defaultDevice: DeviceModel;
    devicesList: DeviceConfig[];
    portsList: PortsList[];
    serialConfig: SerialPortOptions;
    isLoading: boolean;
    error: string | null;

    onDefaultDeviceChange: (model: DeviceModel) => Promise<void>;
    onSerialConfigChange: (info: Partial<SerialPortOptions>) => Promise<void>;

    saveDeviceSettings: () => Promise<boolean>;
}

export const useDeviceSettings = (isOpen: boolean): DeviceSettingsReturn => {
    const [defaultDevice, setDefaultDevice] = useState<DeviceModel>(DeviceModel.unknown);
    const [portsList, setPortsList] = useState<PortsList[]>([]);
    const [serialConfig, setSerialConfig] = useState<SerialPortOptions>({
        path: '',
        baudRate: 4800,
        dataBits: 8,
        stopBits: 1,
        parity: 'none',
        timeout: 20
    });

    // 使用共享的设备数据 Hook
    const { devicesList, isLoading, error } = useDeviceData({
        defaultDevice,
        shouldLoad: isOpen
    });

    const saveDeviceSettings = async (): Promise<boolean> => {
        try {
            const [response_defaultDevice, response_serialConfig] = await Promise.all([
                window.customApi.store.set(DEFAULT_DEVICE_STORE_KEY, defaultDevice),
                window.customApi.store.set(SERIAL_CONFIG_STORE_KEY, serialConfig)
            ]);
            if (response_defaultDevice.success && response_serialConfig.success) {
                return true;
            }
            if (!response_defaultDevice.success) {
                logger.error('保存默认设备失败', response_defaultDevice.error, {
                    component: './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts'
                });
            }
            if (!response_serialConfig.success) {
                logger.error('保存串口配置失败', response_serialConfig.error, {
                    component: './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts'
                });
            }
            return false;
        } catch (error) {
            logger.error('设备设置保存失败', error, {
                component: './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts'
            });
            return false;
        }
    };

    const onDefaultDeviceChange = async (model: DeviceModel): Promise<void> => {
        setDefaultDevice(model);
        // 设备变化时，共享的 Hook 会自动重新加载设备列表
    };

    const onSerialConfigChange = async (info: Partial<SerialPortOptions>): Promise<void> => {
        setSerialConfig((prev) => ({ ...prev, ...info }));
    };

    useEffect(() => {
        if (isOpen) {
            const initDevice = async (): Promise<void> => {
                try {
                    logger.info(
                        '--------------------------------------------------------------------------------------',
                        {
                            component:
                                './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts'
                        }
                    );

                    // 获取串口号信息
                    const portsInfo = await window.customApi.serial.getSerialPorts();
                    logger.info('portsInfo', {
                        component:
                            './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts',
                        data: portsInfo
                    });

                    // 获取默认设备信息
                    const response_defaultDevice = await window.customApi.store.get<DeviceModel>(
                        DEFAULT_DEVICE_STORE_KEY,
                        DeviceModel.unknown
                    );

                    logger.info('1--> response_defaultDevice:', {
                        component:
                            './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts',
                        data: { response_defaultDevice }
                    });

                    let defaultDev: DeviceModel = response_defaultDevice.data as DeviceModel;

                    if (!response_defaultDevice.success) {
                        logger.error('获取默认设备信息失败', response_defaultDevice.error, {
                            component:
                                './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts'
                        });
                        defaultDev = DeviceModel.unknown;
                    }

                    // 等待设备列表加载完成后再验证默认设备
                    // 注意：这里不直接使用 devicesList，因为它可能还在加载中
                    // 设备验证逻辑将在 devicesList 更新后的 useEffect 中处理

                    logger.info('2--> defaultDevice:' + defaultDev, {
                        component:
                            './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts'
                    });

                    // 获取串口配置信息
                    const response_serialConfig =
                        await window.customApi.store.get<SerialPortOptions>(
                            SERIAL_CONFIG_STORE_KEY,
                            {
                                path: '',
                                baudRate: 4800,
                                dataBits: 8,
                                stopBits: 1,
                                parity: 'none',
                                timeout: 20
                            }
                        );

                    let serialCfg: SerialPortOptions =
                        response_serialConfig.data as SerialPortOptions;

                    if (!response_serialConfig.success) {
                        logger.error('获取串口配置失败', response_serialConfig.error, {
                            component:
                                './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts'
                        });
                    }

                    if (portsInfo.length > 0 && serialCfg.path === '') {
                        serialCfg = { ...serialCfg, path: portsInfo[0].path };
                    }

                    logger.info('serialPath:' + serialCfg.path, {
                        component:
                            './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts'
                    });

                    setPortsList(portsInfo as PortsList[]);
                    setDefaultDevice(defaultDev as DeviceModel);
                    setSerialConfig(serialCfg as SerialPortOptions);

                    logger.info(
                        '--------------------------------------------------------------------------------------',
                        {
                            component:
                                './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts'
                        }
                    );
                } catch (error) {
                    logger.error('初始化设备失败', error, {
                        component:
                            './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts'
                    });
                }
            };
            initDevice();
        }
    }, [isOpen]);

    // 当设备列表加载完成后，验证并调整默认设备
    useEffect(() => {
        if (devicesList.length === 0) return; // 设备列表为空时不处理

        // 如果当前默认设备在列表中存在，不需要调整
        if (defaultDevice !== DeviceModel.unknown) {
            const device = devicesList.find((d) => d.model === defaultDevice);
            if (device) {
                return; // 当前默认设备有效，无需调整
            }
        }

        // 只有在默认设备无效或未知时，才设置第一个设备为默认设备
        const newDefaultDevice = devicesList[0].model as DeviceModel;
        if (newDefaultDevice !== defaultDevice) {
            logger.info('modify default device: ' + defaultDevice + ' -> ' + newDefaultDevice, {
                component: './src/renderer/src/pages/1_1_deviceMgr/hooks/useDeviceSettings.ts'
            });
            setDefaultDevice(newDefaultDevice);
        }
    }, [devicesList]); // 只依赖 devicesList，避免 defaultDevice 变化时重复执行

    return {
        defaultDevice,
        devicesList,
        portsList,
        serialConfig,
        isLoading,
        error,
        onDefaultDeviceChange,
        onSerialConfigChange,
        saveDeviceSettings
    };
};
