import React, { useState } from 'react';
import {
    Modal,
    ModalOverlay,
    ModalContent,
    ModalHeader,
    ModalBody,
    Button,
    HStack,
    VStack,
    Box,
    List,
    ListItem,
    IconButton,
    Input,
    useToast,
    Icon,
    Text,
    FormControl,
    FormLabel,
    Divider,
    SimpleGrid,
    InputGroup,
    InputLeftElement,
    Tooltip,
    Spacer,
    Flex,
    ModalCloseButton,
    useDisclosure,
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    NumberInput,
    NumberInputField
} from '@chakra-ui/react';

import { AddIcon, SearchIcon, DeleteIcon, InfoIcon } from '@chakra-ui/icons';
import FormulaInputField from '@renderer/components/FormulaInputField';
import { TextFormField, NumberFormField, SelectFormField, CheckboxFormField } from '@renderer/components/FormFields';
import { useDeviceFilters } from '@renderer/hooks/useDeviceFilters';
import DeleteConfirmDialog from '@renderer/components/DeleteConfirmDialog';

import { styles } from '@renderer/utils/theme';
import { useTranslation } from 'react-i18next';
import logger from '../../utils/logger';

import {
    Project,
    projectTestType,
    projectBlankCorrection,
    mtpEnteryMode,
    quantitativeAxisType,
    projectPositiveJudge,
    ProjectUtils,
    TestType,
    ResultShow,
    BlankCorrection,
    EnteryMode,
    AxisType,
    PositiveJudge,
    getTestTypeName,
    fittingModelType,
    getFittingModelTypeLabel,
    FittingModel,
    getFittingModelFormula
} from '@shared/types';

import { useTranslatedOptions } from '@renderer/hooks/useTranslatedOptions';
import { px } from 'framer-motion';

interface ProjectManagerProps {
    isOpen: boolean;
    onClose: () => void;
}

const ProjectManager: React.FC<ProjectManagerProps> = ({ isOpen, onClose }) => {
    const selectedAction = {
        borderWidth: '1px',
        borderColor: 'blue.400',
        borderLeftWidth: '10px',
        borderLeftColor: 'blue.500'
    } as const;

    const hoverAction = {
        borderWidth: '1px',
        borderColor: 'blue.400'
    } as const;

    const toast = useToast();
    const { t } = useTranslation(['common', 'pages', 'components']);
    const { translateOptions } = useTranslatedOptions();

    // 使用通用的设备滤光片Hook
    const { filters, defaultDevice, isLoading, error } = useDeviceFilters(isOpen);

    // 如果有加载错误，显示提示
    React.useEffect(() => {
        if (error) {
            toast({
                title: t('pages:projectManager.toast.error.title_device'),
                description: error,
                status: 'warning',
                duration: 3000,
                isClosable: true,
                position: 'top'
            });
        }
    }, [error, toast]);

    // 调试信息：显示当前默认设备
    React.useEffect(() => {
        if (isOpen && defaultDevice) {
            logger.info('projectMgr- current defualt device:' + defaultDevice + ' available filters:' + filters.length);
        }
    }, [isOpen, defaultDevice, filters.length]);

    const [projects, setProjects] = useState<Project[]>([]);
    const [selectedId, setSelectedId] = useState<string | null>(null);
    const [editProject, setEditProject] = useState<Project | null>(null);
    const [search, setSearch] = useState('');
    const [isLoadingProjects, setIsLoadingProjects] = useState(false);
    const [stdConcentrationWarningShown, setStdConcentrationWarningShown] = useState(false);

    // 从数据库加载项目列表
    const loadProjects = async () => {
        if (!isOpen) return; // 只有在对话框打开时才加载

        setIsLoadingProjects(true);
        try {
            const result = await window.customApi.configInfo.getProjectList();
            if (result.success && result.data) {
                setProjects(result.data);
                // 如果有项目数据，默认选中第一个
                if (result.data.length > 0) {
                    const firstProject = result.data[0];
                    setSelectedId(firstProject.id);
                    setEditProject(firstProject);
                }
            } else {
                logger.error('Failed to load project list:', result.error, {
                    component: './src/renderer/src/pages/1_2_projectMgr/index.tsx'
                });

                toast({
                    title: t('pages:projectManager.toast.error.title_projectList'),
                    description: result.error || t('pages:projectManager.toast.error.description_db'),
                    status: 'error',
                    duration: 3000,
                    position: 'top',
                    isClosable: true
                });
            }
        } catch (error) {
            logger.error('Failed to load project list:', error, {
                component: './src/renderer/src/pages/1_2_projectMgr/index.tsx'
            });

            toast({
                title: t('pages:projectManager.toast.error.title_projectList'),
                description: t('pages:projectManager.toast.error.description_system'),
                status: 'error',
                duration: 3000,
                position: 'top',
                isClosable: true
            });
        } finally {
            setIsLoadingProjects(false);
        }
    };

    // 在对话框打开时加载项目数据
    React.useEffect(() => {
        loadProjects();
    }, [isOpen]);

    // 删除确认对话框状态
    const { isOpen: isDeleteDialogOpen, onOpen: onDeleteDialogOpen, onClose: onDeleteDialogClose } = useDisclosure();
    const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);

    // 选中项目
    const handleSelect = (id: string) => {
        setSelectedId(id);
        const proj = projects.find((p) => p.id === id) || null;
        setEditProject(proj);
        setStdConcentrationWarningShown(false); // 重置警告状态
    };

    // 新增项目
    const handleAdd = () => {
        const defaultProject = ProjectUtils.createDefault();
        setEditProject({
            ...defaultProject,
            id: Date.now().toString() // 临时 ID，保存到数据库时会跳过此字段
        } as Project);
        setSelectedId(null);
        setStdConcentrationWarningShown(false); // 重置警告状态
    };

    // 显示删除确认对话框
    const handleDelete = (id: string) => {
        const projectToDelete = projects.find((p) => p.id === id);
        if (projectToDelete) {
            setProjectToDelete(projectToDelete);
            onDeleteDialogOpen();
        }
    };

    // 确认删除项目
    const confirmDelete = async () => {
        if (!projectToDelete) return;

        try {
            // 调用数据库删除 API
            const deleteResult = await window.customApi.configInfo.deleteProject(projectToDelete.id);

            if (deleteResult.success) {
                // 数据库删除成功，更新本地状态
                setProjects((prev) => prev.filter((p) => p.id !== projectToDelete.id));

                // 如果删除的是当前选中的项目，清空编辑状态
                if (selectedId === projectToDelete.id) {
                    setSelectedId(null);
                    setEditProject(null);
                }

                toast({
                    title: t('pages:projectManager.toast.success.title_delete'),
                    position: 'top',
                    description: t('pages:projectManager.label.title') + `"${projectToDelete.name}"` + t('pages:projectManager.toast.success.description_delete'),
                    status: 'success',
                    duration: 3000
                });
            } else {
                // 数据库删除失败
                toast({
                    title: t('pages:projectManager.toast.error.title_delete'),
                    description: deleteResult.error || t('pages:projectManager.toast.error.description_db'),
                    status: 'error',
                    duration: 3000,
                    position: 'top',
                    isClosable: true
                });
            }
        } catch (error) {
            logger.error('Failed to delete project:', error, {
                component: './src/renderer/src/pages/1_2_projectMgr/index.tsx'
            });

            toast({
                title: t('pages:projectManager.toast.error.title_delete'),
                description: t('pages:projectManager.toast.error.description_system'),
                status: 'error',
                duration: 3000,
                position: 'top',
                isClosable: true
            });
        } finally {
            // 无论成功失败都关闭对话框并清理状态
            onDeleteDialogClose();
            setProjectToDelete(null);
        }
    };

    // 保存项目
    const handleSave = async () => {
        if (!editProject) return;
        if (!editProject.code || !editProject.name) {
            toast({
                title: t('pages:projectManager.toast.error.title_notEmpty'),
                position: 'top',
                status: 'warning',
                duration: 3000,
                isClosable: true
            });
            return;
        }

        // 判断是否为编辑模式（项目在现有列表中存在）
        const isEditMode = projects.some((p) => p.id === editProject.id);

        // 只有在新增模式下才需要检查重复性
        if (!isEditMode) {
            // 检查项目名称和代码是否重复
            const duplicateName = projects.find((p) => p.name === editProject.name);
            const duplicateCode = projects.find((p) => p.code === editProject.code);

            if (duplicateName && duplicateCode) {
                toast({
                    title: t('pages:projectManager.toast.error.title'),
                    description:
                        t('pages:projectManager.label.title') + `"${editProject.name}"` + t('pages:projectManager.label.ProjectCode') + `"${editProject.code}"` + t('pages:projectManager.toast.error.description_exist'),
                    status: 'error',
                    duration: 3000,
                    position: 'top',
                    isClosable: true
                });
                return;
            }

            if (duplicateName) {
                toast({
                    title: t('pages:projectManager.toast.error.title'),
                    description: t('pages:projectManager.label.title') + `"${editProject.name}"` + t('pages:projectManager.toast.error.description_exist'),
                    status: 'error',
                    duration: 3000,
                    position: 'top',
                    isClosable: true
                });
                return;
            }

            if (duplicateCode) {
                toast({
                    title: t('pages:projectManager.toast.error.title'),
                    description: t('pages:projectManager.label.ProjectCode') + `"${editProject.code}"` + t('pages:projectManager.toast.error.description_exist'),
                    status: 'error',
                    duration: 3000,
                    position: 'top',
                    isClosable: true
                });
                return;
            }
        }

        // refRangeText 已经在 handleEditChange 中自动更新，不需要手动计算
        const projectToSave = editProject;

        // 确保数字字段的类型正确（前端输入可能是字符串）
        const projectWithCorrectTypes = {
            ...projectToSave,
            testType: projectToSave.testType as TestType,
            resultShow: Number(projectToSave.resultShow) as ResultShow,
            testWaveIdx: Number(projectToSave.testWaveIdx),
            refWaveIdx: Number(projectToSave.refWaveIdx),
            useBlankCorrection: projectToSave.useBlankCorrection as BlankCorrection,
            enteryMode: Number(projectToSave.enteryMode) as EnteryMode,
            shakeTime: Number(projectToSave.shakeTime),
            refRangeDown: Number(projectToSave.refRangeDown),
            refRangeUp: Number(projectToSave.refRangeUp),
            ncRangeDown: Number(projectToSave.ncRangeDown),
            ncRangeUp: Number(projectToSave.ncRangeUp),
            pcRangeDown: Number(projectToSave.pcRangeDown),
            pcRangeUp: Number(projectToSave.pcRangeUp),
            grayRangeDown: Number(projectToSave.grayRangeDown),
            grayRangeUp: Number(projectToSave.grayRangeUp),
            quantitativexAxis: Number(projectToSave.quantitativexAxis) as AxisType,
            quantitativeyAxis: Number(projectToSave.quantitativeyAxis) as AxisType,
            grayEnble: Boolean(projectToSave.grayEnble),
            postiveJudge: String(projectToSave.postiveJudge) as PositiveJudge
        };

        try {
            let result: boolean;
            let savedProject: Project;

            if (isEditMode) {
                // 编辑模式：调用更新 API
                const updateResult = await window.customApi.configInfo.updateProject(projectWithCorrectTypes);
                result = updateResult.success;
                if (result && updateResult.data) {
                    savedProject = updateResult.data;
                } else {
                    savedProject = projectToSave;
                }
            } else {
                // 新增模式：调用添加 API，数据库会自动忽略 id 字段并生成新的 ID
                const addResult = await window.customApi.configInfo.addProject(projectWithCorrectTypes);
                result = addResult.success;

                if (result && addResult.data) {
                    // 添加成功，使用返回的项目数据（包含数据库生成的真实 ID）
                    savedProject = addResult.data;
                } else {
                    savedProject = projectToSave;
                }
            }

            if (result) {
                // 数据库操作成功，更新本地状态
                setProjects((prev) => {
                    if (isEditMode) {
                        // 编辑模式：更新现有项目
                        return prev.map((p) => (p.id === editProject.id ? savedProject : p));
                    } else {
                        // 新增模式：添加新项目
                        return [...prev, savedProject];
                    }
                });

                setSelectedId(savedProject.id);
                setEditProject(savedProject);

                toast({
                    position: 'top',
                    title: t('pages:projectManager.toast.success.title'),
                    status: 'success',
                    duration: 2000
                });

                // 触发全局事件，通知其他页面刷新项目列表
                window.dispatchEvent(new CustomEvent('projectListUpdated'));
            } else {
                // 数据库操作失败
                toast({
                    title: t('pages:projectManager.toast.error.title'),
                    //数据库操作失败，请重试
                    description: t('pages:projectManager.toast.error.description_db'),
                    status: 'error',
                    duration: 3000,
                    position: 'top',
                    isClosable: true
                });
            }
        } catch (error) {
            logger.error('Failed to save project:', error, {
                component: './src/renderer/src/pages/1_2_projectMgr/index.tsx'
            });

            toast({
                title: t('pages:projectManager.toast.error.title'),
                //系统错误，请重试
                description: t('pages:projectManager.toast.error.description_system'),
                status: 'error',
                duration: 3000,
                position: 'top',
                isClosable: true
            });
        }
    };

    // 处理标准浓度变更
    const handleStdConcentrationChange = (index: number, inputValue: string) => {
        const currentStdConcentration = editProject?.stdConcentration || [];

        // 确保数组有10个元素，只在必要时初始化
        const newStdConcentration: [string, number][] = currentStdConcentration.length >= 10 ? [...currentStdConcentration] : Array.from({ length: 10 }, (_, i) => currentStdConcentration[i] || [`STD${i + 1}`, 0]);

        // 直接修改指定位置的元素
        const numValue = inputValue === '' ? 0 : parseFloat(inputValue) || 0;
        newStdConcentration[index] = [`STD${index + 1}`, numValue];

        handleEditChange('stdConcentration', newStdConcentration);
        console.log('newStdConcentration:', newStdConcentration);

        // 检查0到当前index之间是否有浓度值为0的情况
        if (numValue > 0 && !stdConcentrationWarningShown) {
            for (let i = 0; i <= index; i++) {
                if (newStdConcentration[i][1] === 0) {
                    toast({
                        title: '浓度值警告',
                        description: `STD${i + 1} 的浓度值不能为0，请检查并修正中间的浓度值`,
                        status: 'warning',
                        duration: 5000,
                        isClosable: true
                    });
                    setStdConcentrationWarningShown(true); // 标记已显示警告
                    break; // 只显示第一个为0的警告
                }
            }
        }
    };

    // 编辑表单变更（集成校验逻辑）
    const handleEditChange = (field: keyof Project, value: unknown) => {
        setEditProject((prev) => {
            if (!prev) return prev;

            const newProject = { ...prev, [field]: value };

            // 如果修改了参考范围，自动更新 refRangeText
            if (field === 'refRangeDown' || field === 'refRangeUp') {
                newProject.refRangeText = joinRefRange(newProject.refRangeDown, newProject.refRangeUp);
            }

            // 延迟校验，确保状态更新后再检查
            setTimeout(() => {
                // 参考范围校验
                if (field === 'refRangeDown' || field === 'refRangeUp') {
                    validateRange(newProject.refRangeDown, newProject.refRangeUp, t('pages:projectManager.label.refRange'));
                }
                // 阴性对照校验
                else if (field === 'ncRangeDown' || field === 'ncRangeUp') {
                    validateRange(newProject.ncRangeDown, newProject.ncRangeUp, t('pages:projectManager.label.negativeControl'));
                }
                // 阳性对照校验
                else if (field === 'pcRangeDown' || field === 'pcRangeUp') {
                    validateRange(newProject.pcRangeDown, newProject.pcRangeUp, t('pages:projectManager.label.positiveControl'));
                }
                // 灰区校验
                else if (field === 'grayRangeDown' || field === 'grayRangeUp') {
                    validateRange(newProject.grayRangeDown, newProject.grayRangeUp, t('pages:projectManager.label.grayZone'));
                }
            }, 100);

            return newProject;
        });
    };

    // 搜索过滤
    const filteredProjects = projects.filter((p) => p.name.includes(search) || p.code.includes(search));

    const joinRefRange = (down: number, up: number) => {
        return `${down}  ~  ${up}`;
    };

    // 封装的范围校验函数
    const validateRange = (down: number, up: number, fieldName: string) => {
        // 如果任一值为空或无效，不进行校验
        if (isNaN(down) || isNaN(up)) {
            return;
        }

        if (down > up) {
            toast({
                position: 'top',
                isClosable: true,
                title: t('pages:projectManager.toast.error.title_input'),
                description: `${fieldName}` + t('pages:projectManager.toast.error.description_input'),
                status: 'error',
                duration: 5000
            });
        }
    };

    return (
        <>
            <Modal size={'6xl'} isOpen={isOpen} onClose={onClose} isCentered closeOnEsc={false} closeOnOverlayClick={false}>
                <ModalOverlay />
                <ModalContent maxH="85vh" h="80vh" maxW={'75vw'} w={'70vw'}>
                    <ModalHeader sx={styles.modalHeader}>
                        <HStack alignItems="center" gap={6}>
                            <Icon as={InfoIcon} boxSize={6} />
                            {/* 项目管理*/}
                            <span>{t('pages:projectManager.title')}</span>
                        </HStack>
                        <ModalCloseButton color="white" bg="rgba(255,255,255,0.1)" _hover={{ bg: 'rgba(255,255,255,0.2)' }} borderRadius="full" size="lg" />
                    </ModalHeader>
                    <ModalBody p={1} m={0} flex="1" overflow="hidden">
                        <HStack align="stretch" spacing={0} h="100%">
                            {/* 左侧项目列表 */}
                            <Box
                                // border="1px solid blue"
                                borderRight={'1px solid #e0e0e0'}
                                w="38.2%"
                                h="100%"
                                display="flex"
                                flexDirection="column"
                                px={2}
                                py={1}
                                minH={0}
                            >
                                <HStack mb={2} flexShrink={0}>
                                    <InputGroup>
                                        <InputLeftElement pointerEvents="none">
                                            <SearchIcon color="gray.800" />
                                        </InputLeftElement>
                                        <Input placeholder={t('pages:projectManager.search_placeholder')} value={search} onChange={(e) => setSearch(e.target.value)} size="md" bg="gray.100" borderRadius="md" />
                                    </InputGroup>
                                </HStack>
                                <List spacing={2} flex="1" overflowY="auto" bg="white" p={0} minH={0} maxH={'100%'}>
                                    {isLoadingProjects && (
                                        <Text color="gray.500" textAlign="center" py={8}>
                                            {t('pages:projectManager.isLoadingProjects')}
                                        </Text>
                                    )}
                                    {!isLoadingProjects && filteredProjects.length === 0 && (
                                        <Text color="gray.400" textAlign="center" py={8}>
                                            {t('pages:projectManager.emptyProjects')}
                                        </Text>
                                    )}
                                    {!isLoadingProjects &&
                                        filteredProjects.map((proj) => (
                                            <ListItem
                                                key={proj.id}
                                                px={4}
                                                py={2}
                                                borderRadius="md"
                                                _even={{ bg: 'gray.50' }}
                                                _odd={{ bg: 'white' }}
                                                {...(selectedId === proj.id ? selectedAction : {})}
                                                _hover={selectedId === proj.id ? selectedAction : hoverAction}
                                                onClick={() => handleSelect(proj.id)}
                                                position="relative"
                                            >
                                                <Tooltip label={t('pages:projectManager.label_deletePro')} placement="top" bg="red.500" color="white">
                                                    <IconButton
                                                        aria-label="deleteProject"
                                                        icon={<DeleteIcon />}
                                                        size="sm"
                                                        colorScheme="red"
                                                        variant="ghost"
                                                        position="absolute"
                                                        top={1}
                                                        right={1}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleDelete(proj.id);
                                                        }}
                                                    />
                                                </Tooltip>

                                                <Box>
                                                    <Text fontWeight="bold" fontSize="md">
                                                        {proj.name}
                                                        <Text as="span" fontWeight="normal" fontSize="xs" color="gray.500" ml={2}>
                                                            ({proj.code})
                                                        </Text>
                                                    </Text>
                                                    <Box mt={1} pl={1}>
                                                        <Text fontSize="xs" color="gray.500">
                                                            {t('pages:projectManager.type')}
                                                            &nbsp;:&nbsp;
                                                            {getTestTypeName(proj.testType as TestType)}
                                                            &nbsp;|&nbsp;
                                                            {t('pages:projectManager.testWave')}
                                                            &nbsp;:&nbsp;
                                                            {filters.find((f) => f.no === proj.testWaveIdx)?.wavelength || '-'}
                                                            nm &nbsp;|&nbsp;
                                                            {t('pages:projectManager.refWave')}
                                                            &nbsp;:&nbsp;
                                                            {filters.find((f) => f.no === proj.refWaveIdx)?.wavelength || '-'}
                                                            nm
                                                        </Text>
                                                    </Box>
                                                </Box>
                                            </ListItem>
                                        ))}
                                </List>
                            </Box>

                            {/* 右侧详细信息表单 */}
                            <Box flex="1" m={0} px={2} overflowY="auto">
                                <VStack align="stretch" spacing={0}>
                                    <Flex py={3} w="100%" justifyContent="space-between" alignItems="center">
                                        {/* 添加按钮 */}
                                        <Tooltip label={t('pages:projectManager.label_addPro')} placement="end-start" hasArrow bg="blue.500" color="white">
                                            {/* 添加按钮 */}
                                            <IconButton mx={4} aria-label="addProject" icon={<AddIcon />} size="md" onClick={handleAdd} colorScheme="blue" />
                                        </Tooltip>
                                        <Spacer />

                                        {/* 保存按钮 */}
                                        <Button colorScheme="blue" mx={4} onClick={handleSave} size={'md'} disabled={!editProject}>
                                            {t('common:button.save')}
                                        </Button>
                                        {/* 退出按钮 */}
                                        <Button mx={4} onClick={onClose} size={'md'} colorScheme="blue">
                                            {t('common:button.exit')}
                                        </Button>
                                    </Flex>

                                    {/* 项目信息 */}
                                    <Box flex={1} bg="white" p={1}>
                                        {editProject ? (
                                            <>
                                                {/* 项目名称 */}
                                                <Text fontWeight="bold" fontSize="lg" mb={2} color="teal.600">
                                                    {t('pages:projectManager.label.title')}
                                                </Text>

                                                <VStack align="stretch" spacing={2} divider={<Divider />}>
                                                    <Box p={0}>
                                                        <SimpleGrid columns={2} spacing={1}>
                                                            <TextFormField
                                                                // label="项目名称"
                                                                label={t('pages:projectManager.label.ProjectName')}
                                                                value={editProject.name}
                                                                onChange={(value) => handleEditChange('name', value)}
                                                                // placeholder="必填,唯一"
                                                                placeholder={t('pages:projectManager.placeholder')}
                                                                isRequired
                                                                gridColumn="span 2"
                                                                isReadOnly={projects.some((p) => p.id === editProject.id)}
                                                            />

                                                            <TextFormField
                                                                // label="项目代码"
                                                                label={t('pages:projectManager.label.ProjectCode')}
                                                                value={editProject.code}
                                                                onChange={(value) => handleEditChange('code', value)}
                                                                // placeholder="必填,唯一"
                                                                placeholder={t('pages:projectManager.placeholder')}
                                                                isRequired
                                                                gridColumn="span 2"
                                                                isReadOnly={projects.some((p) => p.id === editProject.id)}
                                                            />
                                                            <SelectFormField
                                                                // label="检测波长"
                                                                label={t('pages:projectManager.testWave')}
                                                                value={editProject.testWaveIdx}
                                                                onChange={(value) => handleEditChange('testWaveIdx', value)}
                                                                options={filters.map((item) => ({
                                                                    id: item.no,
                                                                    label: item.wavelength.toString()
                                                                }))}
                                                                isRequired
                                                                isDisabled={isLoading}
                                                            />

                                                            <SelectFormField
                                                                // label="参考波长"
                                                                label={t('pages:projectManager.refWave')}
                                                                value={editProject.refWaveIdx}
                                                                onChange={(value) => handleEditChange('refWaveIdx', value)}
                                                                options={filters.map((item) => ({
                                                                    id: item.no,
                                                                    label: item.wavelength.toString()
                                                                }))}
                                                                placeholder=" "
                                                                isDisabled={isLoading}
                                                            />
                                                            <SelectFormField
                                                                // label="进板模式"
                                                                label={t('pages:projectManager.para.enteryMode')}
                                                                value={editProject.enteryMode}
                                                                onChange={(value) => handleEditChange('enteryMode', value)}
                                                                options={translateOptions([...mtpEnteryMode], 'common:commonDefines')}
                                                                isRequired
                                                            />

                                                            <NumberFormField
                                                                // label="振板时间"
                                                                label={t('pages:projectManager.para.shakeTime')}
                                                                value={editProject.shakeTime}
                                                                onChange={(value) => handleEditChange('shakeTime', value)}
                                                                // rightText="(秒)"
                                                                rightText={t('pages:projectManager.para.rightTxtSec')}
                                                            />

                                                            {/* <SelectFormField
                                                                // label="结果显示"
                                                                label={t(
                                                                    'pages:projectManager.label.resultShow'
                                                                )}
                                                                value={editProject.resultShow}
                                                                onChange={(value) =>
                                                                    handleEditChange(
                                                                        'resultShow',
                                                                        value
                                                                    )
                                                                }
                                                                options={projectResultShow}
                                                                // placeholder="必选"
                                                                placeholder={t(
                                                                    'pages:projectManager.placeholder_required'
                                                                )}
                                                                isRequired
                                                                gridColumn="span 2"
                                                            /> */}
                                                        </SimpleGrid>
                                                    </Box>

                                                    {/* 基础参数 */}
                                                    <Box>
                                                        <SimpleGrid columns={2} spacing={2}>
                                                            <SelectFormField
                                                                // label="项目类型"
                                                                label={t('pages:projectManager.para.projectType')}
                                                                value={editProject.testType}
                                                                onChange={(value) => handleEditChange('testType', value)}
                                                                options={projectTestType.map((item) => ({
                                                                    id: item,
                                                                    label: getTestTypeName(item as TestType)
                                                                }))}
                                                                // options={translateOptions(
                                                                //     [...projectTestType],
                                                                //     'common:commonDefines'
                                                                // )}
                                                                isRequired
                                                            />

                                                            <SelectFormField
                                                                // label="空白校正"
                                                                label={t('pages:projectManager.para.blank')}
                                                                value={editProject.useBlankCorrection}
                                                                onChange={(value) => handleEditChange('useBlankCorrection', value)}
                                                                options={projectBlankCorrection}
                                                                isRequired
                                                            />
                                                        </SimpleGrid>
                                                    </Box>

                                                    {/* 定量分析参数 - 仅当项目类型为定量时显示 */}
                                                    {editProject.testType === 'q' && (
                                                        <Box>
                                                            <Text fontWeight="bold" fontSize="lg" mb={2} color="blue.600">
                                                                定量分析参数
                                                            </Text>
                                                            <SimpleGrid columns={2} spacing={2}>
                                                                {/* 拟合模型 */}
                                                                <SelectFormField
                                                                    label="曲线拟合"
                                                                    value={editProject.quantitativeMethod}
                                                                    onChange={(value) => handleEditChange('quantitativeMethod', value)}
                                                                    options={fittingModelType.map((item) => ({
                                                                        id: item,
                                                                        label: getFittingModelTypeLabel(item as FittingModel)
                                                                    }))}
                                                                />

                                                                {/* 拟合公式 */}
                                                                <Input
                                                                    // flex="1"
                                                                    border="none"
                                                                    bg="transparent"
                                                                    color="blue.600"
                                                                    fontSize="md"
                                                                    fontWeight="bold"
                                                                    // mb={2}
                                                                    isReadOnly
                                                                    value={getFittingModelFormula((editProject.quantitativeMethod || 'LINEAR') as FittingModel)}
                                                                    onChange={(value) => handleEditChange('quantitativeFormula', value)}
                                                                />

                                                                <TextFormField
                                                                    // label="结果单位"
                                                                    label="结果单位"
                                                                    value={editProject.resultUnit}
                                                                    onChange={(value) => handleEditChange('resultUnit', value)}
                                                                />

                                                                <TextFormField
                                                                    // label="参考范围"
                                                                    label="参考范围"
                                                                    value={editProject.refRangeText}
                                                                    onChange={() => {}}
                                                                    isReadOnly
                                                                />

                                                                <NumberFormField
                                                                    // label="参考下限"
                                                                    label={t('pages:projectManager.label.refDown')}
                                                                    value={editProject.refRangeDown}
                                                                    onChange={(value) => handleEditChange('refRangeDown', value)}
                                                                />

                                                                <NumberFormField
                                                                    // label="参考上限"
                                                                    label={t('pages:projectManager.label.refUp')}
                                                                    value={editProject.refRangeUp}
                                                                    onChange={(value) => handleEditChange('refRangeUp', value)}
                                                                />
                                                                <SelectFormField
                                                                    // label="x 轴"
                                                                    label="x 轴"
                                                                    value={editProject.quantitativexAxis}
                                                                    onChange={(value) => handleEditChange('quantitativexAxis', value)}
                                                                    options={quantitativeAxisType}
                                                                />

                                                                <SelectFormField
                                                                    // label="y 轴"
                                                                    label={t('pages:projectManager.para.yAxis')}
                                                                    value={editProject.quantitativeyAxis}
                                                                    onChange={(value) => handleEditChange('quantitativeyAxis', value)}
                                                                    options={quantitativeAxisType}
                                                                />
                                                            </SimpleGrid>
                                                            <Box my={4}>
                                                                <Text>标准浓度</Text>

                                                                <Box overflowX="auto">
                                                                    <Table size="sm" variant="simple">
                                                                        <Thead>
                                                                            <Tr>
                                                                                {Array.from({ length: 10 }, (_, i) => (
                                                                                    <Th key={i} textAlign="center" px={0} py={0.5} fontSize="xs">
                                                                                        STD{i + 1}
                                                                                    </Th>
                                                                                ))}
                                                                            </Tr>
                                                                        </Thead>
                                                                        <Tbody>
                                                                            <Tr>
                                                                                {Array.from({ length: 10 }, (_, i) => {
                                                                                    const currentStdConcentration = editProject.stdConcentration || [];
                                                                                    const stdItem = currentStdConcentration[i] || [`STD${i + 1}`, 0];

                                                                                    return (
                                                                                        <Td key={i} px={0.5} py={0}>
                                                                                            <Input
                                                                                                p={0}
                                                                                                id={`stdConcentration${i}`}
                                                                                                type="number"
                                                                                                size="xs"
                                                                                                height="24px"
                                                                                                fontSize="xs"
                                                                                                value={stdItem[1]?.toString() || ''}
                                                                                                min={0}
                                                                                                step="any"
                                                                                                placeholder="0"
                                                                                                onChange={(e) => {
                                                                                                    handleStdConcentrationChange(i, e.target.value);
                                                                                                }}
                                                                                            />
                                                                                        </Td>
                                                                                    );
                                                                                })}
                                                                            </Tr>
                                                                        </Tbody>
                                                                    </Table>
                                                                </Box>
                                                            </Box>
                                                        </Box>
                                                    )}

                                                    {/* 定性分析参数 - 仅当项目类型为定性时显示 */}
                                                    {editProject.testType === 'x' && (
                                                        <Box>
                                                            <Text fontWeight="bold" fontSize="lg" mb={2} color="orange.600">
                                                                定性分析参数
                                                            </Text>
                                                            <SimpleGrid columns={2} spacing={2}>
                                                                {/* Cutoff公式 */}
                                                                <FormControl gridColumn={'span 2'}>
                                                                    <Flex align="center">
                                                                        <FormLabel sx={styles.label_normal} fontSize="lg" mb={0} minW="90px" textAlign="right">
                                                                            Cutoff =
                                                                        </FormLabel>
                                                                        <Box flex="1" ml={1}>
                                                                            <FormulaInputField value={editProject.cutOffFormula} onChange={(formula) => handleEditChange('cutOffFormula', formula)} singleLine={true} />
                                                                        </Box>
                                                                    </Flex>
                                                                </FormControl>

                                                                {/* 阳性判断 */}
                                                                <SelectFormField
                                                                    // label="阳性判断"
                                                                    label={t('pages:projectManager.para.positiveJudge')}
                                                                    value={editProject.postiveJudge}
                                                                    onChange={(value) => handleEditChange('postiveJudge', value)}
                                                                    options={projectPositiveJudge.map((item) => ({
                                                                        id: item,
                                                                        label: item
                                                                    }))}

                                                                    // isRequired
                                                                />
                                                                <Box color="transparent">占位符</Box>

                                                                {/* 结果显示 */}
                                                                {/* <SelectFormField
                                                                    label="阳性结果显示"
                                                                    // label={t(
                                                                    //     'pages:projectManager.para.positiveJudge'
                                                                    // )}
                                                                    value={editProject.positiveShowTxt}
                                                                    onChange={(value) =>
                                                                        handleEditChange(
                                                                            'positiveShowTxt',
                                                                            value
                                                                        )
                                                                    }
                                                                    options={positiveResultShowTxt.map(item => ({
                                                                        id: item,
                                                                        label: item
                                                                    }))}
                                                                
                                                                    // isRequired
                                                                /> */}

                                                                {/* 阴性对照 */}
                                                                <NumberFormField
                                                                    // label="阴性对照"
                                                                    label={t('pages:projectManager.para.negativeControl')}
                                                                    value={editProject.ncRangeDown}
                                                                    onChange={(value) => handleEditChange('ncRangeDown', value)}
                                                                    rightText={t('pages:projectManager.para.rightTxtDown')}
                                                                    // isRequired
                                                                />

                                                                {/* 阴性对照 */}
                                                                <NumberFormField
                                                                    // label="阴性对照"
                                                                    label={t('pages:projectManager.para.negativeControl')}
                                                                    value={editProject.ncRangeUp}
                                                                    onChange={(value) => handleEditChange('ncRangeUp', value)}
                                                                    rightText={t('pages:projectManager.para.rightTxtUp')}
                                                                    // isRequired
                                                                />

                                                                {/* 阳性对照 */}
                                                                <NumberFormField
                                                                    // label="阳性对照"
                                                                    label={t('pages:projectManager.para.positiveControl')}
                                                                    value={editProject.pcRangeDown}
                                                                    onChange={(value) => handleEditChange('pcRangeDown', value)}
                                                                    rightText={t('pages:projectManager.para.rightTxtDown')}
                                                                    // isRequired
                                                                />

                                                                {/* 阳性对照 */}
                                                                <NumberFormField
                                                                    // label="阳性对照"
                                                                    label={t('pages:projectManager.para.positiveControl')}
                                                                    value={editProject.pcRangeUp}
                                                                    onChange={(value) => handleEditChange('pcRangeUp', value)}
                                                                    rightText={t('pages:projectManager.para.rightTxtUp')}
                                                                    // isRequired
                                                                />
                                                            </SimpleGrid>

                                                            <SimpleGrid columns={2} spacing={2} mt={2} justifyContent="center" alignItems="center">
                                                                {/* 启用灰区 */}
                                                                <CheckboxFormField
                                                                    // label="启用灰区"
                                                                    label={t('pages:projectManager.para.grayZone')}
                                                                    isChecked={editProject.grayEnble}
                                                                    onChange={(checked) => handleEditChange('grayEnble', checked)}
                                                                />
                                                                <Box color="transparent">占位符</Box>
                                                                {/* 灰区下限 */}
                                                                <NumberFormField
                                                                    // label="灰区下限"
                                                                    label={t('pages:projectManager.para.grayZoneDown')}
                                                                    value={editProject.grayRangeDown}
                                                                    onChange={(value) => handleEditChange('grayRangeDown', value)}
                                                                    isDisabled={!editProject.grayEnble}
                                                                />

                                                                {/* 灰区上限 */}
                                                                <NumberFormField
                                                                    // label="灰区上限"
                                                                    label={t('pages:projectManager.para.grayZoneUp')}
                                                                    value={editProject.grayRangeUp}
                                                                    onChange={(value) => handleEditChange('grayRangeUp', value)}
                                                                    isDisabled={!editProject.grayEnble}
                                                                />
                                                            </SimpleGrid>
                                                        </Box>
                                                    )}
                                                </VStack>
                                            </>
                                        ) : (
                                            <Text color="gray.400" mt={20} textAlign="center">
                                                {t('pages:projectManager.pleaseSelect')}
                                            </Text>
                                        )}
                                    </Box>
                                </VStack>
                            </Box>
                        </HStack>
                    </ModalBody>
                </ModalContent>
            </Modal>

            {/* 删除确认对话框 */}
            <DeleteConfirmDialog
                isOpen={isDeleteDialogOpen}
                onClose={onDeleteDialogClose}
                onConfirm={confirmDelete}
                itemName={projectToDelete?.name || ''}
                itemIdentifier={projectToDelete?.code}
                // isLoading={isDeleteLoading}
            />
        </>
    );
};

export default ProjectManager;
