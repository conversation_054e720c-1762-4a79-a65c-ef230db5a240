import React from 'react';
import { Modal, ModalOverlay, ModalContent, ModalHeader, ModalFooter, ModalBody, Tabs, TabList, TabPanels, TabPanel, Tab, Button, HStack, Icon, useToast } from '@chakra-ui/react';
import { InfoIcon } from '@chakra-ui/icons';
import { useTranslation } from 'react-i18next';

// 导入报告相关组件
import ReportPara from '@components/report/ReportPara'; // 报告参数组件
import ReagentSupplier from '@components/report/ReagentSupplier'; // 试剂供应商组件
import LisPara from '@components/report/LisPara'; // LIS参数组件
import logger from '@renderer/utils/logger'; // 日志工具

/**
 * 报告管理组件的属性接口
 */
interface ReportManagerProps {
    isOpen: boolean; // 模态框是否打开
    onClose: () => void; // 关闭模态框的回调函数
}

/**
 * 报告管理组件
 * 用于管理报告相关的配置，包括报告参数、试剂供应商和LIS参数
 */
const ReportManager: React.FC<ReportManagerProps> = ({ isOpen, onClose }) => {
    const toast = useToast(); // Chakra UI 的提示组件

    // 注释掉的代码保留，可能在未来版本中使用
    // const deviceSettings = useDeviceSettings(isOpen);
    // const serialConfig = useSerialConfig(isOpen);
    // const filterManager = useFilterManager(deviceSettings.selectedDevice);

    // 国际化翻译钩子，支持通用翻译和页面翻译
    const { t } = useTranslation(['common', 'pages']);

    /**
     * 处理确认按钮点击事件
     * 保存所有报告管理相关的配置
     */
    const handleConfirm = async (): Promise<void> => {
        try {
            // 以下代码为预留的设备配置保存逻辑，暂时注释
            // if (deviceSettings.selectedDevice === UNSET_DEVICE.model) {
            //     toast({
            //         title: '请选择设备型号',
            //         status: 'warning',
            //         duration: 3000,
            //         isClosable: true
            //     });
            //     return;
            // }

            // // 保存所有配置
            // await window.customApi.app.updateAppSettings({
            //     deviceModel: deviceSettings.selectedDevice
            // });

            // if (!(await serialConfig.saveConfig())) {
            //     throw new Error('通讯参数保存失败');
            // }

            // if (!(await filterManager.saveFilters())) {
            //     throw new Error('滤光片参数保存失败');
            // }

            // 显示保存成功提示
            toast({
                title: t('common:message.saveSuccess'), // 使用国际化文本
                status: 'success',
                duration: 2000
            });

            // 关闭模态框
            onClose();
        } catch (error) {
            // 记录错误日志
            logger.error('save config failed', error, {
                component: './src/renderer/src/pages/1_3_reportMgr/index.tsx'
            });

            // 显示错误提示
            toast({
                title: String(error),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
        }
    };

    return (
        <>
            {/* 报告管理模态框 */}
            <Modal
                isOpen={isOpen}
                onClose={onClose}
                size="xl"
                isCentered
                closeOnEsc={false} // 禁用ESC键关闭
                closeOnOverlayClick={false} // 禁用点击遮罩层关闭
            >
                <ModalOverlay />
                <ModalContent maxW="600px" maxH="650px" w="100vw" h="100vh" overflow="auto">
                    {/* 模态框标题栏 */}
                    <ModalHeader fontFamily="MiSans-Bold" bg="teal.500" color="white">
                        <HStack>
                            <Icon as={InfoIcon} boxSize={6} />
                            {/* 使用国际化的报告管理标题 */}
                            <span>{t('pages:ReportManager.title')}</span>
                        </HStack>
                    </ModalHeader>
                    {/* 模态框主体内容 */}
                    <ModalBody>
                        <Tabs>
                            {/* 标签页列表 */}
                            <TabList>
                                {/* 报告参数标签页 */}
                                <Tab fontFamily="MiSans-Bold">{t('components:ReportPara.title')}</Tab>
                                {/* 试剂供应商标签页 */}
                                <Tab fontFamily="MiSans-Bold">{t('components:reagentSupplier.title')}</Tab>
                                {/* LIS参数标签页 */}
                                <Tab fontFamily="MiSans-Bold">{t('components:lisPara.title')}</Tab>
                            </TabList>

                            {/* 标签页内容面板 */}
                            <TabPanels>
                                {/* 报告参数面板 */}
                                <TabPanel>
                                    <ReportPara />
                                </TabPanel>
                                {/* 试剂供应商面板 */}
                                <TabPanel>
                                    <ReagentSupplier />
                                </TabPanel>
                                {/* LIS参数面板 */}
                                <TabPanel>
                                    <LisPara />
                                </TabPanel>
                            </TabPanels>
                        </Tabs>
                    </ModalBody>
                    {/* 模态框底部按钮区域 */}
                    <ModalFooter>
                        {/* 确定按钮 - 保存配置并关闭 */}
                        <Button colorScheme="blue" mr={3} onClick={handleConfirm} size={'md'}>
                            {t('common:button.confirm')}
                        </Button>
                        {/* 取消按钮 - 直接关闭不保存 */}
                        <Button onClick={onClose} size={'md'}>
                            {t('common:button.cancel')}
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </>
    );
};

export default ReportManager;
