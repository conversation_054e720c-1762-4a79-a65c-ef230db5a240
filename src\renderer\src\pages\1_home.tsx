import React from 'react';
import { Flex, Box, Link, Spacer, Text, useBoolean } from '@chakra-ui/react';

import LanguageSwitch from '@components/LanguageSwitch';
import AboutPage from '@pages/about';
import InfoCard from '@components/InfoCard';

import DeviceManager from '@pages/1_1_deviceMgr';
import ProjectManager from '@pages/1_2_projectMgr';
import ReportManager from '@pages/1_3_reportMgr';

import { useTranslation } from 'react-i18next';

import DeviceImg from '@icons/device.svg';
import ProjectImg from '@icons/project.svg';
import ReportTplImg from '@icons/reporttpl.svg';

import { useColorModeValue } from '@chakra-ui/react';

const Home: React.FC = () => {
    const { t } = useTranslation(['common', 'pages']);
    // const [isOpen_about, setIsOpen_about] = useState(false);
    // const [isOpen_deviceMgr, setIsOpen_deviceMgr] = useState(false);
    // const [isOpen_project, setIsOpen_project] = useState(false);
    // const [isOpen_report, setIsOpen_report] = useState(false);
    const [isOpen_about, setIsOpen_about] = useBoolean(false);
    const [isOpen_deviceMgr, setIsOpen_deviceMgr] = useBoolean(false);
    const [isOpen_project, setIsOpen_project] = useBoolean(false);
    const [isOpen_report, setIsOpen_report] = useBoolean(false);

    const handleDevice = () => {
        console.log('设备管理');
        setIsOpen_deviceMgr.on();
    };

    const handleProject = () => {
        console.log('项目管理');
        setIsOpen_project.on();
    };

    const handleTemplate = () => {
        console.log('报告管理');
        setIsOpen_report.on();
    };

    const handleAboutClick = (e: React.MouseEvent): void => {
        e.preventDefault();
        setIsOpen_about.on();
    };

    // 使用 Chakra UI 的颜色模式获取动态颜色
    const bgGradient = useColorModeValue(
        'linear(to-br, blue.50, gray.50)',
        'linear(to-br, gray.900, blue.900)'
    );
    const cardGradient = useColorModeValue('white', 'gray.800');

    const borderColor = useColorModeValue('gray.200', 'gray.700');

    const cardInfoArray = [
        {
            iconsrc: DeviceImg,
            title: t('pages:home.infoCard.device.title'),
            description: t('pages:home.infoCard.device.description'),
            buttontext: t('common:button.entry'),
            onClick: handleDevice
        },
        {
            iconsrc: ProjectImg,
            title: t('pages:home.infoCard.project.title'),
            description: t('pages:home.infoCard.project.description'),
            buttontext: t('common:button.entry'),
            onClick: handleProject
        },
        {
            iconsrc: ReportTplImg,
            title: t('pages:home.infoCard.report.title'),
            description: t('pages:home.infoCard.report.description'),
            buttontext: t('common:button.entry'),
            onClick: handleTemplate
        }
    ];

    return (
        <Flex
            w="100%"
            h="100%"
            direction="column"
            bgGradient={bgGradient}
            position="relative"
            overflow="hidden"
        >
            {/* 主要内容区域 */}
            <Box
                flex="1"
                position="relative"
                overflow="hidden"
                display="flex"
                alignItems="center"
                justifyContent="center"
                py={{ base: 4, md: 6, lg: 8 }}
            >
                <Flex
                    direction={{ base: 'column', lg: 'row' }}
                    gap={{ base: 4, md: 6, xl: '60px' }}
                    justify="center"
                    align="center"
                    maxW={{ base: '1600px', xl: '1800px' }}
                    w="100%"
                    h="100%"
                >
                    {cardInfoArray.map((item, index) => (
                        <InfoCard
                            key={index}
                            iconsrc={item.iconsrc}
                            title={item.title}
                            description={item.description}
                            buttontext={item.buttontext}
                            onClick={item.onClick}
                            bg={cardGradient}
                            borderColor={borderColor}
                            w={{ base: '100%', lg: '300px', xl: '360px' }}
                            maxW={{ base: '400px', xl: '480px' }}
                            h={{ base: 'auto', lg: '380px', xl: '420px' }}
                            display="flex"
                            justifyContent="center"
                            transition="all 0.3s"
                            _hover={{
                                transform: 'translateY(-4px)',
                                boxShadow: 'xl',
                                borderColor: 'blue.200'
                            }}
                        />
                    ))}
                </Flex>
            </Box>

            {/* 底部信息区域 */}
            <Box
                h="50px"
                position="relative"
                bg="rgba(255, 255, 255, 0.5)"
                backdropFilter="blur(10px)"
                borderTop="1px solid rgba(255, 255, 255, 0.2)"
                display="flex"
                flexDirection="column"
                justifyContent="center"
            >
                <Flex
                    justify="space-between"
                    align="center"
                    // maxW={{ base: '1600px', xl: '1800px' }}
                    // mx="auto"
                    w="100%"
                    h="100%"
                    px={'10px'}
                    gap={'10px'}
                >
                    {/* 公司名称 */}
                    <Text fontSize="lg">{t('common:company')}</Text>
                    <Spacer />
                    {/* 语言切换 */}
                    <LanguageSwitch />

                    <Link
                        onClick={handleAboutClick}
                        fontSize="lg"
                        // fontFamily="MiSans-Demibold"
                        // color="blue.500"
                        _hover={{
                            bg: 'gray.50',
                            cursor: 'pointer',
                            color: 'blue.600',
                            textDecoration: 'none'
                        }}
                    >
                        {/* 关于 */}
                        {t('common:about')}
                    </Link>
                </Flex>
            </Box>

            {/* 关于弹窗组件 */}
            {isOpen_about && (
                <AboutPage isOpen={isOpen_about} onClose={() => setIsOpen_about.off()} />
            )}

            {/* 设备管理窗口 */}
            {isOpen_deviceMgr && (
                <DeviceManager
                    isOpen={isOpen_deviceMgr}
                    onClose={() => setIsOpen_deviceMgr.off()}
                />
            )}

            {/* 项目管理窗口 */}
            {isOpen_project && (
                <ProjectManager isOpen={isOpen_project} onClose={() => setIsOpen_project.off()} />
            )}

            {/* 报告管理窗口 */}
            {isOpen_report && (
                <ReportManager isOpen={isOpen_report} onClose={() => setIsOpen_report.off()} />
            )}
        </Flex>
    );
};

export default Home;
