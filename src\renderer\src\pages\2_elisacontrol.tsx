import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Button,
    Flex,
    Spacer,
    useToast,
    Select,
    IconButton,
    useDisclosure
} from '@chakra-ui/react';
import { DeleteIcon } from '@chakra-ui/icons';
import DeleteConfirmDialog from '@components/DeleteConfirmDialog';
import ProjectInfoForm from '@components/ElisaPlate/ProjectInfoForm';
import ElisaPlateForm from '@components/ElisaPlate/ElisaPlateForm';
import SampleTypeForm from '@components/ElisaPlate/SampleTypeForm';
import DetectionInfoForm from '@components/ElisaPlate/DetectionInfoForm';
// import AdditionalInfoForm from '@components/ElisaPlate/AdditionalInfoForm';
import TemplateModal from '@components/TemplateModal';
import TemplateNameModal from '@components/TemplateNameModal';
// import { useElisaFitting } from '@renderer/hooks/useElisaFitting';
// import { ElisaFittingPanel } from '@components/ElisaFittingPanel';

import { SampleTypeVariable, MTPLayoutType } from '@shared/commondefines';
import {
    Project,
    PlateData,
    SerialPortOptions,
    WellData,
    ProjectAssignments,
    TestAdditionalInfo
} from '@shared/types';

import {
    isElisaDataComplete,
    parseODValuesToArray,
    convertArrayToRecord,
    isProjectVaild,
    generateReadCommand,
    convertToTestRecord,
    calculateODValue,
    calculateCutOffValue,
    saveTestRecordToDb
} from '@renderer/utils';

import logger from '@renderer/utils/logger';

// 报告参数存储key
const REPORT_PARA_STORE_KEY = 'app.reportPara';
// 报告参数数据结构
interface ReportParaData {
    mainTitle: string; // 报告主标题
    subTitle: string; // 报告副标题
    testMethod: string; // 检测方法
    testBasis: string; // 检测依据
    testOperator: string; // 检测人员
    reviewer: string; // 审核员
}

const ElisaControl: React.FC = () => {
    const toast = useToast();
    const { t } = useTranslation(['common', 'pages']);

    const [testAdditionalInfo, setTestAdditionalInfo] = useState<TestAdditionalInfo>({});

    const [plateData, setPlateData] = useState<PlateData>({
        layoutType: MTPLayoutType.SingleProjectLayout,
        singleProjectAssignments: {} as Project,
        multiProjectAssignments: {} as ProjectAssignments,
        wellData: {}
    });

    const [selectedProject, setSelectedProject] = useState<Project>({} as Project);
    const [selectedProjects, setSelectedProjects] = useState<Project[]>([]);
    const [curSampleType, setCurSampleType] = useState<SampleTypeVariable>({
        name: '',
        type: 'none'
    });
    const [useCommonBlank, setUseCommonBlank] = useState<boolean>(false);
    const [isTemplateModalOpen, setIsTemplateModalOpen] = useState<boolean>(false);
    const [isTemplateNameModalOpen, setIsTemplateNameModalOpen] = useState<boolean>(false);
    const [projectRefreshKey, setProjectRefreshKey] = useState<number>(0);
    const [templates, setTemplates] = useState<
        Array<{ id: string; name: string; createdAt: string }>
    >([]);
    const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');
    const [isLoadingTemplates, setIsLoadingTemplates] = useState<boolean>(false);
    const [templateToDelete, setTemplateToDelete] = useState<{ id: string; name: string } | null>(
        null
    );
    const [isDeletingTemplate, setIsDeletingTemplate] = useState<boolean>(false);
    const [isReadingPlate, setIsReadingPlate] = useState<boolean>(false);

    const {
        isOpen: isDeleteDialogOpen,
        onOpen: onDeleteDialogOpen,
        onClose: onDeleteDialogClose
    } = useDisclosure();

    // // 添加定量拟合Hook
    // const {
    //     result: fittingResult,
    //     predictConcentration,
    //     isValid: isFittingValid
    // } = useElisaFitting('LOGISTIC_4P');

    // // 从标准品数据生成标准曲线
    // const getStandardCurveData = useCallback((): StandardCurveData | null => {
    //     const standardWells = Object.entries(plateData.wellData)
    //         .filter(([_, well]) => well.sampleType?.type === 'std')
    //         .map(([wellId, well]) => ({
    //             wellId,
    //             concentration: parseFloat(well.sampleNumber || '0'),
    //             response: well.odValue || 0
    //         }))
    //         .filter((item) => item.concentration > 0 && item.response > 0)
    //         .sort((a, b) => a.concentration - b.concentration);

    //     if (standardWells.length < 3) return null;

    //     return {
    //         concentrations: standardWells.map((w) => w.concentration),
    //         responses: standardWells.map((w) => w.response),
    //         name: selectedProject.name || '标准曲线'
    //     };
    // }, [plateData.wellData, selectedProject.name]);

    // // 自动计算样本浓度
    // const calculateSampleConcentrations = useCallback(() => {
    //     if (!fittingResult || !isFittingValid) return;

    //     setPlateData((prev) => {
    //         const newWellData = { ...prev.wellData };

    //         Object.entries(newWellData).forEach(([wellId, well]) => {
    //             if (well.sampleType?.type === 'sample' && well.odValue) {
    //                 const concentration = predictConcentration(well.odValue);
    //                 if (concentration !== null) {
    //                     newWellData[wellId] = {
    //                         ...well,
    //                         concentration,
    //                         result: 0
    //                     };
    //                 }
    //             }
    //         });

    //         return { ...prev, wellData: newWellData };
    //     });
    // }, [fittingResult, isFittingValid, predictConcentration]);

    // // 拟合结果变化时自动计算
    // React.useEffect(() => {
    //     calculateSampleConcentrations();
    // }, [calculateSampleConcentrations]);

    // 当选择的项目改变时，更新selectedProjects
    useEffect(() => {
        if (selectedProject.id) {
            // 将当前选中的项目添加到selectedProjects中（如果不存在的话）
            setSelectedProjects((prev) => {
                const exists = prev.find((p) => p.id === selectedProject.id);
                if (!exists) {
                    return [...prev, selectedProject];
                }
                return prev;
            });
        }
        loadReportPara();
    }, [selectedProject]);

    // 加载报告参数
    const loadReportPara = async () => {
        try {
            const response =
                await window.customApi.store.get<ReportParaData>(REPORT_PARA_STORE_KEY);
            logger.info('Load report para response:', {
                data: response,
                component: './src/renderer/src/components/report/ReportPara.tsx'
            });

            if (response.success && response.data) {
                logger.info('Loading existing data from storage:', {
                    data: response.data,
                    component: './src/renderer/src/components/report/ReportPara.tsx'
                });
                // 直接使用存储中的数据

                setTestAdditionalInfo({
                    ...testAdditionalInfo,
                    mainTitle: response.data.mainTitle, // 报告主标题
                    subTitle: response.data.subTitle, // 报告副标题
                    testMethod: response.data.testMethod, // 检测方法
                    testBasis: response.data.testBasis, // 检测依据
                    testOperator: response.data.testOperator, // 检测人员
                    testReviewer: response.data.reviewer
                });
            } else {
                // 如果没有保存的数据，使用默认值并保存
                logger.info('No existing data, using default values:', {
                    component: './src/renderer/src/components/report/ReportPara.tsx'
                });
            }
        } catch (error) {
            logger.error('Load report para failed:', error, {
                component: './src/renderer/src/components/report/ReportPara.tsx'
            });
            logger.info('Error occurred, using default values:', {
                component: './src/renderer/src/components/report/ReportPara.tsx'
            });
        }
    };

    // 监听项目列表更新事件
    useEffect(() => {
        const handleProjectListUpdate = () => {
            refreshProjectList();
        };

        window.addEventListener('projectListUpdated', handleProjectListUpdate);

        // 清理事件监听器
        return () => {
            window.removeEventListener('projectListUpdated', handleProjectListUpdate);
        };
    }, []);

    // 组件加载时获取模板列表
    useEffect(() => {
        loadTemplates();
    }, []);

    const closeTemplateModal = () => {
        setIsTemplateModalOpen(false);
    };

    const cleanTemplateNameModal = () => {
        // 清空所有设置信息
        setPlateData({
            layoutType: MTPLayoutType.SingleProjectLayout,
            singleProjectAssignments: {} as Project,
            multiProjectAssignments: {} as ProjectAssignments,
            wellData: {}
        });
        setSelectedProjects([]);
        setCurSampleType({
            name: '',
            type: 'none'
        });

        // 清空选中的项目
        setSelectedProject({} as Project);

        // 清空模板选择
        setSelectedTemplateId('');

        // 通知ElisaPlateForm清空wellNumbers状态
        // 这里我们需要通过某种方式通知子组件清空状态
        // 可以通过添加一个key来强制重新渲染组件
        setProjectRefreshKey((prev) => prev + 1);

        console.log('已清空模板设置');
    };

    const openTemplateNameModal = () => {
        setIsTemplateNameModalOpen(true);
    };

    const closeTemplateNameModal = () => {
        setIsTemplateNameModalOpen(false);
    };

    const handleTemplateNameSubmit = async (name: string) => {
        console.log('模板名称：', name);

        if (!name.trim()) {
            toast({
                title: t('pages:elisaControl.messages.error'), // 错误
                description: t('pages:elisaControl.messages.template.nameRequired'), // 请输入模板名称
                status: 'error',
                duration: 3000,
                isClosable: true
            });
            return;
        }

        try {
            // 调用保存模板的API
            const response = await window.customApi.configInfo.addMtpTemplate({
                name: name.trim(),
                plateData: plateData,
                createdBy: 'current_user' // 这里可以传入当前用户信息
            });

            if (response.success) {
                toast({
                    title: t('pages:elisaControl.messages.success'), // 成功
                    description: t('pages:elisaControl.messages.template.saveSuccess'), // 模板保存成功
                    status: 'success',
                    duration: 3000,
                    isClosable: true
                });
                closeTemplateNameModal();
                // 刷新模板列表
                loadTemplates();
            } else {
                toast({
                    title: t('pages:elisaControl.messages.error'), // 错误
                    description:
                        response.error || t('pages:elisaControl.messages.template.saveFailed'), // 保存模板失败
                    status: 'error',
                    duration: 3000,
                    isClosable: true
                });
            }
        } catch (error) {
            logger.error('Failed to save template:', error, {
                component: './src/renderer/src/pages/2_elisacontrol.tsx'
            });

            toast({
                title: t('pages:elisaControl.messages.error'), // 错误
                description: t('pages:elisaControl.messages.template.saveError'), // 保存模板时发生错误
                status: 'error',
                duration: 3000,
                isClosable: true
            });
        }
    };

    const refreshProjectList = () => {
        setProjectRefreshKey((prev) => prev + 1);
    };

    // 加载模板列表
    const loadTemplates = async () => {
        setIsLoadingTemplates(true);
        try {
            const response = await window.customApi.configInfo.getAllMtpTemplates();
            if (response.success) {
                // 转换数据格式以匹配状态类型
                const templateList = (response.data || []).map(
                    (template: { id: string; name: string; createdAt: Date | string }) => ({
                        id: template.id,
                        name: template.name,
                        createdAt:
                            template.createdAt instanceof Date
                                ? template.createdAt.toISOString()
                                : template.createdAt
                    })
                );
                setTemplates(templateList);
            } else {
                logger.error('Failed to load template list:', response.error, {
                    component: './src/renderer/src/pages/2_elisacontrol.tsx'
                });

                toast({
                    title: t('pages:elisaControl.messages.error'), // 错误
                    description: t('pages:elisaControl.messages.template.listLoadFailed'), // 加载模板列表失败
                    status: 'error',
                    duration: 3000,
                    isClosable: true
                });
            }
        } catch (error) {
            logger.error('Failed to load template list:', error, {
                component: './src/renderer/src/pages/2_elisacontrol.tsx'
            });
            toast({
                title: t('pages:elisaControl.messages.error'), // 错误
                description: t('pages:elisaControl.messages.template.listLoadError'), // 加载模板列表时发生错误
                status: 'error',
                duration: 3000,
                isClosable: true
            });
        } finally {
            setIsLoadingTemplates(false);
        }
    };

    // 处理模板选择
    const handleTemplateSelect = async (templateId: string) => {
        if (!templateId) return;

        // 清空选中的项目 触发项目列表更新
        setSelectedProject({} as Project);

        setSelectedTemplateId(templateId);

        try {
            const response = await window.customApi.configInfo.getMtpTemplateById(templateId);
            if (response.success && response.data) {
                // 解析模板数据并应用到当前界面
                const templateData = JSON.parse(response.data.plateData);
                setPlateData(templateData);

                // 更新项目列表
                if (templateData.layoutType === MTPLayoutType.SingleProjectLayout) {
                    // 单项目布局
                    if (
                        templateData.singleProjectAssignments &&
                        templateData.singleProjectAssignments.id
                    ) {
                        setSelectedProjects([templateData.singleProjectAssignments]);
                        setSelectedProject(templateData.singleProjectAssignments);
                    }
                } else if (templateData.multiProjectAssignments) {
                    // 多项目布局
                    const assignedProjects = Object.values(
                        templateData.multiProjectAssignments
                    ) as Project[];
                    setSelectedProjects(assignedProjects);

                    // 设置第一个项目为当前选中项目
                    if (assignedProjects.length > 0) {
                        setSelectedProject(assignedProjects[0]);
                    }
                }

                toast({
                    title: t('pages:elisaControl.messages.success'), // 成功
                    description: t('pages:elisaControl.messages.template.loadSuccess'), // 模板加载成功
                    status: 'success',
                    duration: 3000,
                    isClosable: true
                });
            } else {
                toast({
                    title: t('pages:elisaControl.messages.error'), // 错误
                    description:
                        response.error || t('pages:elisaControl.messages.template.loadFailed'), // 加载模板失败
                    status: 'error',
                    duration: 3000,
                    isClosable: true
                });
            }
        } catch (error) {
            logger.error('Failed to load template:', error, {
                component: './src/renderer/src/pages/2_elisacontrol.tsx'
            });

            toast({
                title: t('pages:elisaControl.messages.error'), // 错误
                description: t('pages:elisaControl.messages.template.loadError'), // 加载模板时发生错误
                status: 'error',
                duration: 3000,
                isClosable: true
            });
        }
    };

    // 显示删除确认对话框
    const handleDeleteTemplate = () => {
        if (!selectedTemplateId) {
            toast({
                title: t('pages:elisaControl.messages.info'), // 提示
                description: t('pages:elisaControl.messages.template.selectFirst'), // 请先选择要删除的模板
                status: 'warning',
                duration: 3000,
                isClosable: true
            });
            return;
        }

        // 找到要删除的模板信息
        const template = templates.find((t) => t.id === selectedTemplateId);
        if (template) {
            setTemplateToDelete(template);
            onDeleteDialogOpen();
        }
    };

    // 确认删除模板
    const confirmDeleteTemplate = async () => {
        if (!templateToDelete) return;

        setIsDeletingTemplate(true);
        try {
            const response = await window.customApi.configInfo.deleteMtpTemplate(
                templateToDelete.id
            );
            if (response.success) {
                toast({
                    title: t('pages:elisaControl.messages.success'), // 成功
                    description: t('pages:elisaControl.messages.template.deleteSuccess'), // 模板删除成功
                    status: 'success',
                    duration: 3000,
                    isClosable: true
                });
                // 清空选择并刷新模板列表
                setSelectedTemplateId('');
                loadTemplates();
            } else {
                toast({
                    title: t('pages:elisaControl.messages.error'), // 错误
                    description:
                        response.error || t('pages:elisaControl.messages.template.deleteFailed'), // 删除模板失败
                    status: 'error',
                    duration: 3000,
                    isClosable: true
                });
            }
        } catch (error) {
            logger.error('Failed to delete template:', error, {
                component: './src/renderer/src/pages/2_elisacontrol.tsx'
            });

            toast({
                title: t('pages:elisaControl.messages.error'), // 错误
                description: t('pages:elisaControl.messages.template.deleteError'), // 删除模板时发生错误
                status: 'error',
                duration: 3000,
                isClosable: true
            });
        } finally {
            setIsDeletingTemplate(false);
            onDeleteDialogClose();
            setTemplateToDelete(null);
        }
    };

    // 读板函数
    const handleReadPlate = async () => {
        // 验证项目设置
        if (!isProjectVaild(toast, plateData)) {
            return;
        }

        // 生成读板命令字符串
        const readCommand = generateReadCommand(toast, plateData);
        if (readCommand === '') {
            return;
        }

        // 设置读板状态为进行中
        setIsReadingPlate(true);

        // 在读板前清空现有数据
        setPlateData((prevPlateData) => {
            const newWellData = { ...prevPlateData.wellData };
            Object.keys(newWellData).forEach((wellId) => {
                if (newWellData[wellId]) {
                    newWellData[wellId] = {
                        ...newWellData[wellId],
                        odMain: undefined,
                        odRef: undefined,
                        odValue: undefined,
                        odRatio: undefined,
                        result: undefined
                    };
                }
            });
            return {
                ...prevPlateData,
                wellData: newWellData
            };
        });

        try {
            // 1. 从配置文件读取串口配置信息
            const serialConfigResponse = await window.customApi.store.get<SerialPortOptions>(
                'app.serialConfig',
                {
                    path: '',
                    baudRate: 4800,
                    dataBits: 8,
                    stopBits: 1,
                    parity: 'none',
                    timeout: 20
                }
            );

            logger.info('step1  get serialConfig from store', {
                data: serialConfigResponse,
                component: './src/renderer/src/pages/2_elisacontrol.tsx'
            });

            if (!serialConfigResponse.success) {
                toast({
                    title: t('pages:elisaControl.messages.error'), // 错误
                    description: t('pages:elisaControl.messages.serial.configFailed'), // 获取串口配置失败
                    status: 'error',
                    duration: 3000,
                    isClosable: true
                });
                return;
            }

            const serialConfig = serialConfigResponse.data as SerialPortOptions;
            if (!serialConfig || !serialConfig.path) {
                toast({
                    title: t('pages:elisaControl.messages.error'), // 错误
                    description: t('pages:elisaControl.messages.serial.configRequired'), // 请先配置串口参数
                    status: 'error',
                    duration: 3000,
                    isClosable: true
                });
                return;
            }

            // 2. 打开串口
            const openResult = await window.customApi.serial.openSerialPort(serialConfig);
            if (!openResult) {
                toast({
                    title: t('pages:elisaControl.messages.error'), // 错误
                    description: t('pages:elisaControl.messages.serial.openFailed'), // 打开串口失败
                    status: 'error',
                    duration: 5000,
                    isClosable: true
                });
                return;
            }

            logger.info('step2  open serial', {
                data: 'openResult: ' + openResult,
                component: './src/renderer/src/pages/2_elisacontrol.tsx'
            });

            toast({
                title: t('pages:elisaControl.messages.info'), // 提示
                description: t('pages:elisaControl.messages.serial.opened'), // 串口已打开，发送读板命令...
                status: 'info',
                duration: 3000,
                isClosable: true
            });

            // 3. 发送读板命令
            const sendResult = await window.customApi.serial.sendData(readCommand);
            logger.info('step3 send readCommand to serial port', {
                data: 'sendCommand: [' + readCommand.trim() + '] sendResult:' + sendResult,
                component: './src/renderer/src/pages/2_elisacontrol.tsx'
            });

            if (!sendResult) {
                toast({
                    title: t('pages:elisaControl.messages.error'), // 错误
                    description: t('pages:elisaControl.messages.serial.sendFailed'), // 发送读板命令失败
                    status: 'error',
                    duration: 5000,
                    isClosable: true
                });
                await window.customApi.serial.closeSerialPort();
                return;
            }

            const timeoutMs = serialConfig.timeout ? serialConfig.timeout * 1000 : 20000;

            // 4. 接收数据
            const toastId = toast({
                title: t('pages:elisaControl.messages.info'), // 提示
                description: t('pages:elisaControl.messages.serial.receiving'), // 正在接收数据...
                status: 'loading',
                duration: timeoutMs,
                isClosable: true
            });

            const receivedDataString = await window.customApi.serial.receiveData(timeoutMs);
            logger.info('\r\nstep4  receive data from serial port', {
                data:
                    ' [timeoutMs:' + timeoutMs + ']\r\n' + 'receivedData:\r\n' + receivedDataString,
                component: './src/renderer/src/pages/2_elisacontrol.tsx'
            });

            toast.close(toastId);

            // 5. 关闭串口
            await window.customApi.serial.closeSerialPort();

            logger.info('接收到的数据长度:', {
                data: receivedDataString.length,
                component: './src/renderer/src/pages/2_elisacontrol.tsx'
            });

            // 6. 处理接收到的数据
            if (receivedDataString && receivedDataString.length > 10) {
                const dataString = receivedDataString;
                console.log('接收到的数据:', dataString);

                // 检查数据完整性
                if (!isElisaDataComplete(dataString)) {
                    toast({
                        title: t('pages:elisaControl.messages.warning'), // 警告
                        description: t('pages:elisaControl.messages.serial.dataIncomplete'), // 接收到的数据不完整，可能缺少部分酶标板数据
                        status: 'warning',
                        duration: 5000,
                        isClosable: true
                    });
                    return;
                }

                // 解析OD值为数组格式
                const odData = parseODValuesToArray(dataString);
                console.log('解析后的mainData数组长度:', odData.mainData.length, odData.mainData);
                console.log('解析后的refData数组长度:', odData.refData.length, odData.refData);
                console.log('解析后的OD值数组:', odData);

                // 将主数据转换为Record格式
                const mainOdValues = convertArrayToRecord(odData.mainData);

                // 如果有参考数据，也转换为Record格式
                const refOdValues = convertArrayToRecord(odData.refData);

                logger.info('转换为Record格式', {
                    data: {
                        mainOdValuesLength: Object.keys(mainOdValues).length,
                        mainOdValues: mainOdValues,
                        refOdValuesLength: Object.keys(refOdValues).length,
                        refOdValues: refOdValues
                    },
                    component: './src/renderer/src/pages/2_elisacontrol.tsx'
                });

                // 创建默认的 WellData 对象
                const createDefaultWellData = (): WellData => ({
                    sampleType: { name: '', type: 'none' },
                    sampleNumber: '',
                    odMain: undefined,
                    odRef: undefined,
                    odValue: undefined,
                    odRatio: undefined,
                    concentration: undefined,
                    result: undefined
                });

                // 首先清空所有孔位的计算值，保留样本配置
                const newWellData = { ...plateData.wellData };
                Object.keys(newWellData).forEach((wellId) => {
                    if (newWellData[wellId]) {
                        newWellData[wellId] = {
                            ...newWellData[wellId],
                            odMain: undefined,
                            odRef: undefined,
                            odValue: undefined,
                            odRatio: undefined,
                            concentration: undefined,
                            result: undefined
                        };
                    }
                });

                // 填充主数据到 odMain
                Object.entries(mainOdValues).forEach(([wellId, odMain]) => {
                    if (!newWellData[wellId]) {
                        newWellData[wellId] = createDefaultWellData();
                    }
                    newWellData[wellId] = {
                        ...newWellData[wellId],
                        odMain: odMain
                    };
                });

                // 如果有参考数据，填充到 odRef
                if (Object.keys(refOdValues).length > 0) {
                    Object.entries(refOdValues).forEach(([wellId, odRef]) => {
                        // 如果孔位不存在，创建新的 WellData
                        if (!newWellData[wellId]) {
                            newWellData[wellId] = createDefaultWellData();
                            logger.error('odRef 数据填充，如果数据结构不存在，100%的程序有问题', {
                                data: {
                                    wellId: wellId,
                                    newWellData: newWellData[wellId]
                                },
                                component: './src/renderer/src/pages/2_elisacontrol.tsx'
                            });
                        }

                        // 更新 odRef 值
                        newWellData[wellId] = {
                            ...newWellData[wellId],
                            odRef: odRef
                        };
                    });
                }

                logger.info('newWellData', {
                    data: {
                        totalWells: Object.keys(newWellData).length,
                        wellsWithData: Object.keys(newWellData).filter(
                            (wellId) =>
                                newWellData[wellId]?.odMain !== undefined ||
                                newWellData[wellId]?.odRef !== undefined
                        ).length,
                        sampleWells: Object.keys(newWellData).filter(
                            (wellId) => newWellData[wellId]?.sampleType?.type === 'sample'
                        ).length
                    },
                    component: './src/renderer/src/pages/2_elisacontrol.tsx'
                });

                // 更新孔板数据
                const updatedPlateData = {
                    ...plateData,
                    wellData: newWellData
                };
                setPlateData(updatedPlateData);

                // 使用更新后的数据进行计算
                const testRecords = convertToTestRecord(updatedPlateData, testAdditionalInfo);
                const calcODValueTestRecords = calculateODValue(testRecords);
                const calcCutOffValueTestRecords = calculateCutOffValue(calcODValueTestRecords);

                // 更新计算结果
                const finalWellData = { ...updatedPlateData.wellData };
                calcCutOffValueTestRecords.forEach((testRecord) => {
                    if (testRecord.wellData) {
                        Object.entries(testRecord.wellData).forEach(([wellId, wellData]) => {
                            if (finalWellData[wellId]) {
                                finalWellData[wellId] = {
                                    ...finalWellData[wellId],
                                    odValue: wellData.odValue,
                                    odRatio: wellData.odRatio,
                                    result: wellData.result
                                };
                            }
                        });
                    }
                });

                // 设置最终数据
                setPlateData({
                    ...updatedPlateData,
                    wellData: finalWellData
                });

                // 保存读板数据到数据库
                const isSuccess = saveTestRecordToDb(calcCutOffValueTestRecords);

                if (isSuccess) {
                    toast({
                        title: t('pages:elisaControl.messages.plate.readComplete'), // 读板完成
                        description: t('pages:elisaControl.messages.plate.dataSaved'), // 数据已保存完成，请到结果页面查看数据...
                        status: 'success',
                        duration: 2500,
                        isClosable: true
                    });

                    const mtpNumber = testAdditionalInfo.mtpNumber || '';
                    logger.debug('准备跳转到结果页面', {
                        component: 'ElisaControl',
                        data: {
                            mtpNumber,
                            timestamp: new Date().toISOString()
                        }
                    });

                    // 延迟3秒后跳转
                    setTimeout(() => {
                        // 切换到结果数据标签页（索引为2）
                        const tabsElement = document.querySelector('[role="tablist"]');
                        if (tabsElement) {
                            const resultTab = tabsElement.children[2] as HTMLElement;
                            if (resultTab) {
                                resultTab.click();
                            }
                        }
                    }, 3000);
                } else {
                    toast({
                        title: t('pages:elisaControl.messages.error'), // 错误
                        description: t('pages:elisaControl.messages.plate.saveFailed'), // 保存读板数据失败
                        status: 'error',
                        duration: 3000,
                        isClosable: true
                    });
                }
            } else {
                toast({
                    title: t('pages:elisaControl.messages.warning'), // 警告
                    description: t('pages:elisaControl.messages.serial.noData'), // 未接收到数据
                    status: 'warning',
                    duration: 3000,
                    isClosable: true
                });
            }
        } catch (error) {
            toast.closeAll();
            logger.error('read plate error:', error, {
                component: './src/renderer/src/pages/2_elisacontrol.tsx'
            });

            // 确保串口被关闭
            try {
                await window.customApi.serial.closeSerialPort();
            } catch (closeError) {
                logger.error('close serial port error:', closeError, {
                    component: './src/renderer/src/pages/2_elisacontrol.tsx'
                });
            }

            toast({
                title: t('pages:elisaControl.messages.error'), // 错误
                description:
                    error instanceof Error
                        ? error.message
                        : t('pages:elisaControl.messages.serial.readError'), // 读板过程中发生错误
                status: 'error',
                duration: 5000,
                isClosable: true
            });
        } finally {
            // 重置读板状态
            setIsReadingPlate(false);
        }
    };

    // 处理孔位点击
    const handleWellClick = (row: number, col: number) => {
        const wellId = `${String.fromCharCode(65 + row)}${col + 1}`;

        // 如果样本类型为'sample'，不设置wellData中的sampleType
        if (curSampleType.type === 'sample') {
            return;
            // setCurSampleType({
            //     name: '',
            //     type: 'none'
            // });
        }

        // 将当前选择的样本类型应用到该孔位
        setPlateData((prevPlateData) => {
            const newWellData = {
                ...prevPlateData.wellData,
                [wellId]: {
                    sampleType: curSampleType,
                    sampleNumber: '',
                    odMain: undefined,
                    odRef: undefined,
                    odValue: undefined,
                    odRatio: undefined,
                    concentration: undefined,
                    result: undefined
                }
            };

            return {
                ...prevPlateData,
                wellData: newWellData
            };
        });
    };

    // 处理酶标板数据变化
    const handlePlateDataChange = (newPlateData: PlateData) => {
        setPlateData(newPlateData);

        // 单项目模式下，selectedProjects 只包含 singleProjectAssignments
        if (newPlateData.layoutType === MTPLayoutType.SingleProjectLayout) {
            if (newPlateData.singleProjectAssignments && newPlateData.singleProjectAssignments.id) {
                setSelectedProjects([newPlateData.singleProjectAssignments]);
            } else {
                setSelectedProjects([]);
            }
        } else if (newPlateData.multiProjectAssignments) {
            // 多项目模式下，selectedProjects 包含所有分配的项目
            const assignedProjects = Object.values(
                newPlateData.multiProjectAssignments
            ) as Project[];
            const uniqueProjectIds = [...new Set(assignedProjects.map((p) => p.id))];
            uniqueProjectIds.forEach((projectId) => {
                if (projectId && !selectedProjects.find((p) => p.id === projectId)) {
                    const assignedProject = assignedProjects.find((p) => p.id === projectId);
                    if (assignedProject) {
                        setSelectedProjects((prev) => {
                            const exists = prev.find((p) => p.id === projectId);
                            if (!exists) {
                                return [...prev, assignedProject];
                            }
                            return prev;
                        });
                    }
                }
            });
        }
        console.log('酶标板数据更新:', newPlateData);
    };

    const buttonWidth = '140px';

    return (
        <>
            <Flex
                overflow={'auto'}
                flex={1}
                w="100vw"
                h="calc(100vh - 80px)"
                direction="column"
                gap={2}
                m={0}
                pt={2}
            >
                <Flex flexShrink={0} gap={2}>
                    <SampleTypeForm
                        id="sample-type"
                        initialType={curSampleType}
                        onTypeChange={(type) => {
                            setCurSampleType(type);
                        }}
                    />
                    <ProjectInfoForm
                        id="project-info"
                        layoutType={plateData.layoutType}
                        onLayoutTypeChange={(newLayoutType) => {
                            setPlateData((prev) => ({
                                ...prev,
                                layoutType: newLayoutType
                            }));
                        }}
                        selectedProject={selectedProject}
                        onSelectedProjectChange={setSelectedProject}
                        onSingleProjectChange={(project) => {
                            setPlateData((prev) => ({
                                ...prev,
                                singleProjectAssignments: project
                            }));
                        }}
                        useCommonBlank={useCommonBlank}
                        onUseCommonBlankChange={setUseCommonBlank}
                        refreshKey={projectRefreshKey}
                    />
                    <DetectionInfoForm
                        id="detection-info"
                        selectedProject={selectedProject}
                        testAdditionalInfo={testAdditionalInfo}
                        setTestAdditionalInfo={setTestAdditionalInfo}
                    />
                </Flex>
                <Flex flex={1} direction={'column'}>
                    <ElisaPlateForm
                        data={Array(8)
                            .fill(null)
                            .map(() => Array(12).fill(null))}
                        onWellClick={handleWellClick}
                        layoutType={plateData.layoutType}
                        selectedProject={selectedProject}
                        selectedProjects={selectedProjects}
                        plateData={plateData}
                        onPlateDataChange={handlePlateDataChange}
                        refreshKey={projectRefreshKey}
                        selectedSampleType={curSampleType.type}
                    />

                    {/* 添加拟合面板 */}
                    {/* {getStandardCurveData() && (
                        <ElisaFittingPanel
                            standardData={getStandardCurveData()!}
                            onResultChange={(result) => {
                                console.log('拟合完成:', result);
                            }}
                        />
                    )} */}
                </Flex>

                <Flex
                    flexShrink={0}
                    alignItems={'center'}
                    justifyContent={'center'}
                    // border="1px solid green"
                    gap={6}
                >
                    <Spacer />
                    <Button
                        size="md"
                        w={buttonWidth}
                        colorScheme="blue"
                        onClick={handleReadPlate}
                        isLoading={isReadingPlate}
                        loadingText={t('pages:elisaControl.buttons.reading')} // 读板中...
                        isDisabled={isReadingPlate}
                    >
                        {t('pages:elisaControl.buttons.readPlate')} {/* 读板 */}
                    </Button>

                    <Spacer />
                    <Flex alignItems={'center'} justifyContent={'center'} gap={6}>
                        <Button
                            w={buttonWidth}
                            colorScheme="blue"
                            mr={2}
                            onClick={cleanTemplateNameModal}
                            isDisabled={isReadingPlate}
                        >
                            {t('pages:elisaControl.buttons.clearTemplate')} {/* 清空模板 */}
                        </Button>

                        <Button
                            w={buttonWidth}
                            colorScheme="blue"
                            onClick={openTemplateNameModal}
                            isDisabled={isReadingPlate}
                        >
                            {t('pages:elisaControl.buttons.saveAsTemplate')} {/* 另存为模板 */}
                        </Button>

                        <Select
                            size="lg"
                            colorScheme="blue"
                            w={buttonWidth}
                            placeholder={t('pages:elisaControl.buttons.selectTemplate')} // 选择模板
                            value={selectedTemplateId}
                            onChange={(e) => handleTemplateSelect(e.target.value)}
                            isDisabled={isLoadingTemplates || isReadingPlate}
                        >
                            {templates.map((template) => (
                                <option key={template.id} value={template.id}>
                                    {template.name}
                                </option>
                            ))}
                        </Select>
                        <IconButton
                            aria-label={t('pages:elisaControl.buttons.deleteTemplate')} // 删除模板
                            icon={<DeleteIcon />}
                            colorScheme="red"
                            size="md"
                            onClick={handleDeleteTemplate}
                            isDisabled={!selectedTemplateId || isLoadingTemplates}
                            title={t('pages:elisaControl.buttons.deleteSelectedTemplate')} // 删除选中的模板
                        />
                    </Flex>
                    <Spacer />
                    {/* <Flex align="center" gap={2}>
            <Button mr={2} isDisabled>
                定量参数
            </Button>
            <Button isDisabled>手工拟合</Button>
        </Flex> */}
                    {/* <Button colorScheme="blue" mr={2} onClick={openTemplateModal}>
                建立模板
            </Button> */}
                </Flex>
            </Flex>
            {isTemplateModalOpen && (
                <TemplateModal isOpen={isTemplateModalOpen} onClose={closeTemplateModal} />
            )}
            {isTemplateNameModalOpen && (
                <TemplateNameModal
                    isOpen={isTemplateNameModalOpen}
                    onClose={closeTemplateNameModal}
                    onSubmit={handleTemplateNameSubmit}
                />
            )}

            {/* 删除模板确认对话框 */}
            <DeleteConfirmDialog
                isOpen={isDeleteDialogOpen}
                onClose={onDeleteDialogClose}
                onConfirm={confirmDeleteTemplate}
                itemName={templateToDelete?.name || ''}
                itemIdentifier={''}
                description={t('pages:elisaControl.dialogs.deleteTemplate.description')} // 删除模板后，该模板的所有配置信息将被永久清除，此操作不可撤销。
                isLoading={isDeletingTemplate}
            />
        </>
    );
};

export default ElisaControl;
