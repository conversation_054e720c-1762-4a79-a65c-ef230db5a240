import React, { useState } from 'react';
import { Box, Flex, Table, Thead, Tbody, Tr, Th, Td, Text, Radio, RadioGroup, Button, Stack, TableContainer, useToast, Spinner, Spacer } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';

// 导入组件
import DeleteConfirmDialog from '@renderer/components/DeleteConfirmDialog'; // 删除确认对话框
import CutoffChart from '@renderer/components/CutoffChart'; // Cutoff图表组件
import ElisaPlateDataForm from '@renderer/components/ElisaPlateDataForm'; // 酶标板数据表单
import ResizableSplitPane from '@renderer/components/ResizableSplitPane'; // 可调整大小的分割面板
import RecordQueryForm from '@renderer/components/RecordQueryForm'; // 记录查询表单

// 导入类型定义
import { TestRecord } from '@shared/types/plateData'; // 测试记录类型
import type { PrintRequest } from '@shared/types/print'; // 打印请求类型

// 导入工具函数和钩子
import { formatDateTimeReadable } from '@renderer/utils/dateUtils'; // 日期格式化工具
import logger from '@renderer/utils/logger'; // 日志工具
import { useDeviceFilters } from '@renderer/hooks/useDeviceFilters'; // 设备滤光片钩子
import { languageManager } from '@renderer/i18n'; // 语言管理器

/**
 * 结果数据页面组件
 * 用于显示、查询、导出和管理检测结果数据
 */
const ResultDataPage: React.FC = () => {
    const toast = useToast(); // Chakra UI 提示组件
    const { t } = useTranslation(['common', 'pages']); // 国际化翻译钩子

    // 使用通用的设备滤光片Hook
    const { filters } = useDeviceFilters(true);

    // 数据状态管理
    const [isLoading, setIsLoading] = useState(false); // 数据加载状态
    const [resultShowType, setResultShowType] = useState('result'); // 结果显示类型（结果值/原始吸光值）
    const [currentRecord, setCurrentRecord] = useState<TestRecord>(); // 当前选中的检测记录
    const [filteredRecords, setFilteredRecords] = useState<TestRecord[]>([]); // 过滤后的检测记录列表

    // 各种操作的加载状态
    const [isPrintPreviewLoading, setIsPrintPreviewLoading] = useState(false); // 打印预览加载状态
    const [isPrintLoading, setIsPrintLoading] = useState(false); // 打印加载状态
    const [isPdfLoading, setIsPdfLoading] = useState(false); // PDF导出加载状态
    const [isExcelLoading, setIsExcelLoading] = useState(false); // Excel导出加载状态
    const [isCsvLoading, setIsCsvLoading] = useState(false); // CSV导出加载状态
    const [isLISLoading, setIsLISLoading] = useState(false); // LIS导出加载状态

    // 对话框状态管理
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false); // 删除确认对话框开启状态
    const [isDeleteLoading, setIsDeleteLoading] = useState(false); // 删除操作加载状态

    // 图表状态管理
    const [isCutoffChartOpen, setIsCutoffChartOpen] = useState(false); // Cutoff图表开启状态

    // 处理查询
    const handleQuery = async (params: { startDate: string; endDate: string; mtpNumber: string }) => {
        logger.debug('执行记录查询', {
            component: './src/renderer/src/pages/3_resultdata.tsx',
            data: {
                queryParams: params,
                totalRecords: filteredRecords.length
            }
        });

        try {
            setIsLoading(true);
            let response;

            // 根据查询条件选择不同的查询方式（优先级：微板编号 > 日期范围 > 全部查询）
            if (params.mtpNumber && params.mtpNumber.trim()) {
                // 优先按微板编号查询
                response = await window.customApi.configInfo.getTestRecordByMtpNumber(params.mtpNumber.trim());
            } else if (params.startDate && params.endDate && params.startDate.trim() && params.endDate.trim()) {
                // 其次按日期范围查询
                response = await window.customApi.configInfo.getTestRecordByTestDate(params.startDate.trim(), params.endDate.trim());
            } else {
                // 无查询条件，加载所有记录
                response = await window.customApi.configInfo.getTestRecordList();
            }

            if (response.success && response.data) {
                setFilteredRecords(response.data);
                logger.debug('查询结果', {
                    component: './src/renderer/src/pages/3_resultdata.tsx',
                    data: {
                        filteredCount: response.data.length
                    }
                });
            } else {
                logger.error('查询失败', {
                    component: './src/renderer/src/pages/3_resultdata.tsx',
                    data: { error: '查询失败' }
                });
                toast({
                    title: t('pages:resultData.messages.queryFailed'),
                    description: t('pages:resultData.messages.queryError'),
                    status: 'error',
                    duration: 3000,
                    isClosable: true,
                    position: 'top'
                });
            }
        } catch (error) {
            logger.error('查询异常', {
                component: './src/renderer/src/pages/3_resultdata.tsx',
                data: { error: error instanceof Error ? error.message : '未知错误' }
            });
            toast({
                title: t('pages:resultData.messages.queryFailed'),
                description: error instanceof Error ? error.message : t('common:form.invalid_format'),
                status: 'error',
                duration: 3000,
                isClosable: true,
                position: 'top'
            });
        } finally {
            setIsLoading(false);
        }
    };

    // 处理数据用于排序显示
    const processedRecords = React.useMemo(() => {
        const processed: Array<{
            record: TestRecord;
            isFirstInGroup: boolean;
            isSameAsPrevious: boolean;
        }> = [];

        // 按时间倒序排序
        const sortedRecords = [...filteredRecords].sort((a, b) => b.testDate.getTime() - a.testDate.getTime());

        // 生成处理后的数据
        sortedRecords.forEach((record, index) => {
            const isFirstInGroup = index === 0 || sortedRecords[index - 1].mtpNumber !== record.mtpNumber;

            processed.push({
                record,
                isFirstInGroup,
                isSameAsPrevious: !isFirstInGroup
            });
        });

        return processed;
    }, [filteredRecords]);

    // 记录当前选中记录的变化
    React.useEffect(() => {
        if (currentRecord) {
            logger.debug('当前选中记录已更新', {
                component: './src/renderer/src/pages/3_resultdata.tsx',
                data: {
                    recordId: currentRecord.id,
                    mtpNumber: currentRecord.mtpNumber,
                    projectName: currentRecord.testProject?.name
                }
            });
        }
    }, [currentRecord]);

    // 处理打印预览
    const handlePrintPreview = async () => {
        logger.debug('打印预览功能被调用', {
            component: './src/renderer/src/pages/3_resultdata.tsx',
            data: {
                hasCurrentRecord: !!currentRecord,
                recordId: currentRecord?.id,
                mtpNumber: currentRecord?.mtpNumber
            }
        });

        if (!currentRecord) {
            toast({
                title: t('pages:resultData.messages.selectRecord'),
                status: 'warning',
                duration: 2000,
                isClosable: true,
                position: 'top'
            });
            return;
        }

        // 设置加载状态
        setIsPrintPreviewLoading(true);

        try {
            // 准备打印请求数据
            const printRequest: PrintRequest = {
                testRecord: currentRecord,
                printDate: formatDateTimeReadable(new Date()),
                options: {
                    language: languageManager.getCurrentLanguage(), // 从用户设置获取
                    printType: 'quantitative', // 根据项目类型确定
                    includePlateData: true,
                    includeStatistics: true,
                    dataType: 'od' // 默认显示OD值
                }
            };

            // 使用新的打印预览API
            const result = await window.customApi.print.printPreview(printRequest);

            if (result.success) {
                toast({
                    title: t('pages:resultData.messages.printPreviewOpened'),
                    description: t('pages:resultData.messages.printFailedDescription'),
                    status: 'success',
                    duration: 4000,
                    isClosable: true,
                    position: 'top'
                });
            } else {
                toast({
                    title: t('pages:resultData.messages.printPreviewOpenFailed'),
                    description: result.message,
                    status: 'error',
                    duration: 3000,
                    isClosable: true,
                    position: 'top'
                });
            }
        } catch (error) {
            logger.error('打印预览失败:', error, {
                component: './src/renderer/src/pages/3_resultdata.tsx'
            });

            toast({
                title: t('pages:resultData.messages.printPreviewFailed'),
                description: error instanceof Error ? error.message : t('common:form.invalid_format'),
                status: 'error',
                duration: 3000,
                isClosable: true,
                position: 'top'
            });
        } finally {
            // 清除加载状态
            setIsPrintPreviewLoading(false);
        }
    };

    // 处理打印
    const handlePrint = async () => {
        logger.debug('打印功能被调用', {
            component: './src/renderer/src/pages/3_resultdata.tsx',
            data: {
                hasCurrentRecord: !!currentRecord,
                recordId: currentRecord?.id,
                mtpNumber: currentRecord?.mtpNumber
            }
        });

        if (!currentRecord) {
            toast({
                title: t('pages:resultData.messages.selectRecord'),
                status: 'warning',
                duration: 2000,
                isClosable: true,
                position: 'top'
            });
            return;
        }

        // 设置加载状态
        setIsPrintLoading(true);

        try {
            // 准备打印请求数据
            const printRequest: PrintRequest = {
                testRecord: currentRecord,
                printDate: formatDateTimeReadable(),
                options: {
                    language: languageManager.getCurrentLanguage(),
                    printType: 'quantitative',
                    includePlateData: true,
                    includeStatistics: true,
                    dataType: 'od'
                }
            };

            // 使用新的打印API
            const result = await window.customApi.print.print(printRequest, {
                silent: false,
                printBackground: true,
                color: true,
                margins: { marginType: 'printableArea' },
                landscape: false,
                pagesPerSheet: 1,
                collate: false,
                copies: 1
                // header: 'ELISA检测报告',
                // footer: '第 {page} 页，共 {totalPages} 页'
            });

            if (result.success) {
                toast({
                    title: t('pages:resultData.messages.printSuccess'),
                    status: 'success',
                    duration: 2000,
                    isClosable: true,
                    position: 'top'
                });
            } else {
                toast({
                    title: t('pages:resultData.messages.printFailed'),
                    description: result.message,
                    status: 'error',
                    duration: 3000,
                    isClosable: true,
                    position: 'top'
                });
            }
        } catch (error) {
            logger.error('打印失败:', error, {
                component: './src/renderer/src/pages/3_resultdata.tsx'
            });

            toast({
                title: t('pages:resultData.messages.printFailed'),
                description: error instanceof Error ? error.message : t('common:form.invalid_format'),
                status: 'error',
                duration: 3000,
                isClosable: true,
                position: 'top'
            });
        } finally {
            // 清除加载状态
            setIsPrintLoading(false);
        }
    };

    // 处理PDF生成（使用新的Electron API）
    const handleGeneratePdf = async () => {
        if (!currentRecord) {
            toast({
                title: t('pages:resultData.messages.selectRecord'),
                status: 'warning',
                duration: 2000,
                isClosable: true,
                position: 'top'
            });
            return;
        }

        // 设置加载状态
        setIsPdfLoading(true);

        try {
            // 准备打印请求数据
            const printRequest: PrintRequest = {
                testRecord: currentRecord,
                printDate: formatDateTimeReadable(),
                options: {
                    language: languageManager.getCurrentLanguage(),
                    printType: 'quantitative',
                    includePlateData: true,
                    includeStatistics: true,
                    dataType: 'od'
                }
            };

            // 使用新的PDF生成API
            const result = await window.customApi.print.printToPdf(printRequest, {
                printBackground: true,
                // color: true,
                margins: { marginType: 'printableArea' },
                landscape: false,
                pageSize: 'A4'
            });

            if (result.success) {
                toast({
                    title: t('pages:resultData.messages.generatePdfSuccess'),
                    status: 'success',
                    duration: 2000,
                    isClosable: true,
                    position: 'top'
                });
            } else {
                toast({
                    title: t('pages:resultData.messages.generatePdfFailed'),
                    description: result.message,
                    status: 'error',
                    duration: 3000,
                    isClosable: true,
                    position: 'top'
                });
            }
        } catch (error) {
            logger.error('PDF creation failed:', error, {
                component: './src/renderer/src/pages/3_resultdata.tsx'
            });

            toast({
                title: t('pages:resultData.messages.exportPdfFailed'),
                description: error instanceof Error ? error.message : t('common:form.invalid_format'),
                status: 'error',
                duration: 3000,
                isClosable: true,
                position: 'top'
            });
        } finally {
            // 清除加载状态
            setIsPdfLoading(false);
        }
    };

    // 处理Excel导出
    const handleExportExcel = async () => {
        if (!currentRecord) {
            toast({
                title: t('pages:resultData.messages.selectRecord'),
                status: 'warning',
                duration: 2000,
                isClosable: true,
                position: 'top'
            });
            return;
        }

        // 设置加载状态
        setIsExcelLoading(true);

        try {
            // 准备打印请求数据
            const printRequest: PrintRequest = {
                testRecord: currentRecord,
                printDate: formatDateTimeReadable(),
                options: {
                    language: languageManager.getCurrentLanguage(),
                    printType: 'quantitative',
                    includePlateData: true,
                    includeStatistics: true,
                    dataType: 'od'
                }
            };

            // 使用新的Excel导出API
            const result = await window.customApi.print.exportToExcel(printRequest);

            if (result.success) {
                toast({
                    title: t('pages:resultData.messages.exportExcelSuccess'),
                    status: 'success',
                    duration: 2000,
                    isClosable: true,
                    position: 'top'
                });
            } else {
                toast({
                    title: t('pages:resultData.messages.exportExcelFailed'),
                    description: result.message,
                    status: 'error',
                    duration: 3000,
                    isClosable: true,
                    position: 'top'
                });
            }
        } catch (error) {
            logger.error('Excel export failed:', error, {
                component: './src/renderer/src/pages/3_resultdata.tsx'
            });

            toast({
                title: t('pages:resultData.messages.exportExcelFailed'),
                description: error instanceof Error ? error.message : t('common:form.invalid_format'),
                status: 'error',
                duration: 3000,
                isClosable: true,
                position: 'top'
            });
        } finally {
            // 清除加载状态
            setIsExcelLoading(false);
        }
    };

    const handleExportCsv = async () => {
        logger.debug('CSV导出功能被调用', {
            component: './src/renderer/src/pages/3_resultdata.tsx',
            data: {
                hasCurrentRecord: !!currentRecord,
                recordId: currentRecord?.id,
                mtpNumber: currentRecord?.mtpNumber
            }
        });

        if (!currentRecord) {
            toast({
                title: t('pages:resultData.messages.selectRecord'),
                status: 'warning',
                duration: 2000,
                isClosable: true,
                position: 'top'
            });
            return;
        }

        // 设置加载状态
        setIsCsvLoading(true);

        try {
            // 准备打印请求数据
            const printRequest: PrintRequest = {
                testRecord: currentRecord,
                printDate: formatDateTimeReadable(),
                options: {
                    language: languageManager.getCurrentLanguage(),
                    printType: 'quantitative',
                    includePlateData: true,
                    includeStatistics: true,
                    dataType: 'od'
                }
            };

            // 使用新的CSV导出API
            const result = await window.customApi.print.exportToCsv(printRequest);

            if (result.success) {
                toast({
                    title: t('pages:resultData.messages.exportCsvSuccess'),
                    status: 'success',
                    duration: 2000,
                    isClosable: true,
                    position: 'top'
                });
            } else {
                toast({
                    title: t('pages:resultData.messages.exportCsvFailed'),
                    description: result.message,
                    status: 'error',
                    duration: 3000,
                    isClosable: true,
                    position: 'top'
                });
            }
        } catch (error) {
            logger.error('CSV export failed:', error, {
                component: './src/renderer/src/pages/3_resultdata.tsx'
            });

            toast({
                title: t('pages:resultData.messages.exportCsvFailed'),
                description: error instanceof Error ? error.message : t('common:form.invalid_format'),
                status: 'error',
                duration: 3000,
                isClosable: true,
                position: 'top'
            });
        } finally {
            // 清除加载状态
            setIsCsvLoading(false);
        }
    };

    // 处理删除按钮点击
    const handleDeleteClick = () => {
        if (!currentRecord) {
            toast({
                title: t('pages:resultData.messages.selectRecord'),
                status: 'warning',
                duration: 2000,
                isClosable: true,
                position: 'top'
            });
            return;
        }
        setIsDeleteDialogOpen(true);
    };

    // 处理删除确认
    const handleDeleteConfirm = async () => {
        if (!currentRecord) return;

        setIsDeleteLoading(true);

        try {
            logger.debug('删除检测记录', {
                component: './src/renderer/src/pages/3_resultdata.tsx',
                data: {
                    recordId: currentRecord.id,
                    mtpNumber: currentRecord.mtpNumber,
                    projectName: currentRecord.testProject?.name
                }
            });

            const response = await window.customApi.configInfo.deleteTestRecord(currentRecord.id);

            if (response.success) {
                toast({
                    title: t('pages:resultData.messages.deleteSuccess'),
                    description: t('pages:resultData.messages.deleteSuccessDescription', {
                        mtpNumber: currentRecord.mtpNumber
                    }),
                    status: 'success',
                    duration: 3000,
                    isClosable: true,
                    position: 'top'
                });

                // 清除当前选中的记录
                setCurrentRecord(undefined);

                // 从列表中移除已删除的记录
                setFilteredRecords((prev) => prev.filter((record) => record.id !== currentRecord.id));

                logger.debug('删除成功', {
                    component: './src/renderer/src/pages/3_resultdata.tsx',
                    data: {
                        deletedRecordId: currentRecord.id,
                        remainingRecords: filteredRecords.length - 1
                    }
                });
            } else {
                toast({
                    title: t('pages:resultData.messages.deleteFailed'),
                    description: response.error || t('pages:resultData.messages.queryError'),
                    status: 'error',
                    duration: 3000,
                    isClosable: true,
                    position: 'top'
                });

                logger.error('删除失败', {
                    component: './src/renderer/src/pages/3_resultdata.tsx',
                    data: { error: response.error }
                });
            }
        } catch (error) {
            logger.error('delete record failed:', error, {
                data: { error: error instanceof Error ? error.message : 'unknown error' },
                component: './src/renderer/src/pages/3_resultdata.tsx'
            });
            toast({
                title: t('pages:resultData.messages.deleteFailed'),
                description: error instanceof Error ? error.message : t('common:form.invalid_format'),
                status: 'error',
                duration: 3000,
                isClosable: true,
                position: 'top'
            });

            logger.error('删除异常', {
                component: './src/renderer/src/pages/3_resultdata.tsx',
                data: { error: error instanceof Error ? error.message : '未知错误' }
            });
        } finally {
            setIsDeleteLoading(false);
            setIsDeleteDialogOpen(false);
        }
    };

    // 处理删除取消
    const handleDeleteCancel = () => {
        setIsDeleteDialogOpen(false);
    };

    // 处理Cutoff 显示chat

    const handleCutoffPlot = () => {
        if (!currentRecord) {
            toast({
                title: t('pages:resultData.messages.selectRecord'),
                status: 'warning',
                duration: 3000,
                isClosable: true
            });
            return;
        }

        if (!currentRecord.wellData || Object.keys(currentRecord.wellData).length === 0) {
            toast({
                title: t('pages:resultData.messages.noDataInRecord'),
                status: 'warning',
                duration: 2000,
                isClosable: true
            });
            return;
        }

        setIsCutoffChartOpen(true);
    };

    /**
     * 处理LIS导出功能
     */
    const handleExportLIS = async () => {
        // // 调试：检查API是否可用
        // console.log('window.customApi:', window.customApi);
        // console.log('window.customApi.print:', window.customApi?.print);
        // console.log('window.customApi.print.exportToLIS:', window.customApi?.print?.exportToLIS);

        if (!currentRecord) {
            toast({
                title: t('pages:resultData.messages.selectRecord'),
                status: 'warning',
                duration: 3000,
                isClosable: true
            });
            return;
        }

        setIsLISLoading(true);
        try {
            const printRequest: PrintRequest = {
                testRecord: currentRecord,
                printDate: formatDateTimeReadable(),
                options: {
                    language: languageManager.getCurrentLanguage(),
                    printType: 'qualitative',
                    includePlateData: true,
                    includeStatistics: true,
                    dataType: 'od'
                }
            };

            const result = await window.customApi.print.exportToLIS(printRequest);

            if (result.success) {
                toast({
                    title: t('pages:resultData.messages.exportLisSuccess'),
                    description: result.data?.filePath && result.data.filePath.length > 0 ? t('pages:resultData.messages.exportLisSuccessDescription') + result.data.filePath : '',
                    status: 'success',
                    duration: 5000,
                    isClosable: true
                });
            } else {
                toast({
                    title: t('pages:resultData.messages.exportLisFailed'),
                    description: result.message || t('common:form.invalid_format'),
                    status: 'error',
                    duration: 5000,
                    isClosable: true
                });
            }
        } catch (error) {
            logger.error('LIS export failed:', error, {
                component: './src/renderer/src/pages/3_resultdata.tsx'
            });

            toast({
                title: t('pages:resultData.messages.exportLisFailed'),
                description: error instanceof Error ? error.message : t('common:form.invalid_format'),
                status: 'error',
                duration: 5000,
                isClosable: true
            });
        } finally {
            setIsLISLoading(false);
        }
    };

    return (
        <>
            <ResizableSplitPane
                defaultLeftWidth={400}
                minLeftWidth={80}
                maxLeftWidth={800}
                showSplitterIcon={true}
                left={
                    <Flex
                        p={0}
                        m={0}
                        h="100vh" // 明确设置左侧高度
                        bg="white"
                        borderRight="1px"
                        borderColor="gray.200"
                        direction="column"
                    >
                        {/* 查询表单 */}
                        <Box m={0} p={0} flexShrink={0}>
                            <RecordQueryForm onQuery={handleQuery} />
                        </Box>

                        <TableContainer
                            flex={1} // 占据剩余空间
                            minH={0} // 允许收缩
                            w="100%"
                            overflowY="auto"
                            p={0}
                            m={0}
                            position="relative"
                        >
                            {isLoading && (
                                <Flex position="absolute" top="50%" left="50%" transform="translate(-50%, -50%)" zIndex={10} bg="white" p={4} borderRadius="md" boxShadow="md">
                                    <Spinner size="md" mr={3} />
                                    <Text>{t('pages:resultData.table.loading')}</Text>
                                </Flex>
                            )}
                            <Table
                                p={0}
                                m={0}
                                border="1px"
                                borderColor="gray.200"
                                size="sm"
                                sx={{
                                    borderSpacing: '0 4px',
                                    borderCollapse: 'separate'
                                }}
                            >
                                <Thead>
                                    <Tr>
                                        <Th
                                            fontFamily="MiSans-Bold"
                                            // fontSize="md"
                                            textAlign="center"
                                            whiteSpace="nowrap"
                                            overflow="hidden"
                                            textOverflow="ellipsis"
                                        >
                                            {t('pages:resultData.table.columns.mtpNumber')}
                                        </Th>
                                        <Th
                                            fontFamily="MiSans-Bold"
                                            // fontSize="md"
                                            textAlign="center"
                                            whiteSpace="nowrap"
                                            overflow="hidden"
                                            textOverflow="ellipsis"
                                        >
                                            {t('pages:resultData.table.columns.project_name')}
                                        </Th>
                                        <Th
                                            fontFamily="MiSans-Bold"
                                            // fontSize="md"
                                            textAlign="center"
                                            whiteSpace="nowrap"
                                            overflow="hidden"
                                            textOverflow="ellipsis"
                                        >
                                            {t('pages:resultData.table.columns.date')}
                                        </Th>
                                    </Tr>
                                </Thead>
                                <Tbody>
                                    {processedRecords.length === 0 && !isLoading ? (
                                        <Tr>
                                            <Td colSpan={3} textAlign="center" py={20}>
                                                <Text fontSize="md" color="gray.500" fontFamily="MiSans-Normal">
                                                    {filteredRecords.length === 0 ? t('pages:resultData.table.empty') : t('pages:resultData.table.noMatch')}
                                                </Text>
                                            </Td>
                                        </Tr>
                                    ) : (
                                        processedRecords.map((item) => (
                                            <Tr
                                                key={item.record.id}
                                                cursor="pointer"
                                                height="auto"
                                                p={0}
                                                m={0}
                                                _hover={{
                                                    bg: 'blue.50',
                                                    zIndex: 1
                                                }}
                                                onClick={() => setCurrentRecord(item.record)}
                                                bg={currentRecord?.id === item.record.id ? 'blue.100' : undefined}
                                            >
                                                <Td
                                                    flex="1"
                                                    fontSize={'sm'}
                                                    fontFamily="MiSans-Normal"
                                                    textAlign="left"
                                                    wordBreak="break-all"
                                                    whiteSpace="normal"
                                                    maxW="45%"
                                                    color={item.isFirstInGroup ? 'black' : 'transparent'}
                                                    bg={item.isFirstInGroup ? 'transparent' : 'transparent'}
                                                    _hover={{
                                                        border: '1px solid',
                                                        borderColor: 'blue.500',
                                                        borderRadius: 'md'
                                                    }}
                                                >
                                                    {item.record.mtpNumber}
                                                </Td>
                                                <Td
                                                    w="35%"
                                                    fontFamily="MiSans-Normal"
                                                    textAlign="left"
                                                    _hover={{
                                                        border: '1px solid',
                                                        borderColor: 'blue.500',
                                                        borderRadius: 'md'
                                                    }}
                                                >
                                                    <Text
                                                        // fontWeight="bold"
                                                        fontSize="md"
                                                        fontFamily="MiSans-Normal"
                                                    >
                                                        {item.record.testProject.name}
                                                    </Text>
                                                    <Text fontSize="sm" fontFamily="MiSans-Normal">
                                                        {`( ${item.record.testProject.code} )`}
                                                    </Text>
                                                </Td>
                                                <Td
                                                    w="8%"
                                                    fontSize={'sm'}
                                                    fontFamily="MiSans-Normal"
                                                    textAlign="left"
                                                    _hover={{
                                                        border: '1px solid',
                                                        borderColor: 'blue.500',
                                                        borderRadius: 'md'
                                                    }}
                                                >
                                                    <Text>
                                                        {item.record.testDate.toLocaleString('zh-CN', {
                                                            year: 'numeric',
                                                            month: '2-digit',
                                                            day: '2-digit',
                                                            hour: '2-digit',
                                                            minute: '2-digit',
                                                            second: '2-digit',
                                                            hour12: false
                                                        })}
                                                    </Text>
                                                </Td>
                                            </Tr>
                                        ))
                                    )}
                                </Tbody>
                            </Table>
                        </TableContainer>
                    </Flex>
                }
                right={
                    <Flex
                        direction="column"
                        flex={1}
                        p={2}
                        minW={0}
                        minH={0}
                        // h="100vh" // 明确设置高度
                        h="calc(100vh - 20px)" // 减去padding
                        // maxH="100vh" // 限制最大高度
                        w={'100%'}
                        borderRadius="lg"
                        boxShadow="md"
                        overflow="hidden" // 改为 hidden，让子元素处理滚动
                        gap={2}
                        // border={'1px solid'}
                    >
                        {/* 配置参数
                    <Box mb={4} flexShrink={0}>
                        <Text fontSize="md" fontFamily="MiSans-Bold" mb={2}>
                            检测配置参数：
                        </Text>
                        <Text fontSize="sm" fontFamily="MiSans-Normal">
                            检测方法：定量法 | 波长：450nm/630nm | 空白孔：A1,A2 | 标准孔：B1-B7 |
                            质控孔：C1,C2 | 样本数：48
                        </Text>
                    </Box>
                    <Divider mb={4} flexShrink={0} /> */}

                        {/* 板号和数据显示选择 - 固定区域 */}
                        <Flex
                            gap={4}
                            m={2}
                            mt={4}
                            pt={4}
                            alignItems="center"
                            justifyContent="space-between"
                            flexShrink={0} // 保持不收缩
                        >
                            <Text pl={6} fontSize="md" fontFamily="MiSans-Bold" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
                                {/* 当前板号 */}
                                {t('pages:resultData.details.currentPlate')}：{currentRecord?.mtpNumber || ''}
                            </Text>
                            <RadioGroup pr={6} onChange={setResultShowType} value={resultShowType}>
                                <Stack direction="row">
                                    <Radio value="result">{t('pages:resultData.details.resultType.result')} </Radio>
                                    <Radio value="original">{t('pages:resultData.details.resultType.original')}</Radio>
                                    {/* <Radio value="sco">S/CO值</Radio> */}
                                </Stack>
                            </RadioGroup>
                        </Flex>

                        {/* 板信息展示控件 - 固定区域 */}
                        <Box
                            mx={10}
                            my={0}
                            py={0}
                            border="1px solid"
                            borderColor="gray.200"
                            borderRadius="md"
                            bg="white"
                            w="93%"
                            h="auto"
                            flexShrink={0} // 保持不收缩
                        >
                            <Table
                                m={0}
                                p={0}
                                size="sm"
                                sx={{
                                    borderSpacing: '0 2px',
                                    borderCollapse: 'separate'
                                }}
                            >
                                <Tbody>
                                    <Tr h={'30px'}>
                                        <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'} p={0}>
                                            {/* 项目名称 */}
                                            {t('pages:resultData.details.projectName')}
                                        </Td>
                                        <Td w={'20%'}>{currentRecord?.testProject.name}</Td>
                                        <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'} p={0}>
                                            {/* 项目代码 */}
                                            {t('pages:resultData.details.projectCode')}
                                        </Td>
                                        <Td w={'15%'}>{currentRecord?.testProject.code}</Td>
                                        <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'} p={0}>
                                            {/* 主波长 */}
                                            {t('pages:resultData.details.mainWavelength')}
                                        </Td>
                                        <Td w={'15%'}>{filters.find((f) => f.no === currentRecord?.testProject.testWaveIdx)?.wavelength || ''}</Td>
                                        <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'} p={0}>
                                            {/* 参考波长 */}
                                            {t('pages:resultData.details.refWavelength')}
                                        </Td>
                                        <Td w={'15%'}>{filters.find((f) => f.no === currentRecord?.testProject.refWaveIdx)?.wavelength || ''}</Td>
                                    </Tr>

                                    <Tr h={'30px'}>
                                        <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'} p={0}>
                                            {/* 阳性公式 */}
                                            {t('pages:resultData.details.positiveFormula')}
                                        </Td>
                                        <Td w={'20%'}>{`${currentRecord?.testProject.postiveJudge}  ${currentRecord?.testProject.cutOffFormula}`}</Td>
                                        <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'} p={0}>
                                            {/* Cutoff值 */}
                                            {t('pages:resultData.details.cutoffValue')}
                                        </Td>
                                        <Td w={'15%'}>{currentRecord?.cutOffValue.toFixed(4)}</Td>

                                        <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'} p={0}>
                                            {/* 试剂批号 */}
                                            {t('pages:resultData.details.reagentBatch')}
                                        </Td>
                                        <Td w={'15%'}>{currentRecord?.testAdditionalInfo.reagentBatch}</Td>

                                        <Td borderRadius={'md'} w={'100px'} bg={'gray.100'} textAlign={'center'} p={0}>
                                            {/* 试剂供应商 */}
                                            {t('pages:resultData.details.reagentSupplier')}
                                        </Td>
                                        <Td w={'15%'}>{currentRecord?.testAdditionalInfo.reagentSupplier}</Td>
                                    </Tr>
                                </Tbody>
                            </Table>
                        </Box>

                        {/* 96孔板显示 - 可滚动区域 */}
                        <Box
                            flex={1}
                            overflow="auto"
                            minH={0}
                            maxH="calc(100vh - 300px)" // 减去固定区域的高度
                        >
                            <ElisaPlateDataForm isReadOnly={true} resultShowType={resultShowType as 'result' | 'original'} wellData={currentRecord?.wellData || {}} />
                        </Box>

                        {/* 操作按钮区 - 固定区域 */}
                        <Flex
                            pt={0}
                            pb={2}
                            pr={4}
                            pl={4}
                            m={0}
                            gap={2}
                            wrap="nowrap"
                            flexShrink={0} // 保持不收缩
                            justify="flex-start"
                            overflow="auto"
                        >
                            {/* <Button size="sm" colorScheme="gray">
                                灰区阳转阴
                            </Button>
                            <Button size="sm" colorScheme="gray">
                                灰区阴转阳
                            </Button> */}

                            <Button
                                size="md"
                                colorScheme="blue"
                                onClick={handlePrintPreview}
                                isLoading={isPrintPreviewLoading}
                                loadingText={t('pages:resultData.loadingText.generating')}
                                spinner={<Spinner size="sm" />}
                                disabled={isPrintPreviewLoading || isPrintLoading || isPdfLoading}
                            >
                                {t('pages:resultData.buttons.printPreview')}
                            </Button>
                            <Button
                                size="md"
                                colorScheme="blue"
                                onClick={handlePrint}
                                isLoading={isPrintLoading}
                                loadingText={t('pages:resultData.loadingText.printing')}
                                spinner={<Spinner size="sm" />}
                                disabled={isPrintPreviewLoading || isPrintLoading || isPdfLoading}
                            >
                                {t('pages:resultData.buttons.printReport')}
                            </Button>
                            <Button
                                size="md"
                                colorScheme="green"
                                onClick={handleGeneratePdf}
                                isLoading={isPdfLoading}
                                loadingText={t('pages:resultData.loadingText.generating')}
                                spinner={<Spinner size="sm" />}
                                disabled={isPrintPreviewLoading || isPrintLoading || isPdfLoading}
                            >
                                {t('pages:resultData.buttons.exportPdf')}
                            </Button>

                            <Button
                                size="md"
                                colorScheme="green"
                                onClick={handleExportExcel}
                                isLoading={isExcelLoading}
                                loadingText={t('pages:resultData.loadingText.exporting')}
                                spinner={<Spinner size="sm" />}
                                disabled={isPrintPreviewLoading || isPrintLoading || isPdfLoading || isExcelLoading}
                            >
                                {t('pages:resultData.buttons.exportExcel')}
                            </Button>
                            <Button
                                size="md"
                                colorScheme="green"
                                onClick={handleExportCsv}
                                isLoading={isCsvLoading}
                                loadingText={t('pages:resultData.loadingText.exporting')}
                                spinner={<Spinner size="sm" />}
                                disabled={isPrintPreviewLoading || isPrintLoading || isPdfLoading || isExcelLoading}
                            >
                                {t('pages:resultData.buttons.exportCsv')}
                            </Button>
                            <Spacer />
                            <Button size="md" colorScheme="orange" onClick={handleCutoffPlot}>
                                {t('pages:resultData.buttons.cutoffChart')}
                            </Button>

                            {/* <Spacer /> */}
                            <Button
                                size="md"
                                colorScheme="purple"
                                onClick={handleExportLIS}
                                isLoading={isLISLoading}
                                loadingText={t('pages:resultData.loadingText.exporting')}
                                spinner={<Spinner size="sm" />}
                                disabled={isPrintPreviewLoading || isPrintLoading || isPdfLoading || isExcelLoading || isCsvLoading || isLISLoading || !currentRecord}
                            >
                                {t('pages:resultData.buttons.exportLis')}
                            </Button>

                            <Spacer />
                            <Button size="md" colorScheme="red" onClick={handleDeleteClick} disabled={!currentRecord}>
                                {t('pages:resultData.buttons.deleteData')}
                            </Button>
                        </Flex>
                    </Flex>
                }
            />

            {/* 删除确认对话框 */}
            <DeleteConfirmDialog
                isOpen={isDeleteDialogOpen}
                onClose={handleDeleteCancel}
                onConfirm={handleDeleteConfirm}
                itemName={currentRecord?.testProject?.name || ''}
                itemIdentifier={currentRecord?.mtpNumber || ''}
                isLoading={isDeleteLoading}
            />

            {/* Cutoff图表 */}
            <CutoffChart
                isOpen={isCutoffChartOpen}
                onClose={() => setIsCutoffChartOpen(false)}
                wellData={currentRecord?.wellData || {}}
                cutOffValue={currentRecord?.cutOffValue || 0.1}
                mtpNumber={currentRecord?.mtpNumber}
            />
        </>
    );
};

export default ResultDataPage;
