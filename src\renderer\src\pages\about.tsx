import React, { useEffect, useState } from 'react';
import {
    <PERSON>dal,
    ModalOverlay,
    Modal<PERSON>ontent,
    Modal<PERSON>eader,
    <PERSON>dal<PERSON>ooter,
    ModalBody,
    ModalCloseButton,
    HStack,
    Icon,
    Text
} from '@chakra-ui/react';

import { InfoIcon } from '@chakra-ui/icons';
import { useTranslation } from 'react-i18next';
import logger from '../utils/logger';

interface AboutProps {
    isOpen: boolean;
    onClose: () => void;
}

const About: React.FC<AboutProps> = ({ isOpen, onClose }) => {
    const [version, setVersion] = useState<string>('');
    const { t } = useTranslation(['common']);

    useEffect(() => {
        const getVersion = async (): Promise<void> => {
            setVersion(t('common:loading.loading'));
            try {
                const appVersion = await window.customApi.app.getAppVersion();
                logger.info('getVersion success:', {
                    data: appVersion,
                    component: './src/renderer/src/pages/about.tsx'
                });
                setVersion(appVersion);
            } catch (error) {
                logger.error('getVersion failed:', error, {
                    component: './src/renderer/src/pages/about.tsx'
                });
            }
        };
        getVersion();
    }, []);

    return (
        <Modal
            onClose={onClose}
            isOpen={isOpen}
            // closeOnOverlayClick={false}
            // closeOnEsc={false}
            isCentered
        >
            <ModalOverlay />
            <ModalContent>
                <ModalHeader fontFamily="MiSans-Bold" bg="teal.500" color="white">
                    <HStack>
                        <Icon as={InfoIcon} boxSize={6} />
                        <span>{t('common:about')}</span>
                    </HStack>
                </ModalHeader>

                {/* 程序名称 */}
                <ModalHeader w="100%">{t('common:appName')}</ModalHeader>

                {/* 关闭按钮 */}
                <ModalCloseButton />
                <ModalBody>
                    <Text fontFamily="MiSans-Normal">
                        {/* 版本号 */}
                        {t('common:label.version')}
                        {version}
                    </Text>
                </ModalBody>
                <ModalFooter>
                    {/* 关闭按钮 */}
                    {/* <Button onClick={onClose}>{t('common:button.close')}</Button> */}
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
};

export default About;
