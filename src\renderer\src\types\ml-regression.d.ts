declare module 'ml-regression' {
    interface ScoreResult {
        chi2: number;
        r: number;
        r2: number;
        rmsd: number;
    }

    export class SimpleLinearRegression {
        constructor(x: number[], y: number[]);
        predict(x: number): number;
        score(x: number[], y: number[]): ScoreResult;
        slope: number;
        intercept: number;
    }

    export class ExponentialRegression {
        constructor(x: number[], y: number[]);
        predict(x: number): number;
        score(x: number[], y: number[]): ScoreResult;
        slope: number;
        intercept: number;
    }

    export class PolynomialRegression {
        constructor(x: number[], y: number[], degree: number);
        predict(x: number): number;
        score(x: number[], y: number[]): ScoreResult;
        coefficients: number[];
        degree: number;
    }
}
