/**
 * 时间格式化工具函数
 */

/**
 * 将日期格式化为 YYYYMMDDHHMMSS 格式
 * @param date 日期对象，默认为当前时间
 * @returns 格式化后的时间字符串，如 "20231225143022"
 */
export const formatDateTime = (date: Date = new Date()): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day}_${hours}${minutes}${seconds}`;
};

/**
 * 将日期格式化为 YYYY-MM-DD HH:MM:SS 格式
 * @param date 日期对象，默认为当前时间
 * @returns 格式化后的时间字符串，如 "2023/12/25 14:30:22"
 */
export const formatDateTimeReadable = (date: Date = new Date()): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 将日期格式化为 YYYY-MM-DD 格式
 * @param date 日期对象，默认为当前时间
 * @returns 格式化后的日期字符串，如 "2023-12-25"
 */
export const formatDate = (date: Date = new Date()): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
};

/**
 * 将日期格式化为 HH:MM:SS 格式
 * @param date 日期对象，默认为当前时间
 * @returns 格式化后的时间字符串，如 "14:30:22"
 */
export const formatTime = (date: Date = new Date()): string => {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${hours}:${minutes}:${seconds}`;
};

// /**
//  * 将日期格式化为 ISO 字符串
//  * @param date 日期对象，默认为当前时间
//  * @returns 格式化后的 ISO 字符串，如 "2023-12-25T14:30:22.000Z"
//  */
// export const formatIsoString = (date: Date = new Date()): string => {
//     console.log('date', date);
//     console.log('date.toISOString()', date.toISOString());
//     const isoString = date.toISOString().substring(0, 19);
//     console.log('isoString', isoString);
//     const timestamp = isoString.replace('T', '_').replaceAll(':', '').replaceAll('-', '_');
//     console.log('timestamp', timestamp);
//     return timestamp;
// };

/**
 * 生成带项目代码的时间戳
 * @param projectCode 项目代码
 * @param date 日期对象，默认为当前时间
 * @returns 格式化后的时间戳，如 "20231225143022_PROJ001"
 */
export const generateProjectTimestamp = (projectCode: string, date: Date = new Date()): string => {
    const timestamp = formatDateTime(date);
    return `${timestamp}_${projectCode}`;
};
