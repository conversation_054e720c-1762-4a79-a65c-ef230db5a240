import { PlateData, TestRecord, WellData, TestAdditionalInfo } from '@shared/types';
import { MTPLayoutType } from '@shared/commondefines';
import * as math from 'mathjs';
import logger from './logger';

export const convertToTestRecord = (
    plateData: PlateData,
    testAdditionalInfo: TestAdditionalInfo
): TestRecord[] => {
    // console.log('convertToTestRecord', plateData);
    const testRecords: TestRecord[] = [];
    // 创建 wellData 的深拷贝，避免修改原始数据
    const wellData = JSON.parse(JSON.stringify(plateData.wellData)) as Record<string, WellData>;

    const now = new Date();

    if (plateData.layoutType === MTPLayoutType.SingleProjectLayout) {
        // 单项目布局：返回一条记录
        if (plateData.singleProjectAssignments && plateData.singleProjectAssignments.id) {
            const testRecord: TestRecord = {
                id: now.toISOString(),
                mtpNumber: testAdditionalInfo.mtpNumber || '',
                testDate: now,
                updateDate: now,
                cutOffValue: 0, // 默认截断值
                testProject: plateData.singleProjectAssignments,
                testAdditionalInfo: testAdditionalInfo,
                wellData: wellData // 使用深拷贝的数据
            };
            testRecords.push(testRecord);
        }
    } else {
        // 多项目布局：按项目合并数据
        // 使用Map存储项目和其对应的行号或列号
        const projectLayoutMap = new Map<string, string[]>();

        // 根据布局类型确定有效的键范围
        const validKeys =
            plateData.layoutType === MTPLayoutType.MultiProjectHorizontalLayout
                ? ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'] // 横排布局：只使用行号 A-H
                : ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']; // 竖排布局：只使用列号 1-12

        // 第一步：遍历多项目，合并相同项目的行号或列号
        Object.entries(plateData.multiProjectAssignments)
            .filter(([key]) => validKeys.includes(key))
            .forEach(([key, project]) => {
                if (project && project.id) {
                    const existingKeys = projectLayoutMap.get(project.code) || [];
                    projectLayoutMap.set(project.code, [...existingKeys, key]);
                }
            });

        console.log('projectLayoutMap', projectLayoutMap);

        // 第二步：根据projectLayoutMap创建testRecord
        projectLayoutMap.forEach((layoutKeys, projectCode) => {
            // 找到对应的项目对象
            const project = Object.values(plateData.multiProjectAssignments).find(
                (p) => p.code === projectCode
            );

            if (project) {
                const projectWellData: Record<string, WellData> = {};

                // 遍历所有孔位数据
                Object.entries(wellData).forEach(([wellId, wellDataItem]) => {
                    const layoutKey =
                        plateData.layoutType === MTPLayoutType.MultiProjectHorizontalLayout
                            ? wellId.charAt(0) // 横排布局：使用行号 (A-H)
                            : wellId.substring(1); // 竖排布局：使用列号 (1-12)

                    // 如果这个孔位属于当前项目的任何一个行或列，就添加到projectWellData中
                    if (layoutKeys.includes(layoutKey)) {
                        projectWellData[wellId] = wellDataItem;
                    }
                });

                console.log('project.code', project.code, 'projectWellData', projectWellData);

                // 创建测试记录
                const testRecord: TestRecord = {
                    id: new Date().toISOString(),
                    mtpNumber: testAdditionalInfo.mtpNumber || '',
                    testDate: new Date(),
                    updateDate: new Date(),
                    cutOffValue: 0, // 默认截断值
                    testProject: project,
                    testAdditionalInfo: testAdditionalInfo,
                    wellData: projectWellData
                };
                // console.log('testRecord', testRecord);

                testRecords.push(testRecord);
            }
        });
    }

    console.log('转换后的 TestRecord 数组:', testRecords);
    return testRecords;
};

export const calculateODValue = (testRecords: TestRecord[]): TestRecord[] => {
    // 创建一个新的数组来存储更新后的记录
    const updatedTestRecords = testRecords.map((testRecord) => {
        // 创建记录的深拷贝，避免直接修改原始数据
        const updatedRecord = JSON.parse(JSON.stringify(testRecord)) as TestRecord;
        const wellData = updatedRecord.wellData;
        const useBlankCorrection = updatedRecord.testProject.useBlankCorrection;
        const isSingleWave = updatedRecord.testProject.refWaveIdx === 0;

        if (wellData) {
            // 1. 获取所有空白孔数据
            const blankData = Object.values(wellData).filter(
                (wellDataItem) => wellDataItem.sampleType.type === 'blank'
            );

            // 2. 计算空白校正值
            let blankMainOdValue = 0;
            let blankRefOdValue = 0;
            let blankOd = 0;

            if (blankData.length > 0) {
                if (isSingleWave) {
                    // 单波长必须使用平均空白校正
                    blankMainOdValue =
                        blankData.reduce((sum, item) => sum + (item.odMain || 0), 0) /
                        blankData.length;
                    blankOd = blankMainOdValue;
                } else {
                    // 双波长根据 useBlankCorrection 设置决定校正方式
                    switch (useBlankCorrection) {
                        case 'none': // 不使用空白校正
                            blankMainOdValue = 0;
                            blankRefOdValue = 0;
                            break;
                        case 'max': // 最大空白校正
                            blankMainOdValue = Math.max(
                                ...blankData.map((item) => item.odMain || 0)
                            );
                            blankRefOdValue = Math.max(...blankData.map((item) => item.odRef || 0));
                            break;
                        case 'min': // 最小空白校正
                            blankMainOdValue = Math.min(
                                ...blankData.map((item) => item.odMain || 0)
                            );
                            blankRefOdValue = Math.min(...blankData.map((item) => item.odRef || 0));
                            break;
                        default: // 平均空白校正
                            blankMainOdValue =
                                blankData.reduce((sum, item) => sum + (item.odMain || 0), 0) /
                                blankData.length;
                            blankRefOdValue =
                                blankData.reduce((sum, item) => sum + (item.odRef || 0), 0) /
                                blankData.length;
                    }
                    blankOd = blankMainOdValue - blankRefOdValue;
                }
            }

            // 3. 更新所有非空孔的 OD 值
            Object.entries(wellData).forEach(([key, wellDataItem]) => {
                if (wellDataItem.sampleType.type !== 'none') {
                    const odMain = wellDataItem.odMain || 0;
                    const odRef = wellDataItem.odRef || 0;
                    let odValue;

                    if (wellDataItem.sampleType.type === 'blank') {
                        // 空白孔使用原始值
                        odValue = isSingleWave ? odMain : odMain - odRef;
                    } else {
                        // 非空白孔需要减去空白校正值
                        odValue = isSingleWave
                            ? odMain - blankOd // 单波长：必须减去平均空白值
                            : odMain - odRef - (useBlankCorrection === 'none' ? 0 : blankOd); // 双波长：根据设置决定是否减去空白值
                    }

                    wellData[key] = {
                        ...wellDataItem,
                        odValue: odValue
                    };
                }
            });
        }

        return updatedRecord;
    });

    return updatedTestRecords;
};

export const calculateCutOffValue = (testRecords: TestRecord[]): TestRecord[] => {
    // 添加数字格式化函数
    const formatNumber = (num: number): number => {
        return Number(num.toFixed(4));
    };

    const updatedTestRecords = testRecords.map((testRecord) => {
        const updatedRecord = { ...testRecord };
        const wellData = updatedRecord.wellData;

        if (wellData) {
            // 1. 初始化计算数据对象
            const calcData: Record<string, number[]> = {};

            // 2. 获取项目配置 grayRangeDown
            const { cutOffFormula, postiveJudge, grayEnble, grayRangeUp } =
                updatedRecord.testProject;

            if (!cutOffFormula) {
                console.warn('警告: 未设置截断值计算公式');
                return updatedRecord;
            }

            // 3. 收集计算所需的数据
            Object.entries(wellData).forEach(([_, wellDataItem]) => {
                const { type, name } = wellDataItem.sampleType;

                // 只收集特定类型孔的数据
                if (['blank', 'nc', 'pc', 'qc', 'std'].includes(type) && name) {
                    if (!calcData[name]) {
                        calcData[name] = [];
                    }
                    // 确保 odValue 是有效数字
                    if (typeof wellDataItem.odValue === 'number' && !isNaN(wellDataItem.odValue)) {
                        calcData[name].push(formatNumber(wellDataItem.odValue));
                    }
                }
            });

            // 4. 计算截断值
            try {
                let cutOffValue = math.evaluate(cutOffFormula, calcData);

                // 处理数组结果
                if (Array.isArray(cutOffValue)) {
                    const numericValues = cutOffValue.filter(
                        (val) => typeof val === 'number' && !isNaN(val)
                    );
                    if (numericValues.length > 0) {
                        cutOffValue = formatNumber(
                            numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length
                        );
                    } else {
                        console.warn('警告: 截断值计算结果不包含有效数字');
                        cutOffValue = 0;
                    }
                } else if (typeof cutOffValue !== 'number' || isNaN(cutOffValue)) {
                    console.warn('警告: 截断值计算结果无效');
                    cutOffValue = 0;
                } else {
                    cutOffValue = formatNumber(cutOffValue);
                }

                // 5. 更新记录的截断值
                updatedRecord.cutOffValue = cutOffValue;

                // 6. 计算灰区范围
                const grayRangeValue = grayEnble
                    ? formatNumber(cutOffValue * grayRangeUp)
                    : grayRangeUp;

                // 7. 更新所有非空孔的结果
                Object.entries(wellData).forEach(([key, wellDataItem]) => {
                    if (
                        wellDataItem.sampleType.type !== 'none' &&
                        typeof wellDataItem.odValue === 'number' &&
                        !isNaN(wellDataItem.odValue)
                    ) {
                        const odValue = formatNumber(wellDataItem.odValue);
                        let result = 0; // 0=阴性 1=阳性 2=灰区

                        // 计算 S/CO 比值
                        const sco = cutOffValue !== 0 ? formatNumber(odValue / cutOffValue) : 0;
                        if (cutOffValue === 0) {
                            console.warn(`警告: 孔位 ${key} 的截断值为0，无法计算 S/CO 比值`);
                        }

                        // 判定结果（考虑灰区）
                        const isInGreyZone = (value: number, target: number): boolean => {
                            const lowerBound = formatNumber(target - grayRangeValue);
                            const upperBound = formatNumber(target + grayRangeValue);
                            return value >= lowerBound && value <= upperBound;
                        };

                        switch (postiveJudge) {
                            case '>':
                                if (isInGreyZone(odValue, cutOffValue)) {
                                    result = 2; // 灰区
                                } else {
                                    result = odValue > cutOffValue ? 1 : 0;
                                }
                                break;
                            case '>=':
                                if (isInGreyZone(odValue, cutOffValue)) {
                                    result = 2; // 灰区
                                } else {
                                    result = odValue >= cutOffValue ? 1 : 0;
                                }
                                break;
                            case '<':
                                if (isInGreyZone(odValue, cutOffValue)) {
                                    result = 2; // 灰区
                                } else {
                                    result = odValue < cutOffValue ? 1 : 0;
                                }
                                break;
                            case '<=':
                                if (isInGreyZone(odValue, cutOffValue)) {
                                    result = 2; // 灰区
                                } else {
                                    result = odValue <= cutOffValue ? 1 : 0;
                                }
                                break;
                            default:
                                console.warn(`警告: 未知的判定条件 ${postiveJudge}`);
                                result = 0;
                        }

                        wellData[key] = {
                            ...wellDataItem,
                            odValue: odValue,
                            odRatio: sco,
                            result: result
                        };
                    }
                });
            } catch (error) {
                logger.error('calculateCutOffValue error:', error, {
                    component: './src/renderer/src/utils/elisaDataSaveToDb.ts'
                });

                updatedRecord.cutOffValue = 0;
            }
        }

        return updatedRecord;
    });

    return updatedTestRecords;
};

export const saveTestRecordToDb = (testRecords: TestRecord[]): boolean => {
    try {
        console.log('Saving test records to database:', testRecords);

        // 同步保存每条记录
        for (const record of testRecords) {
            try {
                // 保存测试记录
                window.customApi.configInfo.addTestRecord(record);
            } catch (error) {
                console.error(`Error saving test record ${record.id}:`, error);
                return false;
            }
        }

        console.log('All test records saved successfully');
        return true;
    } catch (error) {
        console.error('Error in saveTestRecordToDb:', error);
        return false;
    }
};
