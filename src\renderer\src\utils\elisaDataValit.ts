import logger from '@renderer/utils/logger';
import { PlateData, Project } from '@shared/types';
import { MTPLayoutType } from '@shared/commondefines';

import { UseToastOptions } from '@chakra-ui/react';
import i18next from 'i18next';

// type ToastFunction = (options: ToastOptions) => void;
type ToastFunction = (options: UseToastOptions) => void;

// 验证项目设置
export const isProjectVaild = (toast: ToastFunction, plateData: PlateData): boolean => {
    if (plateData.layoutType === MTPLayoutType.SingleProjectLayout) {
        // 单项目布局：检查是否设置了单项目
        if (!plateData.singleProjectAssignments || !plateData.singleProjectAssignments.id) {
            toast({
                title: i18next.t('pages:elisaControl.generateReadCommand.toast.error.title'),
                description: i18next.t(
                    'pages:elisaControl.generateReadCommand.toast.error.description_noProject_single'
                ),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
            return false;
        }
    } else {
        // 多项目布局：检查是否至少分配了一个项目
        if (
            !plateData.multiProjectAssignments ||
            Object.keys(plateData.multiProjectAssignments).length === 0
        ) {
            toast({
                title: i18next.t('pages:elisaControl.generateReadCommand.toast.error.title'),
                description: i18next.t(
                    'pages:elisaControl.generateReadCommand.toast.error.description_noProject_multi'
                ),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
            return false;
        }
    }
    return true;
};

//生成读板命令
export const generateReadCommand = (toast: ToastFunction, plateData: PlateData): string => {
    // 生成读板命令字符串
    let readCommand: string = '';
    if (plateData.layoutType === MTPLayoutType.SingleProjectLayout) {
        // 单项目：直接获取项目的 testWave 和 refWave
        const project = plateData.singleProjectAssignments;
        console.log(
            '单项目：',
            'testWave:',
            project.testWaveIdx,
            'refWave:',
            project.refWaveIdx
        );

        if (
            !project.testWaveIdx ||
            project.refWaveIdx === undefined ||
            project.refWaveIdx === null
        ) {
            toast({
                // 缺少波长配置 单项目
                title: i18next.t('pages:elisaControl.generateReadCommand.toast.error.title'),
                description: i18next.t(
                    'pages:elisaControl.generateReadCommand.toast.error.description'
                ),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
            return '';
        }
        if (project.refWaveIdx === 0) {
            readCommand = `P(1,${project.testWaveIdx})\r\n`;
        } else {
            readCommand = `P(2,${project.testWaveIdx},${project.refWaveIdx})\r\n`;
        }
    } else {
        // 多项目：检查所有项目的波长是否一致
        const projects = Object.values(plateData.multiProjectAssignments) as Project[];
        if (projects.length === 0) {
            toast({
                // 没有分配项目 多项目
                title: i18next.t('pages.elisaControl.generateReadCommand.toast.error.title'),
                description: i18next.t(
                    'pages.elisaControl.generateReadCommand.toast.error.description_noProject'
                ),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
            return '';
        }

        // 检查所有项目的 testWave 和 refWave 是否一致
        const firstProject = projects[0];
        const testWave = firstProject.testWaveIdx;
        const refWave = firstProject.refWaveIdx;

        // 验证第一个项目是否有波长配置
        if (!testWave || refWave === undefined || refWave === null) {
            toast({
                // 缺少波长配置 单项目
                title: i18next.t('pages:elisaControl.generateReadCommand.toast.error.title'),
                description:
                    i18next.t(
                        'pages:elisaControl.generateReadCommand.toast.error.description_project'
                    ) +
                    `"[${firstProject.code}] ${firstProject.name}"` +
                    i18next.t(
                        'pages:elisaControl.generateReadCommand.toast.error.description_missingInfo'
                    ),
                status: 'error',
                duration: 3000,
                isClosable: true
            });
            return '';
        }

        // 检查其他项目的波长是否与第一个项目一致
        for (let i = 1; i < projects.length; i++) {
            const project = projects[i];
            if (
                !project.testWaveIdx ||
                project.refWaveIdx === undefined ||
                project.refWaveIdx === null
            ) {
                toast({
                    // 缺少波长配置 多项目
                    title: i18next.t('pages:elisaControl.generateReadCommand.toast.error.title'),
                    description:
                        i18next.t(
                            'pages:elisaControl.generateReadCommand.toast.error.description_project'
                        ) +
                        `"[${project.code}] ${project.name}"` +
                        i18next.t(
                            'pages:elisaControl.generateReadCommand.toast.error.description_missingInfo'
                        ),
                    status: 'error',
                    duration: 5000,
                    isClosable: true
                });
                return '';
            }

            if (project.testWaveIdx !== testWave) {
                toast({
                    // 检测波长不一致 多项目
                    title: i18next.t('pages:elisaControl.generateReadCommand.toast.error.title'),
                    description:
                        i18next.t(
                            'pages:elisaControl.generateReadCommand.toast.error.description_testWave'
                        ) +
                        `"[${project.code}] ${project.name}"` +
                        i18next.t(
                            'pages:elisaControl.generateReadCommand.toast.error.description_refWave_other'
                        ),
                    status: 'error',
                    duration: 5000,
                    isClosable: true
                });
                return '';
            }

            if (project.refWaveIdx !== refWave) {
                toast({
                    // 参考波长不一致 多项目
                    title: i18next.t('pages:elisaControl.generateReadCommand.toast.error.title'),
                    description:
                        i18next.t(
                            'pages:elisaControl.generateReadCommand.toast.error.description_refWave'
                        ) +
                        `"[${project.code}] ${project.name}"` +
                        i18next.t(
                            'pages:elisaControl.generateReadCommand.toast.error.description_refWave_other'
                        ),
                    status: 'error',
                    duration: 5000,
                    isClosable: true
                });
                return '';
            }
        }

        // 所有项目波长一致，生成读板命令
        if (refWave === 0) {
            readCommand = `P(1,${testWave})\r\n`;
        } else {
            readCommand = `P(2,${testWave},${refWave})\r\n`;
        }
    }

    logger.info('generate read command', {
        data: {
            layoutType: plateData.layoutType,
            readCommand: readCommand
        },
        component: './src/renderer/src/utils/elisaDataValit.ts'
    });
    return readCommand;
};

// 检查酶标板数据是否完整
export const isElisaDataComplete = (dataString: string): boolean => {
    if (!dataString || dataString.length === 0) return false;

    // 只保留A-H开头的行，跳过列标题行
    const lines = dataString
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => /^[A-H]/.test(line));

    logger.info('check elisa data completeness', {
        data: {
            totalLines: dataString.split('\n').length,
            validLines: lines.length
        },
        component: './src/renderer/src/utils/elisaDataValit.ts'
    });

    // 检查是否有足够的A-H行（8行或16行）
    if (lines.length === 8 || lines.length === 16) {
        // 检查每8行是否A-H顺序且每行13列
        const expectedRows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];

        const validBlock = (block: string[]) => {
            return (
                block.length === 8 &&
                block.every((line, i) => {
                    const parts = line.split(/\s+/);
                    return parts[0] === expectedRows[i] && parts.length >= 13;
                })
            );
        };

        // 检查第一组8行
        if (validBlock(lines.slice(0, 8))) {
            if (lines.length === 8) {
                logger.info('elisa data completeness check: found complete data (first panel)', {
                    data: {
                        completeRows: 8
                    },
                    component: './src/renderer/src/utils/elisaDataValit.ts'
                });
                return true;
            } else if (lines.length === 16 && validBlock(lines.slice(8, 16))) {
                logger.info('elisa data completeness check: found complete data (second panel)', {
                    data: {
                        completeRows: 16
                    },
                    component: './src/renderer/src/utils/elisaDataValit.ts'
                });
                return true;
            }
        }
    }

    logger.info('elisa data incomplete: continue waiting', {
        data: {
            aToHLines: lines.length
        },
        component: './src/renderer/src/utils/elisaDataValit.ts'
    });

    return false;
};

// 解析OD值为数组格式，支持两组数据
export const parseODValuesToArray = (
    dataString: string
): {
    mainData: Array<Array<number>>;
    refData: Array<Array<number>>;
} => {
    const mainData: Array<Array<number>> = [];
    const refData: Array<Array<number>> = [];

    try {
        // 解析酶标板数据格式：8行×12列，每行以A-H开头
        // 只处理A-H开头的行，跳过列标题行
        const lines = dataString
            .split('\n')
            .map((line) => line.trim())
            .filter((line) => /^[A-H]/.test(line));

        logger.info('parse OD values to array', {
            data: {
                totalLines: dataString.split('\n').length,
                validLines: lines.length
            },
            component: './src/renderer/src/utils/elisaDataValit.ts'
        });

        // 处理所有A-H开头的行
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const parts = line.split(/\s+/).filter((part) => part.length > 0);

            // 检查行首是否为A-H
            const rowLetter = parts[0];
            if (!rowLetter || !['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'].includes(rowLetter)) {
                continue;
            }

            // 解析12个数值
            const rowData: Array<number> = [];
            for (let j = 1; j <= 12 && j < parts.length; j++) {
                const odValue: number = parseFloat(parts[j]);
                // console.log(
                //     '解析12个数值 row[',
                //     i,
                //     ']col[',
                //     j,
                //     '] =',
                //     odValue,
                //     'isNaN =',
                //     isNaN(odValue)
                // );
                rowData.push(isNaN(odValue) ? 0 : odValue);
            }

            // 如果数值不足12个，用0填充
            while (rowData.length < 12) {
                rowData.push(0);
            }
            // console.log('rowData = ', rowData);

            // 根据行索引决定是主数据还是参考数据
            if (i < 8) {
                mainData.push(rowData);
            } else {
                refData.push(rowData);
            }
        }

        logger.info('OD values array parsing completed', {
            data: {
                mainDataLength: mainData.length + ' x 12',
                mainData: mainData,
                refDataLength: refData.length + ' x 12',
                refData: refData
            },
            component: './src/renderer/src/utils/elisaDataValit.ts'
        });
    } catch (error) {
        logger.error('解析OD值数组失败', error, {
            component: './src/renderer/src/utils/elisaDataValit.ts'
        });
    }

    return { mainData, refData };
};

// 将数组格式的OD值转换为Record格式（用于更新plateData）
export const convertArrayToRecord = (odArray: Array<Array<number>>): Record<string, number> => {
    const odValues: Record<string, number> = {};
    const rowLetters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
    // console.log('1-转换为Record格式 odArray.length = ', odArray.length, odArray);
    for (let i = 0; i < odArray.length; i++) {
        const rowLetter = rowLetters[i % 8]; // 循环使用A-H
        // console.log('2-转换为Record格式 rowLetter = ', rowLetter);
        const rowData = odArray[i];
        // console.log('3-转换为Record格式 rowData = odArray[', i, '] = ', rowData);

        for (let j = 0; j < rowData.length; j++) {
            const wellId = `${rowLetter}${j + 1}`;
            odValues[wellId] = rowData[j];
            // console.log(
            //     '4-转换为Record格式 wellId = ',
            //     wellId,
            //     'odValues[',
            //     wellId,
            //     '] = ',
            //     odValues[wellId],
            //     '= odArray[',
            //     i,
            //     '][',
            //     j,
            //     '] = ',
            //     odArray[i][j]
            // );
        }
    }

    return odValues;
};
