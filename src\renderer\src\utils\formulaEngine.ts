import * as math from 'mathjs';

export interface FormulaContext {
    variables: Record<string, number>;
    functions?: Record<string, (...args: number[]) => number>;
}

export interface FormulaResult {
    success: boolean;
    result?: number | boolean;
    error?: string;
}

export class FormulaEngine {
    private context: FormulaContext;

    constructor(context: FormulaContext) {
        this.context = context;
    }

    /**
     * 执行公式
     * @param formula 公式字符串
     * @returns 计算结果
     */
    execute(formula: string): FormulaResult {
        if (!formula.trim()) {
            return { success: false, error: '公式不能为空' };
        }

        try {
            // 预处理公式
            const processedFormula = this.preprocessFormula(formula);

            // 执行计算
            const result = math.evaluate(processedFormula, this.context);

            return { success: true, result };
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            return { success: false, error: `公式执行错误: ${errorMessage}` };
        }
    }

    /**
     * 预处理公式
     */
    private preprocessFormula(formula: string): string {
        let processed = formula;
        // 替换变量
        Object.entries(this.context.variables).forEach(([key, value]) => {
            const regex = new RegExp(`\\b${key}\\b`, 'g');
            processed = processed.replace(regex, value.toString());
        });
        // 替换函数名
        processed = processed
            .replace(/AVERAGE/g, 'mean')
            .replace(/MAX/g, 'max')
            .replace(/MIN/g, 'min')
            .replace(/SUM/g, 'sum')
            .replace(/ABS/g, 'abs')
            .replace(/ROUND/g, 'round')
            .replace(/SQRT/g, 'sqrt')
            .replace(/POW/g, 'pow');
        // 替换逻辑运算符
        processed = processed
            .replace(/\bAND\b/g, '&&')
            .replace(/\bOR\b/g, '||')
            .replace(/\bNOT\b/g, '!');
        return processed;
    }

    /**
     * 验证公式语法
     */
    validate(formula: string): { isValid: boolean; error?: string } {
        try {
            const processed = this.preprocessFormula(formula);
            math.evaluate(processed, this.context);
            return { isValid: true };
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            return { isValid: false, error: errorMessage };
        }
    }

    /**
     * 获取公式中使用的变量
     */
    getUsedVariables(formula: string): string[] {
        const variables: string[] = [];
        const regex = /\b(BLANK|NC\d*|PC\d*|QC\d*|STD\d*|S\d*)\b/g;
        let match;
        while ((match = regex.exec(formula)) !== null) {
            variables.push(match[1]);
        }
        return [...new Set(variables)]; // 去重
    }

    /**
     * 检查公式中是否有未定义的变量
     */
    checkUndefinedVariables(formula: string): string[] {
        const usedVariables = this.getUsedVariables(formula);
        const definedVariables = Object.keys(this.context.variables);

        return usedVariables.filter((variable) => !definedVariables.includes(variable));
    }

    /**
     * 获取公式的依赖关系
     */
    getFormulaDependencies(formula: string): {
        variables: string[];
        functions: string[];
        operators: string[];
    } {
        const variables = this.getUsedVariables(formula);

        const functions: string[] = [];
        const functionRegex = /\b(AVERAGE|MAX|MIN|SUM|ABS|ROUND|SQRT|POW)\b/g;
        let funcMatch;
        while ((funcMatch = functionRegex.exec(formula)) !== null) {
            functions.push(funcMatch[1]);
        }

        const operators: string[] = [];
        const operatorRegex = /(\+|-|\*|\/|<=|>=|<|>|=|!=|\bAND\b|\bOR\b|\bNOT\b|\?|:)/g;
        let opMatch;
        while ((opMatch = operatorRegex.exec(formula)) !== null) {
            operators.push(opMatch[1]);
        }

        return {
            variables: [...new Set(variables)],
            functions: [...new Set(functions)],
            operators: [...new Set(operators)]
        };
    }
}

// 创建公式引擎的工厂函数
export const createFormulaEngine = (data: Record<string, number>) => {
    return new FormulaEngine({
        variables: data,
        functions: {
            // 可以添加自定义函数
            customFunction: (x: number) => x * 2
        }
    });
};

// 预定义的测试数据
export const getDefaultTestData = (): Record<string, number> => ({
    Blank: 0.001,
    NC: 0.05,
    'NC-1': 0.05,
    'NC-2': 0.06,
    'NC-3': 0.04,
    PC: 0.8,
    'PC-1': 0.8,
    'PC-2': 0.85,
    'PC-3': 0.82,
    'DUP-SAMP-1': 0.3,
    'DUP-SAMP-2': 0.32,
    'SAMP-1': 0.25,
    'SAMP-2': 0.28,
    NEG: 0.1,
    POS: 0.5,
    CUTOFF: 0.3
});

// 公式验证器
export class FormulaValidator {
    /**
     * 验证公式的基本语法
     */
    static validateBasicSyntax(formula: string): { isValid: boolean; error?: string } {
        if (!formula.trim()) {
            return { isValid: false, error: '公式不能为空' };
        }
        // 检查括号匹配
        const openBrackets = (formula.match(/\(/g) || []).length;
        const closeBrackets = (formula.match(/\)/g) || []).length;
        if (openBrackets !== closeBrackets) {
            return { isValid: false, error: '括号不匹配' };
        }
        // 禁止出现方括号
        if (formula.includes('[') || formula.includes(']')) {
            return { isValid: false, error: '变量名不能包含中括号' };
        }
        return { isValid: true };
    }
    /**
     * 验证变量格式
     */
    static validateVariableFormat(formula: string): { isValid: boolean; error?: string } {
        const variableRegex = /\b(BLANK|NC\d*|PC\d*|QC\d*|STD\d*|S\d*)\b/g;
        let match;
        while ((match = variableRegex.exec(formula)) !== null) {
            const variableName = match[1];
            if (!variableName.trim()) {
                return { isValid: false, error: '变量名不能为空' };
            }
            if (!/^[A-Z0-9]+$/.test(variableName)) {
                return { isValid: false, error: `变量名 "${variableName}" 不合法` };
            }
        }
        return { isValid: true };
    }
    /**
     * 完整的公式验证
     */
    static validateFormula(formula: string): { isValid: boolean; error?: string } {
        // 基本语法验证
        const basicValidation = this.validateBasicSyntax(formula);
        if (!basicValidation.isValid) {
            return basicValidation;
        }
        // 变量格式验证
        const variableValidation = this.validateVariableFormat(formula);
        if (!variableValidation.isValid) {
            return variableValidation;
        }
        // 使用 mathjs 进行语法验证
        try {
            const testFormula = formula
                .replace(/\b(BLANK|NC\d*|PC\d*|QC\d*|STD\d*|S\d*)\b/g, '1')
                .replace(/AVERAGE/g, 'mean')
                .replace(/MAX/g, 'max')
                .replace(/MIN/g, 'min')
                .replace(/SUM/g, 'sum')
                .replace(/ABS/g, 'abs')
                .replace(/ROUND/g, 'round')
                .replace(/SQRT/g, 'sqrt')
                .replace(/POW/g, 'pow')
                .replace(/\bAND\b/g, '&&')
                .replace(/\bOR\b/g, '||')
                .replace(/\bNOT\b/g, '!');
            math.evaluate(testFormula);
            return { isValid: true };
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            return { isValid: false, error: `语法错误: ${errorMessage}` };
        }
    }
}
