import type { LoggerInterface } from '@shared/logger';

// 将Error对象转换为可序列化的格式
const serializeError = (error: Error | unknown): unknown => {
    if (error instanceof Error) {
        return {
            name: error.name,
            message: error.message,
            stack: error.stack
            // 移除可能导致循环引用的属性
        };
    }

    // 处理其他类型的错误
    if (typeof error === 'object' && error !== null) {
        try {
            // 尝试序列化对象
            JSON.stringify(error);
            return error;
        } catch {
            // 如果无法序列化，返回字符串表示
            return {
                type: typeof error,
                stringValue: String(error)
            };
        }
    }

    return error;
};

const logger: LoggerInterface = {
    log: (message: string, meta?: Record<string, unknown>) => {
        // 测试环境fallback到console
        if (typeof window === 'undefined' || !window.customApi) {
            console.log(message, meta);
            return;
        }

        try {
            window.customApi.log.send({
                level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
                message,
                ...meta
            });
        } catch (error) {
            console.error('send log to main process error:', error);
        }
    },

    info: (message: string, meta?: Record<string, unknown>) => {
        if (typeof window === 'undefined' || !window.customApi) {
            console.info(message, meta);
            return;
        }

        try {
            window.customApi.log.send({
                level: 'info',
                message,
                ...meta
            });
        } catch (error) {
            console.error('send log to main process error:', error);
        }
    },

    warn: (message: string, meta?: Record<string, unknown>) => {
        if (typeof window === 'undefined' || !window.customApi) {
            console.warn(message, meta);
            return;
        }

        try {
            window.customApi.log.send({
                level: 'warn',
                message,
                ...meta
            });
        } catch (error) {
            console.error('send log to main process error:', error);
        }
    },

    error: (message: string, error?: Error | unknown, meta?: Record<string, unknown>) => {
        if (typeof window === 'undefined' || !window.customApi) {
            console.error(message, error, meta);
            return;
        }

        try {
            window.customApi.log.send({
                level: 'error',
                message,
                error: serializeError(error),
                ...meta
            });
        } catch (logError) {
            console.error('send log to main process error:', logError);
        }
    },

    debug: (message: string, meta?: Record<string, unknown>) => {
        if (typeof window === 'undefined' || !window.customApi) {
            if (process.env.NODE_ENV === 'development') {
                console.debug(message, meta);
            }
            return;
        }

        if (process.env.NODE_ENV === 'development') {
            try {
                window.customApi.log.send({
                    level: 'debug',
                    message,
                    ...meta
                });
            } catch (error) {
                console.error('send log to main process error:', error);
            }
        }
    }
};

export default logger;
