/**
 * 表格工具函数
 */

/**
 * 计算需要合并的行数
 * @param data 表格数据
 * @param columnIndex 要合并的列索引
 * @param rowIndex 当前行索引
 * @returns 需要合并的行数
 */
export const getRowSpan = (data: any[][], columnIndex: number, rowIndex: number): number => {
    const currentValue = data[rowIndex][columnIndex];
    let rowSpan = 1;

    // 向下查找相同值的行数
    for (let i = rowIndex + 1; i < data.length; i++) {
        if (data[i][columnIndex] === currentValue) {
            rowSpan++;
        } else {
            break;
        }
    }

    return rowSpan;
};

/**
 * 判断当前单元格是否应该显示（用于合并单元格）
 * @param data 表格数据
 * @param columnIndex 要合并的列索引
 * @param rowIndex 当前行索引
 * @returns 是否应该显示当前单元格
 */
export const shouldShowCell = (data: any[][], columnIndex: number, rowIndex: number): boolean => {
    // 第一行总是显示
    if (rowIndex === 0) return true;

    // 检查上一行的值是否相同
    return data[rowIndex][columnIndex] !== data[rowIndex - 1][columnIndex];
};

/**
 * 处理表格数据，为每行添加合并信息
 * @param data 原始表格数据
 * @param mergeColumns 需要合并的列索引数组
 * @returns 处理后的数据，包含合并信息
 */
export const processTableDataForMerge = (
    data: any[][],
    mergeColumns: number[]
): Array<{
    rowData: any[];
    mergeInfo: Array<{ rowSpan: number; shouldShow: boolean }>;
}> => {
    return data.map((row, rowIndex) => {
        const mergeInfo = mergeColumns.map((columnIndex) => ({
            rowSpan: getRowSpan(data, columnIndex, rowIndex),
            shouldShow: shouldShowCell(data, columnIndex, rowIndex)
        }));

        return {
            rowData: row,
            mergeInfo
        };
    });
};

/**
 * 创建带时间列的示例数据
 * @param count 数据行数
 * @returns 示例数据
 */
export const createSampleDataWithTime = (count: number = 10): any[][] => {
    const data: any[][] = [];
    const baseTime = new Date('2023-12-25 10:00:00');

    for (let i = 0; i < count; i++) {
        // 每3行使用相同的时间
        const timeIndex = Math.floor(i / 3);
        const time = new Date(baseTime.getTime() + timeIndex * 60000); // 每分钟递增

        data.push([
            time.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }), // 时间列
            `样本${i + 1}`, // 样本名称
            `项目${Math.floor(i / 2) + 1}`, // 项目名称
            (Math.random() * 2).toFixed(3), // OD值
            Math.random() > 0.5 ? '阳性' : '阴性' // 结果
        ]);
    }

    return data;
};

/**
 * 格式化时间显示
 * @param timeString 时间字符串
 * @returns 格式化后的时间
 */
export const formatTimeForDisplay = (timeString: string): string => {
    try {
        const date = new Date(`2000-01-01 ${timeString}`);
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch {
        return timeString;
    }
};
