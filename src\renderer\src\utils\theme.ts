import { ChakraTheme, extendTheme } from '@chakra-ui/react';
import '@renderer/assets/fonts.css';

// 定义字体名称常量
const fonts = {
    bold: 'MiSans-Bold',
    normal: 'MiSans-Normal'
} as const;

// 定义常用的字体样式
export const styles = {
    // 标题字体 - 粗体
    heading: {
        fontFamily: fonts.bold,
        fontSize: 'md',
        textAlign: 'center' as const
    },

    // 标题字体 - 大号
    headingLarge: {
        fontFamily: fonts.bold,
        fontSize: 'xl',
        textAlign: 'center' as const
    },

    // 正文字体 - 普通
    body: {
        fontFamily: fonts.normal,
        fontSize: 'md'
    },

    // 正文字体 - 小号
    bodySmall: {
        fontFamily: fonts.normal,
        fontSize: 'sm'
    },

    // 表格头部样式
    tableHeader: {
        fontFamily: fonts.bold,
        fontSize: 'md' as const,
        py: '2',
        textAlign: 'center' as const
    },

    // 表格单元格样式
    tableCell: {
        fontFamily: fonts.normal,
        fontSize: 'lg',
        py: '3',
        textAlign: 'center' as const
    },

    label_normal: {
        fontFamily: fonts.normal,
        alignItems: 'center' as const,
        justifyContent: 'center' as const,
        fontSize: 'md'
    },
    label_bold: {
        fontFamily: fonts.bold,
        alignItems: 'center' as const,
        justifyContent: 'center' as const
    },
    data_normal: {
        fontFamily: fonts.normal
    },
    data_bold: {
        fontFamily: fonts.bold
    },

    // 输入框标签样式
    inputLabel: {
        fontFamily: fonts.bold
    },

    // 输入框内容样式
    inputContent: {
        fontFamily: fonts.normal
    },

    // 模态框头部样式
    modalHeader: {
        fontFamily: fonts.bold,
        bg: 'teal.500',
        color: 'white'
    },

    // 标签页样式
    tab: {
        fontFamily: fonts.bold
    }
} as const;

// 创建自定义主题 - 只设置全局字体，不设置组件样式
// export const customTheme = extendTheme({
//     fonts: {
//         heading: 'MiSans-Bold',
//         body: 'MiSans-Normal'
//     }
// } as Partial<ChakraTheme>);

// 创建自定义主题
export const customTheme = extendTheme({
    fonts: {
        heading: 'MiSans-Bold',
        body: 'MiSans-Normal'
    },
    components: {
        // 表格组件使用全局字体
        Th: {
            baseStyle: {
                fontFamily: 'heading', // 使用主题中定义的 heading 字体
                fontSize: 'md',
                textAlign: 'center'
            }
        },
        Td: {
            baseStyle: {
                fontFamily: 'body', // 使用主题中定义的 body 字体
                textAlign: 'center'
            }
        },
        // 其他组件也可以使用全局字体
        ModalHeader: {
            baseStyle: {
                fontFamily: 'heading'
            }
        },
        Tab: {
            baseStyle: {
                fontFamily: 'heading'
            }
        },

        InputLeftAddon: {
            baseStyle: {
                fontFamily: 'MiSans-Bold'
            }
        },

        Input: {
            baseStyle: {
                fontFamily: 'body'
            }
        },
        Select: {
            baseStyle: {
                fontFamily: 'body'
            }
        },
        NumberInputField: {
            baseStyle: {
                fontFamily: 'body'
            }
        }
    }
} as Partial<ChakraTheme>);

// 导出类型定义
export type styleKey = keyof typeof styles;

// 样式工具函数
export const getFontStyle = (styleKey: styleKey) => {
    return styles[styleKey];
};

// 组合多个样式
export const combineStyles = (...styles: Record<string, unknown>[]) => {
    return styles.reduce((acc, style) => ({ ...acc, ...style }), {});
};

// 常用的样式组合
export const commonStyles = {
    // 表格头部
    tableHeader: combineStyles(styles.tableHeader),

    // 表格单元格
    tableCell: combineStyles(styles.tableCell),

    // 模态框头部
    modalHeader: combineStyles(styles.modalHeader),

    // 输入框标签
    inputLabel: combineStyles(styles.inputLabel),

    // 输入框内容
    inputContent: combineStyles(styles.inputContent)
} as const;
