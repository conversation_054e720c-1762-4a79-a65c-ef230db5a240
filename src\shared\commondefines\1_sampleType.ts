// 定义样本变量类型
export type SampleType = '' | 'sample' | 'blank' | 'nc' | 'pc' | 'qc' | 'std' | 'none';

export interface SampleTypeVariable {
    name: string;
    type: SampleType;
}
// 预定义样本的变量类型
export const PREDEFINED_SAMPLE_TYPES = [
    { name: '', type: 'none' },
    { name: 'SAMPLE', type: 'sample' },
    { name: 'BLANK', type: 'blank' },
    { name: 'NC1', type: 'nc' },
    { name: 'NC2', type: 'nc' },
    { name: 'NC3', type: 'nc' },
    { name: 'NC4', type: 'nc' },
    { name: 'PC1', type: 'pc' },
    { name: 'PC2', type: 'pc' },
    { name: 'PC3', type: 'pc' },
    { name: 'PC4', type: 'pc' },
    { name: 'QC1', type: 'qc' },
    { name: 'QC2', type: 'qc' },
    { name: 'QC3', type: 'qc' },
    { name: 'QC4', type: 'qc' },
    { name: 'STD0', type: 'std' },
    { name: 'STD1', type: 'std' },
    { name: 'STD2', type: 'std' },
    { name: 'STD3', type: 'std' },
    { name: 'STD4', type: 'std' },
    { name: 'STD5', type: 'std' },
    { name: 'STD6', type: 'std' },
    { name: 'STD7', type: 'std' },
    { name: 'STD8', type: 'std' },
    { name: 'STD9', type: 'std' }
] as const;

// 样本类型的颜色配置
export const getSampleTypeColor = (sampleType?: string) => {
    switch (sampleType) {
        case 'sample':
            return { scheme: 'blue', bg: 'blue.50', border: 'blue.300', text: 'blue.600' };
        case 'blank':
            return { scheme: 'gray', bg: 'gray.50', border: 'gray.300', text: 'gray.600' };
        case 'nc':
            return { scheme: 'green', bg: 'green.50', border: 'green.300', text: 'green.600' };
        case 'pc':
            return { scheme: 'red', bg: 'red.50', border: 'red.300', text: 'red.600' };
        case 'qc':
            return { scheme: 'purple', bg: 'purple.50', border: 'purple.300', text: 'purple.600' };
        case 'std':
            return { scheme: 'orange', bg: 'orange.50', border: 'orange.300', text: 'orange.600' };
        default:
            return { scheme: 'gray', bg: 'transparent', border: 'gray.300', text: 'gray.600' };
    }
};

// export enum SampleType {
//     Normal = '普通标本',
//     Blank = '空白',
//     NegativeControl = '阴性对照',
//     WeakPositive = '弱阳性',
//     PositiveControl = '阳性对照',
//     Standard = '标准品',
//     QualityControl = '质控品'
// }

// // 静态方法：获取所有值
// export function getSampleTypeValues(): SampleType[] {
//     return [
//         SampleType.Normal,
//         SampleType.Blank,
//         SampleType.NegativeControl,
//         SampleType.WeakPositive,
//         SampleType.PositiveControl,
//         SampleType.Standard,
//         SampleType.QualityControl
//     ];
// }

// export enum SampleType {
//     Sample = 'Sample',
//     Blank = 'Blank',
//     NC1 = 'NC1',
//     NC2 = 'NC2',
//     NC3 = 'NC3',
//     NC4 = 'NC4',
//     PC1 = 'PC1',
//     PC2 = 'PC2',
//     PC3 = 'PC3',
//     PC4 = 'PC4',
//     QC1 = 'QC1',
//     QC2 = 'QC2',
//     QC3 = 'QC3',
//     QC4 = 'QC4',
//     STD0 = 'STD0',
//     STD1 = 'STD1',
//     STD2 = 'STD2',
//     STD3 = 'STD3',
//     STD4 = 'STD4',
//     STD5 = 'STD5',
//     STD6 = 'STD6',
//     STC7 = 'STC7',
//     STC8 = 'STC8',
//     STC9 = 'STC9'
// }

// 静态方法：获取所有值
// export function getSampleTypeValues(): SampleType[] {
//     return [
//         SampleType.Normal,
//         SampleType.Blank,
//         SampleType.NegativeControl,
//         SampleType.WeakPositive,
//         SampleType.PositiveControl,
//         SampleType.Standard,
//         SampleType.QualityControl
//     ];
// }

export const enum MTPLayoutType {
    SingleProjectLayout = '单项目',
    MultiProjectHorizontalLayout = '多项目（横排）',
    MultiProjectVerticalLayout = '多项目（竖排）'
}
