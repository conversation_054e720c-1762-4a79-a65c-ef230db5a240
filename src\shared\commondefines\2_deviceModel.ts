import { DeviceConfig, DeviceConfigRaw } from '@shared/types';

export const enum DeviceModel {
    unknown = 'unknown',
    st360 = 'ST-360',
    st960 = 'ST-960'
}

function isValidDeviceModel(value: string): value is DeviceModel {
    const validModels: string[] = [
        DeviceModel.unknown, // 'unknown'
        DeviceModel.st360, // 'ST-360'
        DeviceModel.st960 // 'ST-960'
    ];
    return validModels.includes(value);
}

export const DeviceNames = {
    unknown: 'common:commonDefines.deviceNames.unknown',
    st360: 'common:commonDefines.deviceNames.st360',
    st960: 'common:commonDefines.deviceNames.st960'
} as const;

export const DeviceTypes = {
    unknown: 'common:commonDefines.deviceTypes.unknown',
    reader: 'common:commonDefines.deviceTypes.reader',
    station: 'common:commonDefines.deviceTypes.station'
} as const;

interface DeviceInfo {
    name: string;
    type: string;
}

export function getDeviceInfo(model: DeviceModel): DeviceInfo {
    const deviceInfoMap: Record<DeviceModel, DeviceInfo> = {
        [DeviceModel.unknown]: {
            name: DeviceNames.unknown,
            type: DeviceModel.unknown
        },
        [DeviceModel.st360]: {
            name: DeviceNames.st360,
            type: DeviceTypes.reader
        },
        [DeviceModel.st960]: {
            name: DeviceNames.st960,
            type: DeviceTypes.reader
        }
    };
    return deviceInfoMap[model];
}

export function convertToDeviceConfig(rawConfig: DeviceConfigRaw): DeviceConfig {
    if (!isValidDeviceModel(rawConfig.model)) {
        throw new Error(`Invalid device model: ${rawConfig.model}`);
    }
    const model = rawConfig.model as DeviceModel;
    const deviceInfo = getDeviceInfo(model);

    return {
        ...rawConfig,
        name: deviceInfo.name,
        type: deviceInfo.type
    };
}

export function convertToDeviceConfigs(rawConfigs: DeviceConfigRaw[]): DeviceConfig[] {
    return rawConfigs.map(convertToDeviceConfig);
}
