export const IPCChannels = {
    configInfo: {
        GetDeviceConfigInfo: 'get-device-config-info',

        // 检测记录管理
        GetTestRecordList: 'get-test-record-list',
        AddTestRecord: 'add-test-record',
        DeleteTestRecord: 'delete-test-record',
        UpdateTestRecord: 'update-test-record',
        GetTestRecordById: 'get-test-record-by-id',
        GetTestRecordByMtpNumber: 'get-test-record-by-mtp-number',
        GetTestRecordByTestDate: 'get-test-record-by-test-date',

        // 项目管理
        GetProjectList: 'get-project-list',
        AddProject: 'add-project',
        DeleteProject: 'delete-project',
        UpdateProject: 'update-project',

        // 模板管理
        AddMtpTemplate: 'mtp-template:Add',
        GetAllMtpTemplates: 'mtp-template:Get-all',
        GetMtpTemplateById: 'mtp-template:Get-by-id',
        UpdateMtpTemplate: 'mtp-template:Update',
        DeleteMtpTemplate: 'mtp-template:Delete'
    },
    STORE: {
        Get: 'get-store-value',
        Set: 'set-store-value',
        Delete: 'delete-store-value'
    },
    SERIAL: {
        // GetSerialConfig: 'get-serial-config',
        // SaveSerialConfig: 'save-serial-config',

        GetSerialPorts: 'get-serial-ports',
        OpenSerialPort: 'open-serial-port',
        CloseSerialPort: 'close-serial-port',
        SendData: 'send-data',
        ReceiveData: 'receive-data'
    },
    APP: {
        GetAppVersion: 'get-app-version',
        GetUserPath: 'get-user-path',
        GetAppPath: 'get-app-path',
        // 文件对话框
        OpenFileDialog: 'app:open-file-dialog',
        OpenFolderDialog: 'app:open-folder-dialog',
        SaveFileDialog: 'app:save-file-dialog',
        SaveFolderDialog: 'app:save-folder-dialog'

        // GetAppSettings: 'get-app-settings',
        // UpdateAppSettings: 'update-app-settings'
    },
    LOG: {
        Send: 'log:send'
    },
    PRINT: {
        GetPrinters: 'print:get-printers',
        PrintPreview: 'print:print-preview',
        PrintToPdf: 'print:print-to-pdf',
        Print: 'print:print',

        ExportToExcel: 'export:export-to-excel',
        ExportToCsv: 'export:export-to-csv',
        ExportToLIS: 'export:export-to-lis'
    }
} as const;
