// 日志消息
export interface LogMessage {
    level: 'info' | 'warn' | 'error' | 'debug' | 'log';
    message: string;
    component?: string;
    data?: unknown;
    error?: Error | unknown; // 可以是Error对象或序列化后的错误信息
}

export interface LoggerInterface {
    log(message: string, meta?: Record<string, unknown>): void;
    info(message: string, meta?: Record<string, unknown>): void;
    warn(message: string, meta?: Record<string, unknown>): void;
    error(message: string, error?: Error | unknown, meta?: Record<string, unknown>): void;
    debug(message: string, meta?: Record<string, unknown>): void;
}
