import i18n from 'i18next';
// 拟合模型类型
export type FittingModel =
    | 'LINEAR'
    | 'POLYNOMIAL'
    | 'LOG_LOG'
    | 'LOGISTIC_3P'
    | 'LOGISTIC_4P'
    | 'LOGISTIC_5P'
    | 'CUBIC_SPLINE';

export function getFittingModelTypeLabel(model: FittingModel):   string {
    switch (model) {
        case 'LINEAR':
            return i18n.t('common:label.linear');
        case 'POLYNOMIAL':
            return i18n.t('common:label.polynomial');
        case 'LOG_LOG':
            return i18n.t('common:label.log_log');
        case 'LOGISTIC_3P':
            return i18n.t('common:label.logistic_3p');
        case 'LOGISTIC_4P':
            return i18n.t('common:label.logistic_4p');
        case 'LOGISTIC_5P':
            return i18n.t('common:label.logistic_5p');
        case 'CUBIC_SPLINE':
            return i18n.t('common:label.cubicSpline');
        default:
            return '';
    }
}

export function getFittingModelFormula(model: FittingModel):   string {
    switch (model) {
        case 'LINEAR':
            return 'y = a + bx';
        case 'POLYNOMIAL':
            return 'y = a + bx + cx^2 + dx^3';
        case 'LOG_LOG':
            return 'log(y) = a + b*log(x)';
        case 'LOGISTIC_3P':
            return 'y = L / (1 + 10^((log10(IC50) - x) * h))';
        case 'LOGISTIC_4P':
            return 'y = a + (b-a) / (1 + 10^((log10(IC50) - x) * h))';
        case 'LOGISTIC_5P':
            return 'y = a + (b-a) / (1 + 10^((log10(IC50) - x) * h))^g';
        case 'CUBIC_SPLINE':
            return 'y = a + bx + cx^2 + dx^3';
        default:
            return '';
    }
}

export const fittingModelType = ['LINEAR', 'POLYNOMIAL','LOG_LOG','LOGISTIC_3P','LOGISTIC_4P','LOGISTIC_5P','CUBIC_SPLINE'] as const;

// 标准曲线数据
export interface StandardCurveData {
    concentrations: number[];
    responses: number[];
    name?: string;
}

// 拟合选项
export interface FittingOptions {
    model: FittingModel;
    maxIterations?: number;
    errorTolerance?: number;
    damping?: number;
}

// 质量指标
export interface QualityMetrics {
    isValid: boolean;
    warnings: string[];
    rSquared: number;
    maxResidual: number;
    convergence: boolean;
}

// 拟合结果
export interface FittingResult {
    model: FittingModel;
    parameters: number[];
    rSquared: number;
    residuals: number[];
    predict: (x: number) => number;
    predictConcentration: (y: number) => number;
    equation: string;
    convergence: boolean;
    iterations: number;
    qualityMetrics: QualityMetrics | null;
}

export interface FittingOptions {
    model: FittingModel;
    maxIterations?: number;
    errorTolerance?: number;
    damping?: number;
    polynomialDegree?: number; // 多项式拟合的阶数
}
