import { SampleTypeVariable } from '@shared/commondefines';
import { MTPLayoutType } from '@shared/commondefines';
import { Project } from './project';

// 孔位ID类型 (如 "A1", "B2")
export type WellId = string;

// 检测结果
export type TestResult = '' | '阳性' | '阴性' | '阳性(+)' | '阴性(-)' | '+' | '-' | 'positive' | 'negative';

// 单个孔位的数据
export interface WellData {
    sampleType: SampleTypeVariable; // 样本类型
    sampleNumber: string; // 样本编号
    odMain?: number; // 主波长OD值
    odRef?: number; // 参考OD值
    odValue?: number; // 计算后的OD值
    odRatio?: number; // 计算后的OD比
    concentration?: number; // 浓度值
    result?: number; // 结果 0=阴性 1=阳性 2=灰区
}

// 项目分配 (行/列 -> 项目对象)
export interface ProjectAssignments {
    [rowOrCol: string]: Project; // 行标识 (A-H) 或列标识 (1-12) -> 项目对象
}

// 酶标板layout 项目设置 孔位设置信息
export interface PlateData {
    layoutType: MTPLayoutType; // 布局类型
    singleProjectAssignments: Project; // 单个项目分配
    multiProjectAssignments: ProjectAssignments; // 多个项目分配
    wellData: Record<string, WellData>; // 孔位数据 记录格式为 {wellId: WellData}
}

export interface TestAdditionalInfo {
    mtpNumber?: string; //
    reagentBatch?: string; // 试剂批号
    reagentExpiry?: string; // 试剂有效期
    reagentSupplier?: string; // 试剂供应商
    testTemperature?: number; // 检测温度
    testRelativeHumidity?: number; // 检测相对湿度
    testReviewer?: string; // 检测审核者

    mainTitle?: string; // 主标题
    subTitle?: string; // 副标题
    note?: string; // 备注
    testMethod?: string; // 检测方法

    testInstrument?: string; // 检测仪器
    testType?: string; // 检测类型
    testOperator?: string; // 检测人员
    testBasis?: string; // 检测依据
    sampleSource?: string; // 样本来源
    sampleStatus?: string; // 样本状态
    sampleType?: string; // 样本类型
    sampleNumber?: string; // 样本编号
}

export interface TestRecord {
    id: string; // 检测记录ID
    mtpNumber: string; // 酶标板编号
    testDate: Date; // 检测日期
    updateDate: Date; // 更新日期
    cutOffValue: number; // 截断值 默认值为0.1

    testProject: Project; // 检测项目
    testAdditionalInfo: TestAdditionalInfo; // 检测附加信息

    wellData?: Record<string, WellData>; // 孔位数据
}

// 自动编号方向
export enum AutoNumberDirection {
    Horizontal = 'horizontal', // 横向
    Vertical = 'vertical', // 纵向
    Reverse = 'reverse' // 逆序
}

// 自动编号配置
export interface AutoNumberConfig {
    startWell: WellId; // 起始孔位
    direction: AutoNumberDirection;
    prefix?: string; // 编号前缀
    startNumber: number; // 起始编号
    step: number; // 步长
}
