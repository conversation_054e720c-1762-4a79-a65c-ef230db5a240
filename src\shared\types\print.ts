import { TestRecord } from './plateData';

// 打印请求数据结构
export interface PrintRequest {
    // // 项目信息（用于选择模板）
    // projectCode: string;
    // projectName: string;

    // 检测记录数据
    testRecord: TestRecord;
    printDate: string;

    // 打印选项
    options: {
        language: 'zh' | 'en';
        printType: 'qualitative' | 'quantitative'; // 定性/定量
        includePlateData: boolean;
        includeStatistics: boolean;
        dataType: 'od' | 'concentration' | 'result'; // 显示的数据类型
    };
}

// 打印结果
export interface PrintResult {
    success: boolean;
    message?: string;
    data?: {
        filePath?: string;
        previewUrl?: string;
    };
}
