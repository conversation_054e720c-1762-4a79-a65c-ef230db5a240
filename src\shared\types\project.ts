import i18n from 'i18next';

// 通道滤光片编号
export type FiltersNo = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;
// 通道滤光片对象
export type Filters = { no: FiltersNo; wavelength: number };

// 设备配置信息原始数据
export interface DeviceConfigRaw {
    model: string;
    filters: Filters[];
}
// 设备配置信息
export interface DeviceConfig extends DeviceConfigRaw {
    name: string;
    type: string;
}

// 统一的 API 响应格式
export type ApiResponse<T = void> = {
    success: boolean;
    data?: T;
    error?: string;
};

/*------------------------------------串口-----------------------------------------------*/
// 数据位
export type DataBits = 5 | 6 | 7 | 8;
// 停止位
export type StopBits = 1 | 1.5 | 2;
// 校验位
export type Parity = 'none' | 'even' | 'odd' | 'mark' | 'space';
// 流控
export type FlowControl = 'none' | 'hardware' | 'software';

// 定义串口选项类型
// export type SerialPortOptions = Partial<Omit<SerialPortOpenOptions<any>, 'path'>>;
export interface SerialPortOptions {
    path?: string; // 串口路径
    baudRate?: number; // 波特率
    dataBits?: DataBits; // 数据位 (5, 6, 7, or 8)
    stopBits?: StopBits; // 停止位 (1, 1.5, or 2)
    parity?: Parity; // 校验位 (none, odd, even)
    timeout?: number; // 超时时间(秒)
}

// 定义串口信息类型
export interface PortsList {
    path: string; // 串口路径
}
/*------------------------------------串口  -----------------------------------------------*/

/*------------------------------------项目管理-----------------------------------------------*/
export type TestType = 'x' | 'q'; // x=定性 q=定量

// 根据项目配置判断是定性还是定量
export function getTestTypeName(testType: TestType): string {
    if (testType === 'q') {
        return i18n.t('common:label.quantitative'); // 定量
    } else {
        return i18n.t('common:label.qualitative'); // 定性
    }
}

// export const projectTestType = [
//     { id: 'x', label: getTestTypeName('x') }, // 定性
//     { id: 'q', label: getTestTypeName('q') } // 定量
// ] as const;

export const projectTestType = ['x', 'q'] as const;

export type AxisType = 0 | 1 | 2;
export const quantitativeAxisType = [
    { id: 0, label: 'Linear' },
    { id: 1, label: 'Log{x}' },
    { id: 2, label: 'Ln{x}' }
] as const;

export const projectResultShow = [
    { id: 0, label: '数值结果' },
    { id: 1, label: '文本结果' },
    { id: 2, label: '阴/阳性结果' }
] as const;

export type BlankCorrection = 'none' | 'max' | 'min' | 'mean';
export const projectBlankCorrection = [
    { id: 'none', label: 'none' },
    { id: 'max', label: 'max(Blank)' },
    { id: 'min', label: 'min(Blank)' },
    { id: 'mean', label: 'mean(Blank)' }
] as const;

export const mtpEnteryMode = [
    { id: 0, label: 'continuous' },
    { id: 1, label: 'step' }
] as const;

export type PositiveJudge = '>' | '>=' | '<=' | '<';
export const projectPositiveJudge = ['>', '>=', '<=', '<'] as const;

/*------------------------------------项目管理-----------------------------------------------*/
/**
 * 项目类型定义
 * 扁平化设计，程序中使用时直接访问属性
 */

// 基础类型定义

export type ResultShow = 0 | 1 | 2;

export type EnteryMode = 0 | 1;

export type resultShowTxt = '' | '阳性' | '阴性' | '灰区' | 'positive' | 'negative' | 'gray' | '+' | '-' | '(+)';
export const positiveResultShowTxt = ['阳性', 'positive', '+'] as const;
export const negativeResultShowTxt = ['阴性', 'negative', '-'] as const;
export const grayResultShowTxt = ['灰区', 'gray', '(+)'] as const;

// 主项目接口 - 扁平化设计
export interface Project {
    id: string;
    name: string;
    code: string;
    version: number;

    // 结果显示设置
    resultShow: ResultShow; // 0=数值结果 1=文本结果 2=阴阳性结果
    resultUnit: string; // 结果单位
    refRangeText: string; // 参考范围显示文本

    // 测试参数
    testType: TestType; // 0=定性 1=定量
    // testTypeName: string; // 定性/定量

    testWaveIdx: number; // 检测波长滤光片编号 (0-8)
    testWaveName: string; // 检测波长滤光片名称

    refWaveIdx: number; // 参考波长滤光片编号 (0-8)
    refWaveName: string; // 参考波长滤光片名称

    useBlankCorrection: BlankCorrection; // 空白校正 0=不使用 1=max 2=min 3=mean
    enteryMode: EnteryMode; // 进板模式 0=连续 1=步进
    shakeTime: number; // 振板时间(秒)

    // 参考范围
    refRangeDown: number; // 参考范围下限
    refRangeUp: number; // 参考范围上限

    // 阴性对照范围
    ncRangeDown: number; // 阴性对照范围下限
    ncRangeUp: number; // 阴性对照范围上限

    // 阳性对照范围
    pcRangeDown: number; // 阳性对照范围下限
    pcRangeUp: number; // 阳性对照范围上限

    // 灰区范围
    grayRangeDown: number; // 灰区范围下限
    grayRangeUp: number; // 灰区范围上限

    // 其他参数
    cutOffFormula: string; // Cutoff计算公式
    postiveJudge: PositiveJudge; // 阳性公式类型 >,>=, =<,<
    grayEnble: boolean; // 是否启用灰区
    positiveShowTxt: string; // 阳性显示文本
    negativeShowTxt: string; // 阴性显示文本
    grayShowTxt: string; // 灰区显示文本

    // 定量参数
    quantitativeMethod: string; // 定量方法
    quantitativeFormula: string; // 定量公式
    quantitativexAxis: AxisType; // 定量x轴类型 0=Linear 1=Log 2=Ln
    quantitativeyAxis: AxisType; // 定量y轴类型 0=Linear 1=Log 2=Ln
    stdConcentration: [string, number][]; // 标准品浓度数组

    // 系统字段
    createdAt: Date;
    updatedAt: Date;
}

// 数据库记录接口（用于序列化）
export interface ProjectDbRecord {
    id: string;
    name: string;
    code: string;
    version: number;
    infoJson: string; // JSON字符串
    createdAt: Date;
    updatedAt: Date;
}

// 创建项目输入类型
export type ProjectCreate = {
    name: string;
    code: string;
    resultShow: ResultShow;
    resultUnit: string;
    refRangeText: string;
    testType: TestType;
    // testTypeName: string;
    testWaveIdx: number;
    testWaveName: string;
    refWaveIdx: number;
    refWaveName: string;
    useBlankCorrection: BlankCorrection;
    enteryMode: EnteryMode;
    shakeTime: number;
    refRangeDown: number;
    refRangeUp: number;
    ncRangeDown: number;
    ncRangeUp: number;
    pcRangeDown: number;
    pcRangeUp: number;
    grayRangeDown: number;
    grayRangeUp: number;
    cutOffFormula: string;
    postiveJudge: PositiveJudge;
    grayEnble: boolean;
    positiveShowTxt: string;
    negativeShowTxt: string;
    grayShowTxt: string;
    quantitativeMethod: string;
    quantitativeFormula: string;
    quantitativexAxis: AxisType;
    quantitativeyAxis: AxisType;
    stdConcentration: [string, number][];
};

// 更新项目输入类型
export type ProjectUpdate = Partial<Omit<Project, 'id' | 'createdAt' | 'updatedAt'>> & {
    id: string;
};

/**
 * 项目工具类 - 处理数据库序列化/反序列化
 */
export class ProjectUtils {
    /**
     * 将项目对象序列化为数据库记录
     */
    static toDbRecord(project: Project): ProjectDbRecord {
        // 提取需要序列化的字段
        const infoFields = {
            resultShow: project.resultShow,
            resultUnit: project.resultUnit,
            refRangeText: project.refRangeText,
            testType: project.testType,
            // testTypeName: project.testTypeName,
            testWaveIdx: project.testWaveIdx,
            testWaveName: project.testWaveName,
            refWaveIdx: project.refWaveIdx,
            refWaveName: project.refWaveName,
            useBlankCorrection: project.useBlankCorrection,
            enteryMode: project.enteryMode,
            shakeTime: project.shakeTime,
            refRangeDown: project.refRangeDown,
            refRangeUp: project.refRangeUp,
            ncRangeDown: project.ncRangeDown,
            ncRangeUp: project.ncRangeUp,
            pcRangeDown: project.pcRangeDown,
            pcRangeUp: project.pcRangeUp,
            grayRangeDown: project.grayRangeDown,
            grayRangeUp: project.grayRangeUp,
            cutOffFormula: project.cutOffFormula,
            postiveJudge: project.postiveJudge,
            grayEnble: project.grayEnble,
            positiveShowTxt: project.positiveShowTxt,
            negativeShowTxt: project.negativeShowTxt,
            grayShowTxt: project.grayShowTxt,
            quantitativeMethod: project.quantitativeMethod,
            quantitativeFormula: project.quantitativeFormula,
            quantitativexAxis: project.quantitativexAxis,
            quantitativeyAxis: project.quantitativeyAxis,
            stdConcentration: project.stdConcentration
        };

        return {
            id: project.id,
            name: project.name,
            code: project.code,
            version: project.version,
            infoJson: JSON.stringify(infoFields),
            createdAt: project.createdAt,
            updatedAt: project.updatedAt
        };
    }

    /**
     * 从数据库记录反序列化为项目对象
     */
    static fromDbRecord(dbRecord: ProjectDbRecord): Project {
        const info = JSON.parse(dbRecord.infoJson);

        return {
            id: dbRecord.id,
            name: dbRecord.name,
            code: dbRecord.code,
            version: dbRecord.version,
            ...info, // 展开所有info字段
            createdAt: dbRecord.createdAt,
            updatedAt: dbRecord.updatedAt
        };
    }

    /**
     * 创建默认项目对象
     */
    static createDefault(): Omit<Project, 'id' | 'createdAt' | 'updatedAt'> {
        return {
            name: '',
            code: '',
            version: 1,
            resultShow: 0,
            resultUnit: 'OD',
            refRangeText: '0~5',
            testType: 'x',
            // testTypeName: '定性',
            testWaveIdx: 0,
            testWaveName: '450nm',
            refWaveIdx: 0,
            refWaveName: '',
            useBlankCorrection: 'none',
            enteryMode: 0,
            shakeTime: 0,
            refRangeDown: 0,
            refRangeUp: 5,
            ncRangeDown: 0,
            ncRangeUp: 5,
            pcRangeDown: 0,
            pcRangeUp: 5,
            grayRangeDown: 0,
            grayRangeUp: 5,
            cutOffFormula: '',
            postiveJudge: '>=',
            grayEnble: false,
            positiveShowTxt: '阳性',
            negativeShowTxt: '阴性',
            grayShowTxt: '灰区',
            quantitativeMethod: 'LINEAR',
            quantitativeFormula: '',
            quantitativexAxis: 0,
            quantitativeyAxis: 0,
            stdConcentration: Array.from({ length: 10 }, (_, i) => [`STD${i + 1}`, 0] as [string, number])
        };
    }
}
