{
  "extends": "@electron-toolkit/tsconfig/tsconfig.node.json",
  "include": [
    "electron.vite.config.*",
    "src/main/**/*",
    "src/preload/*",
    "src/shared/**/*",
    "src/types/*"
  ],
  "compilerOptions": {
    "strict": true,
    // "noImplicitAny": false,
    "esModuleInterop": true,
    "moduleResolution": "node",
    "composite": false,
    "types": ["electron-vite/node","jest"],
    "baseUrl": ".",
    "paths": {
      "@main/*": ["./src/main/*"],
      "@preload/*": ["./src/preload/*"],
      "@shared/*": ["./src/shared/*"]
    }
  }
}