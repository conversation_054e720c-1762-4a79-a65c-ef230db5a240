{
  "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
  "include": [
     "electron.vite.config.*",
    "src/renderer/src/**/*",
    "src/renderer/src/utils/**/*",
    "src/renderer/src/__tests__/**/*",
    "src/types/**/*",
    "src/preload/*.d.ts",
    "src/shared/**/*",
    "src/**/*"
  ],
  "compilerOptions": {
    "jsx": "react-jsx",
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowJs": true,
    "skipLibCheck": true,
    "noImplicitAny": true,
    "sourceMap": true,
    "outDir": "dist",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "composite": true,
    "strict": true,    
    "types": ["jest","electron-vite/node"],
    // "noImplicitAny": false,
    "baseUrl": ".",
    "paths": {
            "@renderer/*": ["src/renderer/src/*"],
            "@shared/*": ["src/shared/*"],
            "@preload/*": ["src/preload/*"],
            "@components/*": ["src/renderer/src/components/*"],
            "@pages/*": ["src/renderer/src/pages/*"],
            "@icons/*": ["src/renderer/src/icons/*"],
            "@locales/*": ["src/renderer/src/locales/*"],
 
    }
  }
}
